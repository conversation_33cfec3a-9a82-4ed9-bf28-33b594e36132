<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <assignments>
        <name>Add_competitor_name_to_list</name>
        <label>Add competitor name to list</label>
        <locationX>138</locationX>
        <locationY>974</locationY>
        <assignmentItems>
            <assignToReference>competitorNamesText</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Iterate_over_competitors.Name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>competitorNamesText</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>, </stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Iterate_over_competitors</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_competitor_to_update_list</name>
        <label>Add competitor to update list</label>
        <locationX>138</locationX>
        <locationY>1490</locationY>
        <assignmentItems>
            <assignToReference>competitiorsForUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>competitor</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Iterate_over_updatable_competitors</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Collect_Participant_Contact_Id</name>
        <label>Collect Participant Contact Id</label>
        <locationX>270</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>contactIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Iterate_over_Conference_Participants.Contact__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Iterate_over_Conference_Participants</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Mark_competitor_for_disqualification</name>
        <label>Mark competitor for disqualification</label>
        <locationX>138</locationX>
        <locationY>1382</locationY>
        <assignmentItems>
            <assignToReference>competitor.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Iterate_over_updatable_competitors.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>competitor.IsDisqualified__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>competitor.sc_IsNonAttendee__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_competitor_to_update_list</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Is_any_competitors_found</name>
        <label>Is any competitors found</label>
        <locationX>182</locationX>
        <locationY>758</locationY>
        <defaultConnector>
            <targetReference>No_Competitors_Found_Screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>competitors_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Conference_Participants</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Iterate_over_competitors</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Records_Selected</name>
        <label>Is Records Selected</label>
        <locationX>380</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>NoRecordsSelectedScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>rescordsSelected_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ids</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Conference_Participants</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>competitorNamesTextTrimmed</name>
        <dataType>String</dataType>
        <expression>LEFT({!competitorNamesText}, LEN({!competitorNamesText}) - 2)</expression>
    </formulas>
    <interviewLabel>[SC] Mark Conference Non-Attendees {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[SC] Mark Conference Non-Attendees</label>
    <loops>
        <name>Iterate_over_competitors</name>
        <label>Iterate over competitors</label>
        <locationX>50</locationX>
        <locationY>866</locationY>
        <collectionReference>Get_Conference_Participants</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_competitor_name_to_list</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>ConfirmationScreen</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Iterate_over_Conference_Participants</name>
        <label>Iterate over Conference Participants</label>
        <locationX>182</locationX>
        <locationY>350</locationY>
        <collectionReference>Get_Conference_Participants</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Collect_Participant_Contact_Id</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_Competitors</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Iterate_over_updatable_competitors</name>
        <label>Iterate over updatable competitors</label>
        <locationX>50</locationX>
        <locationY>1274</locationY>
        <collectionReference>Get_Competitors</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Mark_competitor_for_disqualification</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_competitors</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Competitors</name>
        <label>Get Competitors</label>
        <locationX>182</locationX>
        <locationY>650</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_any_competitors_found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Participant__c</field>
            <operator>In</operator>
            <value>
                <elementReference>contactIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>cmp_Participant__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Conference_Participants</name>
        <label>Get Conference Participants</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Iterate_over_Conference_Participants</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>ids</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>cm_Participant__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_competitors</name>
        <label>Update competitors</label>
        <locationX>50</locationX>
        <locationY>1682</locationY>
        <connector>
            <targetReference>CompletionScreen</targetReference>
        </connector>
        <inputReference>competitiorsForUpdate</inputReference>
    </recordUpdates>
    <screens>
        <name>CompletionScreen</name>
        <label>Completion Screen</label>
        <locationX>50</locationX>
        <locationY>1790</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>CompletionText</name>
            <fieldText
            >&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Selected participants had been marked as non-attendees and disqualified&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>ConfirmationScreen</name>
        <label>Confirmation Screen</label>
        <locationX>50</locationX>
        <locationY>1166</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Iterate_over_updatable_competitors</targetReference>
        </connector>
        <fields>
            <name>ConfirmationMessage</name>
            <fieldText
            >&lt;p&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Please confirm your action&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;You are about to disqualify following participants: {!competitorNamesTextTrimmed}.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>No_Competitors_Found_Screen</name>
        <label>No Competitors Found Screen</label>
        <locationX>314</locationX>
        <locationY>866</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>NoCompetitorsMessage</name>
            <fieldText
            >&lt;p&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Competitors already disqualified&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Looks like all of selected competitors already disqualified or not participating in any competitions. No records will be updated. Click Finish to return to the previous page.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>NoRecordsSelectedScreen</name>
        <label>No Records Selected Screen</label>
        <locationX>578</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>noParticipantsSelectedMessage</name>
            <fieldText
            >&lt;p&gt;You didn&apos;t select any competitors. Please, mark checkboxes next to participants you want select as non-attendees.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>254</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Is_Records_Selected</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>competitiorsForUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>cmp_Participant__c</objectType>
    </variables>
    <variables>
        <name>competitor</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>cmp_Participant__c</objectType>
    </variables>
    <variables>
        <name>competitorNamesText</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>contactIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ids</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
