public with sharing class unm_cmParticipant<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {

    /************************************************************
     * PUBLIC ENTRY POINT
     ************************************************************/
    public static void handleParticipantStatusChanges(
            List<cm_Participant__c> newList,
            Map<Id, cm_Participant__c> newMap,
            List<cm_Participant__c> oldList,
            Map<Id, cm_Participant__c> oldMap,
            Boolean isInsert,
            Boolean isUpdate,
            Boolean isDelete,
            Boolean isUndelete
    ) {
        // 1) Identify participants that got newly Approved or no-longer Approved
        Map<String, Set<Id>> approvalMap = identifyApprovalChanges(
                newList, newMap, old<PERSON>ist, old<PERSON><PERSON>,
                isD<PERSON><PERSON>, isUpdate, isUndelete
        );

        Set<Id> newlyApprovedIds = approvalMap.get('newlyApproved');
        Set<Id> noLongerApprovedIds = approvalMap.get('noLongerApproved');

        // If nothing changed, exit
        if ((newlyApprovedIds == null || newlyApprovedIds.isEmpty()) &&
                (noLongerApprovedIds == null || noLongerApprovedIds.isEmpty())) {
            return;
        }

        // 2) For newly approved participants, create as_Result__c records
        if (!newlyApprovedIds.isEmpty()) {
            createAssessmentResults(newlyApprovedIds);
        }

        // 3) For participants no longer approved, remove relevant as_Result__c
        if (!noLongerApprovedIds.isEmpty()) {
            removeAssessmentResults(noLongerApprovedIds, oldList, isDelete);
        }
    }

    /************************************************************
     * STEP 1: IDENTIFY WHICH RECORDS ARE NEWLY APPROVED / NO LONGER APPROVED
     ************************************************************/
    private static Map<String, Set<Id>> identifyApprovalChanges(
            List<cm_Participant__c> newList,
            Map<Id, cm_Participant__c> newMap,
            List<cm_Participant__c> oldList,
            Map<Id, cm_Participant__c> oldMap,
            Boolean isDelete,
            Boolean isUpdate,
            Boolean isUndelete
    ) {
        Set<Id> newlyApproved    = new Set<Id>();
        Set<Id> noLongerApproved = new Set<Id>();

        // If not a delete, check newList for changes
        if (!isDelete) {
            for (cm_Participant__c newRec : newList) {
                cm_Participant__c oldRec = (isUpdate || isUndelete) && oldMap != null
                        ? oldMap.get(newRec.Id)
                        : null;

                Boolean newIsApproved = (newRec.RegistrationStatus__c == 'Approved');
                Boolean oldIsApproved = (oldRec != null && oldRec.RegistrationStatus__c == 'Approved');

                if (newIsApproved && !oldIsApproved) {
                    newlyApproved.add(newRec.Id);
                }
                if (!newIsApproved && oldIsApproved) {
                    noLongerApproved.add(newRec.Id);
                }
            }
        }

        // If records are being deleted, check oldList
        if (isDelete) {
            for (cm_Participant__c oldRec : oldList) {
                if (oldRec.RegistrationStatus__c == 'Approved') {
                    noLongerApproved.add(oldRec.Id);
                }
            }
        }

        return new Map<String, Set<Id>> {
                'newlyApproved'    => newlyApproved,
                'noLongerApproved' => noLongerApproved
        };
    }

    /************************************************************
     * STEP 2: CREATE as_Result__c FOR NEWLY APPROVED PARTICIPANTS
     *         ONLY IF RegistrationType__r.IsCompetitionsSelectionAvailable__c = TRUE
     ************************************************************/
    private static void createAssessmentResults(Set<Id> newlyApprovedIds) {
        List<cm_Participant__c> newlyApprovedList = [
                SELECT Id,
                        Contact__c,
                        Conference__c,
                        Conference__r.EmployabilityAssessment__c
                FROM cm_Participant__c
                WHERE Id IN :newlyApprovedIds
                AND Contact__c != NULL
                AND Conference__c != NULL
                AND ConferenceRegistrationType__r.IsCompetitionsSelectionAvailable__c = TRUE
                AND ConferenceRegistrationType__r.IsActive__c = TRUE
        ];
        if (newlyApprovedList.isEmpty()) {
            return;
        }

        // Collect relevant Contact IDs
        Set<Id> contactIds = new Set<Id>();
        for (cm_Participant__c p : newlyApprovedList) {
            contactIds.add(p.Contact__c);
        }

        // Map contacts to users
        Map<Id, Id> contactToUserId = new Map<Id, Id>();
        if (!contactIds.isEmpty()) {
            for (User u : [
                    SELECT Id, ContactId
                    FROM User
                    WHERE ContactId IN :contactIds
            ]) {
                contactToUserId.put(u.ContactId, u.Id);
            }
        }

        // Collect userIds & assessmentIds for searching existing as_Result__c
        Set<Id> userIds       = new Set<Id>();
        Set<Id> assessmentIds = new Set<Id>();

        for (cm_Participant__c p : newlyApprovedList) {
            Id userId = contactToUserId.get(p.Contact__c);
            if (userId != null) {
                userIds.add(userId);
            }
            if (p.Conference__r != null &&
                    p.Conference__r.EmployabilityAssessment__c != null) {
                assessmentIds.add(p.Conference__r.EmployabilityAssessment__c);
            }
        }

        // Build a set of existing records: Participant__c + ':' + Assessment__c
        Set<String> existingKeys = new Set<String>();

        if (!userIds.isEmpty() && !assessmentIds.isEmpty()) {
            for (as_Result__c existingResult : [
                    SELECT Id, Participant__c, Assessment__c
                    FROM as_Result__c
                    WHERE Participant__c IN :userIds
                    AND Assessment__c IN :assessmentIds
            ]) {
                String key = existingResult.Participant__c + ':' + existingResult.Assessment__c;
                existingKeys.add(key);
            }
        }

        // Prepare new as_Result__c records
        List<as_Result__c> resultsToInsert = new List<as_Result__c>();

        for (cm_Participant__c p : newlyApprovedList) {
            if (p.Conference__r == null ||
                    p.Conference__r.EmployabilityAssessment__c == null) {
                continue; // Must have an EmployabilityAssessment__c
            }
            Id userId = contactToUserId.get(p.Contact__c);
            if (userId == null) {
                continue;
            }
            String key = userId + ':' + p.Conference__r.EmployabilityAssessment__c;
            if (!existingKeys.contains(key)) {
                resultsToInsert.add(new as_Result__c(
                        Assessment__c  = p.Conference__r.EmployabilityAssessment__c,
                        Participant__c = userId,
                        Contact__c = p.Contact__c
                ));
                existingKeys.add(key);
            }
        }

        // Insert new as_Result__c
        insert resultsToInsert;
    }

    /************************************************************
     * STEP 3: REMOVE as_Result__c FOR NO-LONGER APPROVED PARTICIPANTS
     *         ONLY IF RegistrationType__r.IsCompetitionsSelectionAvailable__c = TRUE
     ************************************************************/
    private static void removeAssessmentResults(Set<Id> noLongerApprovedIds, List<cm_Participant__c> oldList, Boolean isDelete) {
        // Query participants in DB whose IsCompetitionsSelectionAvailable__c = TRUE
        List<cm_Participant__c> noLongerApprovedList = [
                SELECT Id,
                        Contact__c,
                        Conference__r.EmployabilityAssessment__c
                FROM cm_Participant__c
                WHERE Id IN :noLongerApprovedIds
                AND ConferenceRegistrationType__r.IsCompetitionsSelectionAvailable__c = TRUE
                ALL ROWS
        ];

        // If records are physically deleted, we rely on the oldList to glean prior data
        if (isDelete) {
            for (cm_Participant__c oldRec : oldList) {
                if (noLongerApprovedIds.contains(oldRec.Id)) {
                    // If you need to check if oldRec's RegistrationType__r was true
                    // you'd have to query the oldRec fully or store old values separately.
                    noLongerApprovedList.add(oldRec);
                }
            }
        }

        if (noLongerApprovedList.isEmpty()) {
            return;
        }

        Set<Id> contactIdsToRemove = new Set<Id>();
        Set<Id> conferenceIds  = new Set<Id>();
        Set<Id> assessmentIds  = new Set<Id>();

        for (cm_Participant__c p : noLongerApprovedList) {
            if (p.Contact__c != null) {
                contactIdsToRemove.add(p.Contact__c);
            }
            if (p.Conference__c != null) {
                conferenceIds.add(p.Conference__c);
            }
            if (p.Conference__r?.EmployabilityAssessment__c != null) {
                assessmentIds.add(p.Conference__r.EmployabilityAssessment__c);
            }
        }

        if (contactIdsToRemove.isEmpty() || conferenceIds.isEmpty()) {
            return;
        }

        Set<Id> removeUserIds = new Set<Id>();
        Set<Id> removeContactIds = new Set<Id>();
        for (User u : [SELECT Id, ContactId FROM User WHERE ContactId IN :contactIdsToRemove]) {
            removeUserIds.add(u.Id);
            removeContactIds.add(u.ContactId);
        }

        if (removeUserIds.isEmpty()) {
            return;
        }

        // Query as_Result__c to delete
        List<as_Result__c> resultsToDelete = [
                SELECT Id, Participant__c, Assessment__c
                FROM as_Result__c
                WHERE Participant__c IN :removeUserIds
                AND (cmp_Competition__r.Conference__c IN :conferenceIds OR Assessment__c IN :assessmentIds)
        ];

        WS.deleteRecords(resultsToDelete);

        List<as_ResultGroup__c> groups = new WSHelper().getGroups(removeContactIds, conferenceIds);

        // 4) Determine which groups no longer have any references
        List<as_ResultGroup__c> groupsToDelete = new List<as_ResultGroup__c>();
        for (as_ResultGroup__c theGroup : groups) {
            // If all three references are null, remove that group
            if (theGroup.ResultTechnical__c == null && theGroup.ResultReadiness__c == null && theGroup.ResultEmployability__c == null) {
                groupsToDelete.add(theGroup);
            }
        }

        WS.deleteRecords(groupsToDelete);
    }

    without sharing class WSHelper {
        public List<as_ResultGroup__c> getGroups(Set<Id> removeContactIds, Set<Id> conferenceIds) {
            return [
                    SELECT Id,
                            Contact__c,
                            cmp_Competition__c,
                            ResultTechnical__c,
                            ResultReadiness__c,
                            ResultEmployability__c
                    FROM as_ResultGroup__c
                    WHERE Contact__c IN :removeContactIds
                    AND cmp_Competition__r.Conference__c IN :conferenceIds
            ];
        }

    }
}
