<?xml version="1.0" encoding="UTF-8" ?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>HotelTetrisResultGroup__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CheckInDate__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CheckOutDate__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Account__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HotelRequestRoom__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Hotel__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfGuests__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>HotelRoomType__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>RoomType__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>RoomSubtype__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Guest 1</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1FirstName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1LastName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1Position__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1Company__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1Email__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1Phone__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1Country__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1State__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1City__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1Address1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1Address2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest1PostalCode__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Guest 1 Displayed To Hotel Manager</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalEmail__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalPhone__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalCountry__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalState__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalCity__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalAddress1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalAddress2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalPostalCode__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Guest 2</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest2FirstName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest2LastName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest2Position__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns />
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Guest 3</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest3FirstName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest3LastName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest3Position__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns />
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Guest 4</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest4FirstName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest4LastName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Guest4Position__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns />
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Custom Links</label>
        <layoutColumns />
        <layoutColumns />
        <layoutColumns />
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hRK000002hEjN</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
