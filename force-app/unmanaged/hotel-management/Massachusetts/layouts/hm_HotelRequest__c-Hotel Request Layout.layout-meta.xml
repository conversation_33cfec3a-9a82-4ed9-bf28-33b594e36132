<?xml version="1.0" encoding="UTF-8" ?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EventId__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CheckInDate__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CheckOutDate__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EnforcedHotel__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Account__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ReadOnly__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsHandicapParkingNeeded__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Custom Links</label>
        <layoutColumns />
        <layoutColumns />
        <layoutColumns />
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Hotel__c</fields>
        <fields>PriorityOrder__c</fields>
        <fields>CREATEDBY_USER</fields>
        <fields>CREATED_DATE</fields>
        <relatedList>hm_HotelRequestPreferredHotel__c.HotelRequest__c</relatedList>
        <sortField>PriorityOrder__c</sortField>
        <sortOrder>Asc</sortOrder>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Type__c</fields>
        <fields>CheckInDate__c</fields>
        <fields>CheckOutDate__c</fields>
        <relatedList>hm_HotelRequestRoom__c.HotelRequest__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>FirstName__c</fields>
        <fields>LastName__c</fields>
        <fields>Email__c</fields>
        <fields>Position__c</fields>
        <relatedList>hm_HotelRequestVisitor__c.HotelRequest__c</relatedList>
        <sortField>LastName__c</sortField>
        <sortOrder>Asc</sortOrder>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hOy000000obAw</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
