<?xml version="1.0" encoding="UTF-8" ?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsAvailable__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AdditionalInfo__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Reservation capacity</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfAvailableGuests__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfSingleRooms__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfSofaSleeperSingleRooms__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfTraditionalSingleRooms__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HotelFullyReserved__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfDoubleRooms__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfSofaSleeperDoubleRooms__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>NumberOfTradtionalDoubleRooms__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Location</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Address__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>MapLink__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Geolocation__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsInWalkingDistanceFromEvent__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DistanceToConferenceHall__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Contact Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Email__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Site__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PointOfContact__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Custom Links</label>
        <layoutColumns />
        <layoutColumns />
        <layoutColumns />
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Type__c</fields>
        <fields>Subtype__c</fields>
        <fields>NumberOfRooms__c</fields>
        <relatedList>hm_HotelRoomType__c.Hotel__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hRu000000oOti</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
