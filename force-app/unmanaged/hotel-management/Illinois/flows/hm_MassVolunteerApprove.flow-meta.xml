<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <assignments>
        <name>Add_contact_to_update_list</name>
        <label>Add contact to update list</label>
        <locationX>264</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>contactsForUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>contact</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Iterate_over_submitted_contacts</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Mark_Volunteer_as_Approved</name>
        <label>Mark Volunteer as Approved</label>
        <locationX>264</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>contact.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Iterate_over_submitted_contacts.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>contact.hm_IsVolunteerApproved__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_contact_to_update_list</targetReference>
        </connector>
    </assignments>
    <environments>Default</environments>
    <interviewLabel>[HM] Mass Volunteer Approve {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[HM] Mass Volunteer Approve</label>
    <loops>
        <name>Iterate_over_submitted_contacts</name>
        <label>Iterate over submitted contacts</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <collectionReference>Get_Contacts</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Mark_Volunteer_as_Approved</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Contacts_in_DB</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Contacts</name>
        <label>Get Contacts</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Iterate_over_submitted_contacts</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>ids</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Contacts_in_DB</name>
        <label>Update Contacts in DB</label>
        <locationX>176</locationX>
        <locationY>758</locationY>
        <inputReference>contactsForUpdate</inputReference>
    </recordUpdates>
    <screens>
        <name>ApproveVolunteersConfirm</name>
        <label>Approve Volunteers Confirmation</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Get_Contacts</targetReference>
        </connector>
        <fields>
            <name>ApproveConfirmationText</name>
            <fieldText
            >&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Are you sure you want to approve selected volunteers?&lt;/strong&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Click Confirm to proceed or return to the previous page to cancel&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>ApproveVolunteersConfirm</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>contact</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>contactsForUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Contact</objectType>
    </variables>
    <variables>
        <name>ids</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
