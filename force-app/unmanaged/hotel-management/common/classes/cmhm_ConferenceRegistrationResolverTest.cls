@IsTest
class cmhm_ConferenceRegistrationResolverTest {
    static final String PARTICIPANT_EMAIL = '<EMAIL>';
    static final String NON_PARTICIPANT_EMAIL = '<EMAIL>';
    public static final String CM_ADMIN_PS_NAME = 'CM_AdminPermissions';
    static final String APPROVED_PARTICIPANT_STATUS = 'Approved';

    @TestSetup
    static void setup() {
        User adminUser = hm_TestDataFactory.createAdminUser();
        insert adminUser;
        hm_TestDataFactory.assignPermissionSet(adminUser.Id, CM_ADMIN_PS_NAME);

        System.runAs(adminUser) {
            cm_Conference__c conference = new cm_Conference__c(
                Name = 'Test Conference',
                IsCompetitionsAvailable__c = true
            );
            insert conference;

            Account schoolAccount = new Account(Name = 'Last Household');
            insert schoolAccount;

            Contact participantContact = new Contact(
                FirstName = 'Conference',
                LastName = 'Participant',
                AccountId = schoolAccount.Id,
                Email = PARTICIPANT_EMAIL,
                PortalRole__c = 'Judge;Technical Chair;Lead Advisor'
            );
            Contact nonparticipantContact = new Contact(
                FirstName = 'School',
                LastName = 'Contact',
                AccountId = schoolAccount.Id,
                Email = NON_PARTICIPANT_EMAIL
            );
            insert new List<Contact>{ participantContact, nonparticipantContact };

            cm_Participant__c participant = new cm_Participant__c(
                Conference__c = conference.Id,
                Contact__c = participantContact.Id,
                RegistrationStatus__c = APPROVED_PARTICIPANT_STATUS
            );
            insert participant;
        }

    }

    @IsTest
    static void isContactRegisteredForEventPositive() {
        Contact participantContact = [SELECT PortalRole__c FROM Contact WHERE Email = :PARTICIPANT_EMAIL];
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c LIMIT 1];
        Boolean isParticipant = false;

        Test.startTest();
        System.runAs(hm_TestDataFactory.getAdminUser()) {
            isParticipant = new cmhm_ConferenceRegistrationResolver()
                .isContactRegisteredForEvent(conference.Id, participantContact);
        }
        Test.stopTest();

        Assert.isTrue(
            isParticipant,
            'isContactRegisteredForEvent() should return true if specified contact has cm_Participant__c for specified conference'
        );
    }

    @IsTest
    static void isContactRegisteredForEventNegative() {
        Contact nonparticipantContact = [SELECT PortalRole__c FROM Contact WHERE Email = :NON_PARTICIPANT_EMAIL];
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c LIMIT 1];
        Boolean isParticipant = true;

        Test.startTest();
        System.runAs(hm_TestDataFactory.getAdminUser()) {
            isParticipant = new cmhm_ConferenceRegistrationResolver()
                .isContactRegisteredForEvent(conference.Id, nonparticipantContact);
        }
        Test.stopTest();

        Assert.isFalse(
            isParticipant,
            'isContactRegisteredForEvent() should not return true if contact not registered to conference'
        );
    }
}