public with sharing class cmhm_HotelRequestReminderSchedulable implements System.Schedulable {
    private static final String LEAD_ADVISOR_ROLE = PortalUser.ROLES.get(PortalUser.Role.LEAD_ADVISOR);
    private static final String ACTIVE_HM_CONF_ID = hm_HotelManagementSettings__c.getInstance().ActiveEventId__c;
    private static final String EMAIL_TEMPLATE_NAME = 'cmhm_HotelRegistrationReminder';
    static final String APPROVED_PARTICIPANT_STATUS = 'Approved';

    public void execute(System.SchedulableContext sc) {
        if (ACTIVE_HM_CONF_ID == null) {
            return;
        }
        Map<Id, Contact> idToNotifiedContact = new Map<Id, Contact>();
        List<Contact> leadAdvisorContacts = queryLeadAdvisors();
        List<Contact> notifiedContacts = filterContactsWithoutHotelRequest(leadAdvisorContacts);
        sendEmails(notifiedContacts);
    }

    public List<Contact> queryLeadAdvisors() {
        return [
            SELECT AccountId
            FROM Contact
            WHERE
                PortalRole__c = :LEAD_ADVISOR_ROLE
                AND Account.hm_IsExemptedFromHotelAssignment__c = FALSE
                AND Id IN (
                    SELECT Contact__c
                    FROM cm_Participant__c
                    WHERE
                        Conference__c = :ACTIVE_HM_CONF_ID
                        AND RegistrationStatus__c = :APPROVED_PARTICIPANT_STATUS
                        AND Conference__r.StartDate__c <= TODAY
                        AND Conference__r.RegistrationDeadline__c >= TODAY
                )
        ];
    }

    public List<Contact> filterContactsWithoutHotelRequest(List<Contact> contacts) {
        Set<Id> accountIds = new Set<Id>();
        List<Contact> nonRegisteredContacts = new List<Contact>();

        for (Contact participantContact : contacts) {
            accountIds.add(participantContact.AccountId);
        }

        List<AggregateResult> hotelRequestAgg = [
            SELECT Account__c, COUNT(Id)
            FROM hm_HotelRequest__c
            WHERE Account__c IN :accountIds AND EventId__c = :ACTIVE_HM_CONF_ID
            WITH USER_MODE
            GROUP BY Account__c
        ];
        Set<Id> hotelRegisteredAccountIds = new Set<Id>();

        for (AggregateResult aggRes : hotelRequestAgg) {
            hotelRegisteredAccountIds.add((Id) aggRes.get('Account__c'));
        }

        for (Contact participantContact : contacts) {
            if (!hotelRegisteredAccountIds.contains(participantContact.AccountId)) {
                nonRegisteredContacts.add(participantContact);
            }
        }

        return nonRegisteredContacts;
    }

    private void sendEmails(List<Contact> contacts) {
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        hm_HotelManagementSettings__c settings = hm_HotelManagementSettings__c.getInstance();
        EmailTemplate template = [SELECT Id FROM EmailTemplate WHERE DeveloperName = :EMAIL_TEMPLATE_NAME];

        for (Contact notifiedContact : contacts) {
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setTemplateId(template.Id);
            email.setTargetObjectId(notifiedContact.Id);
            if (String.isNotBlank(settings.OrgWideEmailId__c)) {
                email.setOrgWideEmailAddressId(settings.OrgWideEmailId__c);
            }
            emails.add(email);
        }

        if (!Test.isRunningTest()) {
            Messaging.sendEmail(emails, false);
        }
    }
}