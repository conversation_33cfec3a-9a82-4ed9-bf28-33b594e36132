<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OrganizationName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>IndustryClusters__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Email__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Phone__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OnSiteContactLastName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OnSiteContactFirstName__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Conference__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Account__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccessCode__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SecondNameBadgeFirstName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SecondNameBadgeLastName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AdditionalRequest__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Address Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>AddressState__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>AddressCity__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>AddressPostalCode__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>AddressStreetLineOne__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>AddressStreetLineTwo__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Prices Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OrganizationType__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>BoothSize__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>NumberOfAdditionalTables__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>ElectricalDrop__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>NeedPrize__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalPrice__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hOy000005ekEA</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
