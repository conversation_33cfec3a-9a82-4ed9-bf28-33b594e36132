<apex:page
    controller="unm_ReceiptCtrl"
    showHeader="false"
    sidebar="false"
    standardStylesheets="false"
    applyHtmlTag="false"
    renderAs="{!renderAs}"
>

    <head>
        <style>
            html, body {
                font-family: sans-serif;
            }
            table {
                width: 100%;
                text-align: left;
            }
            table.bordered, table.bordered th, table.bordered td {
                border-collapse: collapse;
                padding: 7px;
            }
            table.bordered th, table.bordered td {
                border: 1px solid #000000;
            }
            table.bordered th {
                background: #f0f0f0;
            }
            .border-none {
                border: none !important;
            }
            .text-center {
                text-align: center !important;
            }
            .text-right {
                text-align: right !important;
            }
        </style>
    </head>

    <table>
        <tr>
            <td>
                {!company.name} <br/>
                {!company.street} <br/>
                {!company.city}, {!company.stateOrProvince} {!company.zipOrPostalCode} <br/>
                {!company.phone} <br/>
                {!company.email}
            </td>
            <td class="text-right">
                <apex:image url="{!company.logoUrl}" width="200" alt="Logo"></apex:image>
            </td>
        </tr>
        <tr>
            <td colspan="2" class="text-center">
                <h2>RECEIPT</h2>
                <hr/>
            </td>
        </tr>
        <tr>
            <td>
                Receipt # <br/>
                <apex:outputText value="{!receipt.receiptName}" />
                <br/>
                <br/>

                Date <br/>
                <apex:outputText value="{0,date,dd MMM yyyy hh:mm a}">
                    <apex:param value="{!receipt.issueDate}" />
                </apex:outputText>
            </td>
            <td class="text-right">
                Payer <br/>
                <apex:outputText value="{!receipt.payerName}" />
                <br/>
                <apex:outputText value="{!receipt.payerEmail}" />
                <br/>
                <br/>
                Payment method <br/>
                {!receipt.paymentMethodType}-{!receipt.paymentMethodBrand}-{!receipt.paymentMethodLast4}
            </td>
        </tr>
        <tr>
            <td colspan="2">

                <apex:repeat value="{!receipt.orders}" var="order">
                    <div>
                        <b>Order #{!order.name}</b>
                    </div>

                    <table class="bordered">
                        <thead>
                        <tr>
                            <th class="text-right" style="width: 1rem;">#</th>
                            <th>Item</th>
                            <th>Description</th>
                            <th class="text-right" style="width: 8rem;">Unit Price</th>
                            <th class="text-right" style="width: 8rem;">Quantity</th>
                            <th class="text-right" style="width: 8rem;">Discount</th>
                            <th class="text-right" style="width: 8rem;">Total</th>
                        </tr>
                        </thead>
                        <tbody>
                        <apex:variable var="rowIndex" value="{!0}" />
                            <apex:repeat value="{!order.orderItems}" var="item">
                                <apex:variable var="rowIndex" value="{!rowIndex + 1}" />
                                <tr>
                                    <td class="text-right">{!rowIndex}</td>
                                    <td>{!item.productName}</td>
                                    <td>{!item.productDescription}</td>
                                    <td class="text-right">
                                        <apex:outputText value="{0,number,#,##0.00}">
                                            <apex:param value="{!item.productPrice}" />
                                        </apex:outputText>
                                    </td>
                                    <td class="text-right">{!item.quantity}</td>
                                    <td class="text-right">{!item.discount}</td>
                                    <td class="text-right">
                                        <apex:outputText value="{0,number,#,##0.00}">
                                            <apex:param value="{!item.finalPrice}" />
                                        </apex:outputText>
                                    </td>
                                </tr>
                            </apex:repeat>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="5" class="border-none"></td>
                            <th class="text-right">Subtotal</th>
                            <th class="text-right">
                                <apex:outputText value="{0,number,#,##0.00}">
                                    <apex:param value="{!receipt.subTotal}" />
                                </apex:outputText>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="5" class="border-none"></td>
                            <th class="text-right">Order discount</th>
                            <th class="text-right">
                                <apex:outputText value="{0,number,#,##0.00}">
                                    <apex:param value="{!receipt.discount}" />
                                </apex:outputText>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="5" class="border-none"></td>
                            <th class="text-right">Total</th>
                            <th class="text-right">
                                <apex:outputText value="{0,number,#,##0.00}">
                                    <apex:param value="{!receipt.ordersFinalPriceSum}" />
                                </apex:outputText>
                            </th>
                        </tr>
                        <tr>
                            <td colspan="5" class="border-none"></td>
                            <th class="text-right">Paid today</th>
                            <th class="text-right">
                                <apex:outputText value="{0,number,#,##0.00}">
                                    <apex:param value="{!receipt.paidToday}" />
                                </apex:outputText>
                            </th>
                        </tr>
                        </tfoot>
                    </table>

                </apex:repeat>

            </td>
        </tr>
        <tr>
            <td colspan="2">
                {!receiptComment}
            </td>
        </tr>
    </table>

</apex:page>
