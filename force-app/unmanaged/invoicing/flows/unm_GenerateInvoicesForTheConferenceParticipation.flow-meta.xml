<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>GenerateInvoicesForTheConference</name>
        <label>Generate Invoices For The Conference</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <actionName>unm_GenerateConferenceInvoices</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>SuccessScreen</targetReference>
        </connector>
        <faultConnector>
            <targetReference>ErrorMessageScreen</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>ids</name>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>unm_GenerateConferenceInvoices</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <environments>Default</environments>
    <interviewLabel>[UNM] Generate Invoices For The Conference Participation {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[UNM] Generate Invoices For The Conference Participation</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <screens>
        <name>ErrorMessageScreen</name>
        <label>Error Message Screen</label>
        <locationX>440</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ErrorMessage</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(212, 0, 0);&quot;&gt;{!$Flow.FaultMessage}&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>SuccessScreen</name>
        <label>Success Screen</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessMessage</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(10, 153, 2);&quot;&gt;The action successfully completed.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>unm_GenerateInvoicesForTheConferenceParticipation</name>
        <label>Generate Invoices For The Conference Participation</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>GenerateInvoicesForTheConference</targetReference>
        </connector>
        <fields>
            <name>ConfirmationText</name>
            <fieldText>&lt;p&gt;Click the &quot;&lt;strong&gt;Confirm&lt;/strong&gt;&quot; button to create invoices for conference participation. &lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;You can perform this action in case the conference registration deadline has already passed.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>unm_GenerateInvoicesForTheConferenceParticipation</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>cm_Conference__c</objectType>
    </variables>
</Flow>
