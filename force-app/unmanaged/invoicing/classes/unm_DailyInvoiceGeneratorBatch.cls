global with sharing class unm_DailyInvoiceGeneratorBatch implements Database.Batchable<SObject>  {

    // Constants for invoice line name patterns
    private static final String DIRECT_STUDENT_PATTERN = 'Direct Student %';
    private static final String DIRECT_ADVISOR_PATTERN = 'Direct Advisor %';
    private static final String TPP_STUDENT_PATTERN = 'TPP Student %';
    private static final String TPP_ADVISOR_PATTERN = 'TPP Advisor %';
    private static final String TPP_GENERAL_PATTERN = 'TPP (%';

    // Constants for invoice settings
    private static final String AUTO_GENERATED_NOTE = 'Auto Generated';
    private static final String DEFAULT_EMAIL_TEMPLATE = 'DailyInvoiceGenerated';

    // Configuration settings
    private final String emailTemplateName;
    private final Integer batchSize;

    // Default constructor using default settings
    global unm_DailyInvoiceGeneratorBatch() {
        this(getEmailTemplateName(), getBatchSize());
    }

    // Constructor with custom configuration
    global unm_DailyInvoiceGeneratorBatch(String emailTemplateName, Integer batchSize) {
        this.emailTemplateName = emailTemplateName;
        this.batchSize = batchSize;
    }

    // Define the query for the batch
    global Database.QueryLocator start(Database.BatchableContext bc) {
        // Consider to create formula field to determine if invoice line should be auto generated
        return Database.getQueryLocator([
                SELECT Id, Account__c, TotalPrice__c FROM InvoiceLine__c
                WHERE Invoice__c = NULL AND ms_IsCredit__c = FALSE AND Account__c != NULL
                AND (
                        Name LIKE :DIRECT_STUDENT_PATTERN OR
                        Name LIKE :DIRECT_ADVISOR_PATTERN OR
                        Name LIKE :TPP_STUDENT_PATTERN OR
                        Name LIKE :TPP_ADVISOR_PATTERN OR
                        Name LIKE :TPP_GENERAL_PATTERN
                )
        ]);
    }

    // Define the operations to perform on each batch
    global void execute(Database.BatchableContext bc, List<SObject> scope) {
        List<Invoice__c> invoices = createInvoices(scope);
        Map<Id, Account> invoiceToAccountMap = getInvoiceToAccountMap(invoices);
        sendInvoicesToLAs(invoiceToAccountMap);
    }

    // Define final actions after the batch completes
    global void finish(Database.BatchableContext bc) {
    }

    private static List<Invoice__c> createInvoices(List<InvoiceLine__c> invoiceLines) {
        Map<Id, List<InvoiceLine__c>> accountIdToInvoiceLines = new Map<Id, List<Schema.InvoiceLine__c>>();
        for (InvoiceLine__c line: invoiceLines) {
            List<InvoiceLine__c> lines = accountIdToInvoiceLines.get(line.Account__c) ?? new List<InvoiceLine__c>();
            lines.add(line);
            accountIdToInvoiceLines.put(line.Account__c, lines);
        }

        List<Invoice__c> invoices = new List<Invoice__c>();
        for (Id accountId: accountIdToInvoiceLines.keySet()) {
            invoices.add(new Invoice__c(
                    Account__c = accountId,
                    TotalPrice__c = calculateTotal(accountIdToInvoiceLines.get(accountId)),
                    Note__c = 'Auto Generated'
            ));
        }
        insert invoices;

        for (InvoiceLine__c line: invoiceLines) {
            Id invoiceId = null;
            for (Invoice__c invoice: invoices) {
                if (line.Account__c == invoice.Account__c) {
                    invoiceId = invoice.Id;
                }
            }
            line.Invoice__c = invoiceId;
        }
        update invoiceLines;

        return invoices;
    }

    private static Decimal calculateTotal(List<InvoiceLine__c> invoiceLines) {
        Decimal total = 0;
        for (InvoiceLine__c invoiceLine : invoiceLines) {
            total += invoiceLine.TotalPrice__c;
        }
        return total;
    }

    private static Map<Id, Account> getInvoiceToAccountMap(List<Invoice__c> invoices) {
        Set<Id> accountIds = new Set<Id>();
        for (Invoice__c inv: invoices) {
            accountIds.add(inv.Account__c);
        }

        Map<Id, Account> accountsMap = new Map<Id, Account>([
                SELECT Id, (
                        SELECT Email
                        FROM Contacts
                        WHERE PortalRole__c INCLUDES ('Lead Advisor')
                )
                FROM Account WHERE Id IN :accountIds
        ]);

        Map<Id, Account> invoiceIdToAccount = new Map<Id, Account>();
        for (Invoice__c inv: invoices) {
            invoiceIdToAccount.put(inv.Id, accountsMap.get(inv.Account__c));
        }

        return invoiceIdToAccount;
    }

    private static void sendInvoicesToLAs(Map<Id, Account> invoiceToAccountMap) {
        EmailTemplate template = inv_Selector.getEmailTemplate('DailyInvoiceGenerated');
        Id orgWideEmailId = CoreSetting__c.getInstance().OrgWideEmailId__c;

        for (Id invoiceId: invoiceToAccountMap.keySet()) {
            sendViaEmailToLeadAdvisors(template.Id, orgWideEmailId, invoiceId, invoiceToAccountMap.get(invoiceId));
        }
    }

    private static void sendViaEmailToLeadAdvisors(Id templateId, Id orgWideEmailId, Id invoiceId, Account theAccount) {
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        for (Contact theContact: theAccount.Contacts) {
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setTemplateId(templateId);
            email.setWhatId(invoiceId);
            email.setTargetObjectId(theContact.Id);
            if (String.isNotBlank(orgWideEmailId)) {
                email.setOrgWideEmailAddressId(orgWideEmailId);
            }

            emails.add(email);
        }

        if (!Test.isRunningTest()) {
            Messaging.sendEmail(emails, false);
        }
    }

}