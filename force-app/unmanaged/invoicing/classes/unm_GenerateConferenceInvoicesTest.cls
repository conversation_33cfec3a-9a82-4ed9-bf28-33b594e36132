@IsTest
private class unm_GenerateConferenceInvoicesTest {
    @TestSetup
    static void setup() {
        cm_TestUtils.insertSettings();
    }

    @IsTest
    static void testRegistrationDeadlineInTheFuture() {
        Account theAccount = cm_TestUtils.createAccount('Acme');
        Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
        leadAdvisor.Division__c = 'Middle School';
        PortalUser.create(leadAdvisor);

        cm_Conference__c conference = new cm_Conference__c(
                RegistrationDeadline__c = System.today().addDays(10),
                FeeStudent__c = 100,
                FeePro__c = 150,
                unm_IsInvoicesGenerated__c = false,
                IsOpenForRegistration__c = true,
                IsCompetitionsAvailable__c = true
        );
        insert conference;

        cm_RegistrationType__c registrationType = new cm_RegistrationType__c(
                Name = 'Student',
                IsActive__c = true,
                Conference__c = conference.Id,
                IsCompetitionsSelectionAvailable__c = true
        );
        insert registrationType;

        cm_Participant__c cmParticipant = new cm_Participant__c(
                Conference__c = conference.Id,
                Contact__c = leadAdvisor.Id,
                RegistrationStatus__c = 'Approved',
                ConferenceRegistrationType__c = registrationType.Id
        );
        insert cmParticipant;

        cmp_Competition__c competition1 = new cmp_Competition__c(
                Name = 'First',
                Conference__c = conference.Id,
                Division__c = 'High School',
                IsOpenForRegistration__c = true,
                CompetitionFee__c = 100
        );

        insert competition1;

        insert new cmp_Participant__c(
                ConferenceParticipant__c = cmParticipant.Id,
                Competition__c = competition1.Id,
                Participant__c = leadAdvisor.Id
        );


        try {
            unm_GenerateConferenceInvoices.run(new List<Id>{conference.Id});
        } catch (Exception e) {
            // Error: The Conference is still open for registration.
        }
    }

    @IsTest
    static void testRegistrationDeadlineOk() {
        Account theAccount = cm_TestUtils.createAccount('Acme');
        Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
        leadAdvisor.Division__c = 'Middle School';
        PortalUser.create(leadAdvisor);

        cm_Conference__c conference = new cm_Conference__c(
                RegistrationDeadline__c = System.today().addDays(-10),
                FeeStudent__c = 100,
                FeePro__c = 150,
                unm_IsInvoicesGenerated__c = false,
                IsOpenForRegistration__c = true,
                IsCompetitionsAvailable__c = true
        );
        insert conference;

        cm_RegistrationType__c registrationType = new cm_RegistrationType__c(
                Name = 'Student',
                IsActive__c = true,
                Conference__c = conference.Id,
                IsCompetitionsSelectionAvailable__c = true
        );
        insert registrationType;

        cm_Participant__c cmParticipant = new cm_Participant__c(
                Conference__c = conference.Id,
                Contact__c = leadAdvisor.Id,
                RegistrationStatus__c = 'Approved',
                ConferenceRegistrationType__c = registrationType.Id
        );
        insert cmParticipant;

        cmp_Competition__c competition1 = new cmp_Competition__c(
                Name = 'First',
                Conference__c = conference.Id,
                Division__c = 'High School',
                IsOpenForRegistration__c = true,
                CompetitionFee__c = 100
        );

        insert competition1;

        insert new cmp_Participant__c(
                ConferenceParticipant__c = cmParticipant.Id,
                Competition__c = competition1.Id,
                Participant__c = leadAdvisor.Id
        );

        unm_GenerateConferenceInvoices.run(new List<Id>{conference.Id});

    }
}