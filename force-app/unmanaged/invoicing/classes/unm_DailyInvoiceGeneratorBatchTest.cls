@IsTest
private class unm_DailyInvoiceGeneratorBatchTest {
    @TestSetup
    static void setup() {
        insert new cm_Settings__c(
                ExhibitorPriceOrganizationIndustry__c = 100
        );

        insert new inv_Settings__c(
                UsePricePerGuest__c = true,
                IsMultiplyPriceForEachNight__c = true
        );

        Account theAccount = new Account(Name = 'Acme');
        insert theAccount;

        insert new InvoiceLine__c(
                Name = 'Direct Student (<PERSON>)',
                Account__c = theAccount.Id,
                Quantity__c = 1,
                UnitPrice__c = 100
        );
    }

    @IsTest
    static void testBehavior() {
        Test.startTest();
        {
            Database.executeBatch(new unm_DailyInvoiceGeneratorBatch(), 1000);
        }
        Test.stopTest();

        InvoiceLine__c invoiceLine = [SELECT Id, Invoice__c FROM InvoiceLine__c LIMIT 1];
        Assert.isNotNull(invoiceLine.Invoice__c, 'Invoice line should be attached to invoice');
    }
}