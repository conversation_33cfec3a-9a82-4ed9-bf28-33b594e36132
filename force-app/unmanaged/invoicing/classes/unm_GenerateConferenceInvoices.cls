public with sharing class unm_GenerateConferenceInvoices {
    private static final String PARTICIPATION_STUDENT_PREFIX = 'Participation Fee (Students)';
    private static final String PARTICIPATION_ADVISOR_PREFIX = 'Participation Fee (Advisors)';

    @InvocableMethod(Label='Generate Invoices For The Conference')
    public static void run(List<Id> ids) {
        cm_Conference__c conference = getCurrentConference(ids);

        List<InvoiceLine__c> invoiceLines = createInvoiceLinesForConferenceParticipants(conference);
        List<InvoiceLine__c> invoiceLinesForCompetition = createInvoiceLinesForCompetitionParticipants(conference);
        List<InvoiceLine__c> invoiceLinesForPayingRT = createInvoiceLinesForPayingRegistrationTypes(conference);

        invoiceLines.addAll(invoiceLinesForCompetition);
        invoiceLines.addAll(invoiceLinesForPayingRT);

        List<Invoice__c> createdInvoices = createInvoices(invoiceLines, conference.Name);

        sendEmailNotifications(conference, createdInvoices);

        update new cm_Conference__c(Id = conference.Id, unm_IsInvoicesGenerated__c = true);
    }

    private static void sendEmailNotifications(cm_Conference__c conference, List<Invoice__c> createdInvoices) {
        Map<Id, Id> accountIdToInvoiceId = new Map<Id, Id>();
        for (Invoice__c invoice : createdInvoices) {
            accountIdToInvoiceId.put(invoice.Account__c, invoice.Id);
        }

        Set<Id> accountIds = new Set<Id>();
        for (Invoice__c invoice : createdInvoices) {
            accountIds.add(invoice.Account__c);
        }

        List<Contact> LAs = [
            SELECT Id, Name, Email, AccountId
            FROM Contact
            WHERE AccountId IN :accountIds AND PortalRole__c INCLUDES ('Lead Advisor')
        ];

        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        for (Contact LA : LAs) {
            // TODO: Move to Core package
            String linkToInvoice = 'https://www.ywdf.org/s/invoices/' + accountIdToInvoiceId.get(LA.AccountId);
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setToAddresses(new List<String>{ LA.Email });
            email.setSubject('Invoice Generated for Your Conference Registration');
            email.setHtmlBody(
                'Dear ' +
                    LA.Name +
                    ',<br><br>' +
                    'We are pleased to inform you that an invoice has been generated for your school\'s ' +
                    conference.Name +
                    ' registration. ' +
                    'You can access the invoice and payment details by logging into your account: ' +
                    linkToInvoice +
                    '<br><br>' +
                    'If you have any questions or need assistance, please feel free to contact <NAME_EMAIL><br><br>' +
                    'Thank you,<br>' +
                    'Support SkillsUSA'
            );
            emails.add(email);
        }
        Messaging.sendEmail(emails);
    }

    private static cm_Conference__c getCurrentConference(List<Id> ids) {
        if (ids.size() > 1) {
            throw Error.exception('The Action is working only with single conference record.');
        }

        cm_Conference__c theConference = [
            SELECT
                Name,
                RegistrationDeadline__c,
                FeeStudent__c,
                FeePro__c,
                unm_IsInvoicesGenerated__c,
                (
                    SELECT
                        Id,
                        Contact__r.AccountId,
                        Contact__r.PortalRole__c,
                        ConferenceRegistrationType__r.Fee__c,
                        ConferenceRegistrationType__r.IsActive__c,
                        ConferenceRegistrationType__r.FeeCode__c,
                        ConferenceRegistrationType__r.Name
                    FROM Participants__r
                    WHERE RegistrationStatus__c = 'Approved'
                ),
                (
                    SELECT
                        Id,
                        CompetitionFee__c,
                        Name,
                        (
                            SELECT Participant__r.AccountId, Competition__r.Name, Competition__r.CompetitionFee__c
                            FROM CompetitionParticipants__r
                        )
                    FROM Competitions__r
                    WHERE CompetitionFee__c > 0
                )
            FROM cm_Conference__c
            WHERE Id = :ids[0]
        ];

        // Check if invoices are already generated
        if (theConference.unm_IsInvoicesGenerated__c) {
            throw Error.exception('Invoices for The Conference are already generated.');
        }

        // Check if deadline of the conference is in the past
        if (theConference.RegistrationDeadline__c > System.now()) {
            throw Error.exception('The Conference is still open for registration.');
        }

        return theConference;
    }

    private static List<InvoiceLine__c> createInvoiceLinesForConferenceParticipants(cm_Conference__c theConference) {
        Map<Id, List<cm_Participant__c>> accountIdToParticipantList = new Map<Id, List<cm_Participant__c>>();
        for (cm_Participant__c participant : theConference.Participants__r) {
            List<cm_Participant__c> participants = accountIdToParticipantList.get(participant.Contact__r.AccountId) ??
                new List<cm_Participant__c>();
            participants.add(participant);
            accountIdToParticipantList.put(participant.Contact__r.AccountId, participants);
        }

        List<InvoiceLine__c> invoiceLines = new List<InvoiceLine__c>();
        for (Id accountId : accountIdToParticipantList.keySet()) {
            Integer students = 0;
            Integer advisors = 0;
            for (cm_Participant__c participant : accountIdToParticipantList.get(accountId)) {
                String role = participant.Contact__r.PortalRole__c ?? '';
                Boolean isStudent = role.contains('Student');
                Boolean isAdvisor = role.contains('Lead Advisor') || role.contains('Advisor');

                if (isStudent) {
                    students++;
                } else if (isAdvisor) {
                    advisors++;
                }
            }

            if (students > 0) {
                invoiceLines.add(
                    new InvoiceLine__c(
                        Name = PARTICIPATION_STUDENT_PREFIX,
                        Account__c = accountId,
                        Quantity__c = students,
                        UnitPrice__c = theConference.FeeStudent__c,
                        Comment__c = theConference.Name
                    )
                );
            }

            if (advisors > 0) {
                invoiceLines.add(
                    new InvoiceLine__c(
                        Name = PARTICIPATION_ADVISOR_PREFIX,
                        Account__c = accountId,
                        Quantity__c = advisors,
                        UnitPrice__c = theConference.FeePro__c,
                        Comment__c = theConference.Name
                    )
                );
            }
        }

        return invoiceLines;
    }

    private static List<InvoiceLine__c> createInvoiceLinesForCompetitionParticipants(cm_Conference__c theConference) {
        Map<Id, List<cmp_Participant__c>> accountIdToCompetitionParticipant = new Map<Id, List<cmp_Participant__c>>();
        for (cmp_Competition__c competition : theConference.Competitions__r) {
            for (cmp_Participant__c participant : competition.CompetitionParticipants__r) {
                Id participantAccountId = participant.Participant__r.AccountId;
                List<cmp_Participant__c> participants = accountIdToCompetitionParticipant.get(participantAccountId) ??
                    new List<cmp_Participant__c>();
                participants.add(participant);
                accountIdToCompetitionParticipant.put(participantAccountId, participants);
            }
        }

        List<InvoiceLine__c> invoiceLines = new List<InvoiceLine__c>();
        for (Id accountId : accountIdToCompetitionParticipant.keySet()) {
            Map<String, List<cmp_Participant__c>> competitionNameToParticipants = new Map<String, List<cmp_Participant__c>>();

            List<cmp_Participant__c> participants = accountIdToCompetitionParticipant.get(accountId);
            for (cmp_Participant__c participant : participants) {
                List<cmp_Participant__c> accountsParticipants = competitionNameToParticipants.get(
                    participant.Competition__r.Name
                ) ?? new List<cmp_Participant__c>();
                accountsParticipants.add(participant);
                competitionNameToParticipants.put(participant.Competition__r.Name, accountsParticipants);
            }

            for (String competitionName : competitionNameToParticipants.keySet()) {
                if (competitionNameToParticipants.get(competitionName).size() > 0) {
                    invoiceLines.add(
                        new InvoiceLine__c(
                            Name = 'Competition Fee (' +
                                competitionNameToParticipants.get(competitionName)[0].Competition__r.Name +
                                ')',
                            Account__c = accountId,
                            Quantity__c = competitionNameToParticipants.get(competitionName).size(),
                            UnitPrice__c = competitionNameToParticipants.get(competitionName)[0]
                                .Competition__r.CompetitionFee__c,
                            Comment__c = theConference.Name
                        )
                    );
                }
            }
        }

        return invoiceLines;
    }

    private static List<InvoiceLine__c> createInvoiceLinesForPayingRegistrationTypes(cm_Conference__c theConference) {
        // Map to track participant counts and fee per registration type per account
        Map<Id, Map<String, Map<String, Object>>> accountIdToTypeData = new Map<Id, Map<String, Map<String, Object>>>();

        // Filter participants and group by account and registration type
        for (cm_Participant__c participant : theConference.Participants__r) {
            if (isValidRegistrationType(participant)) {
                Id accountId = participant.Contact__r.AccountId;
                String registrationTypeName = participant.ConferenceRegistrationType__r.Name;
                Decimal unitPrice = participant.ConferenceRegistrationType__r.Fee__c;

                // Initialize nested map if not present
                if (!accountIdToTypeData.containsKey(accountId)) {
                    accountIdToTypeData.put(accountId, new Map<String, Map<String, Object>>());
                }

                // Add participant to the corresponding registration type for the account
                Map<String, Map<String, Object>> typeData = accountIdToTypeData.get(accountId);
                if (!typeData.containsKey(registrationTypeName)) {
                    typeData.put(
                        registrationTypeName,
                        new Map<String, Object>{ 'participantCount' => 0, 'unitPrice' => unitPrice }
                    );
                }

                // Update participant count for the registration type
                Map<String, Object> data = typeData.get(registrationTypeName);
                data.put('participantCount', (Integer) data.get('participantCount') + 1);
            }
        }

        // Create invoice lines
        List<InvoiceLine__c> invoiceLines = new List<InvoiceLine__c>();
        for (Id accountId : accountIdToTypeData.keySet()) {
            Map<String, Map<String, Object>> typeData = accountIdToTypeData.get(accountId);

            for (String registrationTypeName : typeData.keySet()) {
                Map<String, Object> data = typeData.get(registrationTypeName);

                invoiceLines.add(
                    new InvoiceLine__c(
                        Name = 'Registration Type Fee (' + registrationTypeName + ')',
                        Account__c = accountId,
                        Quantity__c = (Integer) data.get('participantCount'),
                        UnitPrice__c = (Decimal) data.get('unitPrice'),
                        Comment__c = theConference.Name
                    )
                );
            }
        }

        return invoiceLines;
    }

    // Helper method to check if a participant meets the required conditions
    private static Boolean isValidRegistrationType(cm_Participant__c participant) {
        return participant.Contact__c != null &&
            participant.ConferenceRegistrationType__r != null &&
            participant.ConferenceRegistrationType__r.Fee__c > 0 &&
            participant.ConferenceRegistrationType__r.IsActive__c &&
            participant.ConferenceRegistrationType__r.FeeCode__c == 'Paying';
    }

    private static List<Invoice__c> createInvoices(List<InvoiceLine__c> invoiceLines, String conferenceName) {
        insert invoiceLines;

        invoiceLines = [SELECT Account__c, TotalPrice__c FROM InvoiceLine__c WHERE Id IN :invoiceLines];

        Map<Id, List<InvoiceLine__c>> accountIdToInvoiceLines = new Map<Id, List<Schema.InvoiceLine__c>>();
        for (InvoiceLine__c line : invoiceLines) {
            List<InvoiceLine__c> lines = accountIdToInvoiceLines.get(line.Account__c) ?? new List<InvoiceLine__c>();
            lines.add(line);
            accountIdToInvoiceLines.put(line.Account__c, lines);
        }

        List<Invoice__c> invoices = new List<Invoice__c>();
        for (Id accountId : accountIdToInvoiceLines.keySet()) {
            invoices.add(
                new Invoice__c(
                    Account__c = accountId,
                    TotalPrice__c = calculateTotal(accountIdToInvoiceLines.get(accountId)),
                    Note__c = 'Conference (' + conferenceName + ')'
                )
            );
        }
        insert invoices;

        for (InvoiceLine__c line : invoiceLines) {
            Id invoiceId = null;
            for (Invoice__c invoice : invoices) {
                if (line.Account__c == invoice.Account__c) {
                    invoiceId = invoice.Id;
                }
            }
            line.Invoice__c = invoiceId;
        }
        update invoiceLines;

        return invoices;
    }

    public static Decimal calculateTotal(List<InvoiceLine__c> invoiceLines) {
        Decimal total = 0;
        for (InvoiceLine__c invoiceLine : invoiceLines) {
            total += invoiceLine.TotalPrice__c;
        }
        return total;
    }
}