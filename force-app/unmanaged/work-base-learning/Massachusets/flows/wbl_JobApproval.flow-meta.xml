<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <assignments>
        <name>Instantiate_Job_for_Update</name>
        <label>Instantiate Job for Update</label>
        <locationX>314</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>jobForUpdate.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetJob.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>jobForUpdate.wbl_IsApproved__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>jobForUpdate.wbl_JobOccupation__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>newJobOccupation.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Job</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Instantiate_Job_With_Occupation_for_Update</name>
        <label>Instantiate Job With Occupation for Update</label>
        <locationX>578</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>jobForUpdate.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetJob.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>jobForUpdate.wbl_IsApproved__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Job</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Instantiate_new_Job_Occupation</name>
        <label>Instantiate new Job Occupation</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>newJobOccupation.Code__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Code</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>newJobOccupation.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Title</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>newJobOccupation.NumberOfHours__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Number_of_Hours</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>newJobOccupation.Account__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>GetJob.Owner:User.AccountId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Insert_New_Job_Occupation</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Is_Job_Already_Approved</name>
        <label>Is Job Already Approved?</label>
        <locationX>248</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>What_is_the_job_type</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>jobAppr_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetJob.wbl_IsApproved__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Job_Already_Approved</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>What_is_the_job_type</name>
        <label>What is the job type</label>
        <locationX>446</locationX>
        <locationY>350</locationY>
        <defaultConnector>
            <targetReference>Confirm_Job_Approval</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>WBL or RAP with Occupation</defaultConnectorLabel>
        <rules>
            <name>RAP_Without_Occupation</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetJob.wbl_JobOccupation__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GetJob.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>RAP</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Provide_New_Job_Occupation_Details</targetReference>
            </connector>
            <label>RAP without Occupation</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>[WBL] Job Approval {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[WBL] Job Approval</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Insert_New_Job_Occupation</name>
        <label>Insert new Job Occupation</label>
        <locationX>314</locationX>
        <locationY>674</locationY>
        <connector>
            <targetReference>Instantiate_Job_for_Update</targetReference>
        </connector>
        <inputReference>newJobOccupation</inputReference>
    </recordCreates>
    <recordLookups>
        <name>GetJob</name>
        <label>Get Job</label>
        <locationX>248</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Job_Already_Approved</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>JobBoard__Job__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Job</name>
        <label>Update Job</label>
        <locationX>446</locationX>
        <locationY>974</locationY>
        <inputReference>jobForUpdate</inputReference>
    </recordUpdates>
    <screens>
        <name>Confirm_Job_Approval</name>
        <label>Confirm Job Approval</label>
        <locationX>578</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Cancel</backButtonLabel>
        <connector>
            <targetReference>Instantiate_Job_With_Occupation_for_Update</targetReference>
        </connector>
        <fields>
            <name>jobApproveConfirmation</name>
            <fieldText
            >&lt;p&gt;Once the job is approved and active it will become available to applicants. Click Approve if you want to continue.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Approve</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Job_Already_Approved</name>
        <label>Job Already Approved</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>alreadyApprovedScreenBody</name>
            <fieldText>&lt;p&gt;This job is already approved, click Finish  to close this window&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Provide_New_Job_Occupation_Details</name>
        <label>Provide New Job Occupation Details</label>
        <locationX>314</locationX>
        <locationY>458</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Instantiate_new_Job_Occupation</targetReference>
        </connector>
        <fields>
            <name>newOccupationHelpText</name>
            <fieldText
            >&lt;p&gt;Occupation Code is required in order to approve this job. Please specify details of the new Occupation below.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Title</name>
            <dataType>String</dataType>
            <defaultValue>
                <elementReference>GetJob.wbl_NewOccupation__c</elementReference>
            </defaultValue>
            <fieldText>Title</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Number_of_Hours</name>
            <dataType>Number</dataType>
            <defaultValue>
                <elementReference>GetJob.wbl_NumberOfHours__c</elementReference>
            </defaultValue>
            <fieldText>Number of Hours</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <scale>0</scale>
        </fields>
        <fields>
            <name>Code</name>
            <dataType>String</dataType>
            <fieldText>Code</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>122</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetJob</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>jobForUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>JobBoard__Job__c</objectType>
    </variables>
    <variables>
        <name>newJobOccupation</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>wbl_JobOccupation__c</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
