<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_notification_to_requester</name>
        <label>Send notification to requester</label>
        <locationX>176</locationX>
        <locationY>1322</locationY>
        <actionName>wbl_EmailNotifier</actionName>
        <actionType>apex</actionType>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>emailTemplateName</name>
            <value>
                <stringValue>wbl_AccessRequestApproved</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipients</name>
            <value>
                <elementReference>notificationRecipients</elementReference>
            </value>
        </inputParameters>
        <nameSegment>wbl_EmailNotifier</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <assignments>
        <name>Add_requester_to_recipients</name>
        <label>Add requester to recipients</label>
        <locationX>176</locationX>
        <locationY>1214</locationY>
        <assignmentItems>
            <assignToReference>notificationRecipients</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Get_Access_Request.Contact__r.Email</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Send_notification_to_requester</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Mark_Account_as_RAP</name>
        <label>Mark Account as RAP</label>
        <locationX>176</locationX>
        <locationY>998</locationY>
        <assignmentItems>
            <assignToReference>partnerAccount.wbl_IsRapCompany__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Account</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Mark_Request_as_Approved</name>
        <label>Mark Request as Approved</label>
        <locationX>176</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>accessRequestForUpdate.Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Set_Request_Comment</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Account_Id</name>
        <label>Set Account Id</label>
        <locationX>176</locationX>
        <locationY>890</locationY>
        <assignmentItems>
            <assignToReference>partnerAccount.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Access_Request.Account__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Mark_Account_as_RAP</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Request_Comment</name>
        <label>Set Request Comment</label>
        <locationX>176</locationX>
        <locationY>566</locationY>
        <assignmentItems>
            <assignToReference>accessRequestForUpdate.Comment__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Comment</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Set_Request_Resolution_Date</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Request_Id</name>
        <label>Set Request Id</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>accessRequestForUpdate.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Access_Request.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Mark_Request_as_Approved</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Request_Resolution_Date</name>
        <label>Set Request Resolution Date</label>
        <locationX>176</locationX>
        <locationY>674</locationY>
        <assignmentItems>
            <assignToReference>accessRequestForUpdate.ResolutionDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Request</targetReference>
        </connector>
    </assignments>
    <description>Screen flow to approve WBL Access Request and grant related Account access to WBL platform</description>
    <environments>Default</environments>
    <interviewLabel>[WBL] Approve Access Request {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[WBL] Approve Access Request</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Access_Request</name>
        <label>Get Access Request</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Confirmation_Screen</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>wbl_AccessRequest__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Account</name>
        <label>Update Account</label>
        <locationX>176</locationX>
        <locationY>1106</locationY>
        <connector>
            <targetReference>Add_requester_to_recipients</targetReference>
        </connector>
        <inputReference>partnerAccount</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Request</name>
        <label>Update Request</label>
        <locationX>176</locationX>
        <locationY>782</locationY>
        <connector>
            <targetReference>Set_Account_Id</targetReference>
        </connector>
        <inputReference>accessRequestForUpdate</inputReference>
    </recordUpdates>
    <screens>
        <name>Confirmation_Screen</name>
        <label>Confirmation Screen</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Set_Request_Id</targetReference>
        </connector>
        <fields>
            <name>Confirmation</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Once Access Request is approved related Account will be marked as RAP company.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt; &lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Comment</name>
            <defaultValue>
                <stringValue>{!Get_Access_Request.Comment__c}</stringValue>
            </defaultValue>
            <fieldText>Comment</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Approve</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Access_Request</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>accessRequestForUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>wbl_AccessRequest__c</objectType>
    </variables>
    <variables>
        <name>notificationRecipients</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>partnerAccount</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
