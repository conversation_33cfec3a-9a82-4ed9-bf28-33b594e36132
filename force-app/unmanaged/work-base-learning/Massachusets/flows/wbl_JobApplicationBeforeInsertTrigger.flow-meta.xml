<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <description>Copy of Job Board&apos;s default flow trigger which runs before insert</description>
    <environments>Default</environments>
    <formulas>
        <name>NewJobApplicationName</name>
        <dataType>String</dataType>
        <expression
        >LEFT({!$Record.JobBoard__First_Name__c} + &quot; &quot; + {!$Record.JobBoard__Last_Name__c} + &quot; — &quot;  +  {!$Record.JobBoard__Job__r.Name}, 80)</expression>
    </formulas>
    <interviewLabel>[WBL] Job Application Before Insert Trigger {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[WBL] Job Application Before Insert Trigger</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Compose_application_name</name>
        <label>Compose application name</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>NewJobApplicationName</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Compose_application_name</targetReference>
        </connector>
        <object>JobBoard__Job_Application__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
