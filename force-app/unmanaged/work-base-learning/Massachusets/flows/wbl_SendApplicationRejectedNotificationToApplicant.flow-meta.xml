<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>DispatchEmail</name>
        <label>Dispatch Email</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>GetJobApplicationWithJobFields.JobBoard__Email__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <elementReference>orgWideEmail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>emailSubject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>emailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>jobApplicationId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <environments>Default</environments>
    <interviewLabel>[WBL] Send Application Rejected Notification to Applicant
        {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[WBL] Send Application Rejected Notification to Applicant</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetJobApplicationWithJobFields</name>
        <label>Get Job Application with Job fields</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>DispatchEmail</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>jobApplicationId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>JobBoard__Job_Application__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetJobApplicationWithJobFields</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>emailBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text
        >&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Hello, {!GetJobApplicationWithJobFields.JobBoard__First_Name__c}.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Unfortunately your job application for {!GetJobApplicationWithJobFields.JobBoard__Job__r.Name} in &lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;{!GetJobApplicationWithJobFields.JobBoard__Job__r.JobBoard__Company_Name__c}&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt; had been rejected.&lt;/span&gt;&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>emailSubject</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>Job Application for {!GetJobApplicationWithJobFields.JobBoard__Job__r.Name} update</text>
    </textTemplates>
    <variables>
        <name>jobApplicationId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>orgWideEmail</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
