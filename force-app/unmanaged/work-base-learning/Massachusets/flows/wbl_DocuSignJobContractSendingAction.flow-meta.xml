<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>SendContracts</name>
        <label>Send Contracts</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <actionName>wbl_DocuSignJobContractSender</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>successScreen</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>jobAppIds</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>wbl_DocuSignJobContractSender</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <assignments>
        <name>AssignJobAppIds</name>
        <label>Assign jobAppIds</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>jobAppIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>SendContracts</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Is_Application_valid_for_contract</name>
        <label>Is Application valid for contract?</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <defaultConnector>
            <targetReference>Contract_send_not_available_screen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>hiredOnboard_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Job_Application.wbl_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Hired</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Job_Application.wbl_OnboardingForm__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_Job_Application.wbl_DocuSignContractEnvelope__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ConfirmationScreen</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>cannotSendReason</name>
        <dataType>String</dataType>
        <expression
        >IF(NOT(ISPICKVAL({!Get_Job_Application.wbl_Status__c}, &quot;Hired&quot;)), &quot;- Application Status is not Hired&quot; + BR() , &quot;&quot;)  +
IF(ISBLANK({!Get_Job_Application.wbl_OnboardingForm__c}), &quot;- Applicant didn&apos;t complete onboarding form&quot; + BR(), &quot;&quot;) +
IF(NOT(ISBLANK({!Get_Job_Application.wbl_DocuSignContractEnvelope__c})), &quot;- Contract already sent&quot;, &quot;&quot;)</expression>
    </formulas>
    <interviewLabel>[WBL] DocuSign Job Contract Sending Action {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[WBL] DocuSign Job Contract Sending Action</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>Get_Job_Application</name>
        <label>Get Job Application</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Is_Application_valid_for_contract</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>JobBoard__Job_Application__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>ConfirmationScreen</name>
        <label>Confirmation</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>AssignJobAppIds</targetReference>
        </connector>
        <fields>
            <name>confirmationScreenBody</name>
            <fieldText
            >&lt;p&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;This action will send contract for signing to job applicant, partner and internal representetive. Are you sure you want to continue?&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Contract_send_not_available_screen</name>
        <label>Contract send not available</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>sendNotAvailableBody</name>
            <fieldText
            >&lt;p&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;Contract for this job application cannot be send. Reason:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: 14px;&quot;&gt;{!cannotSendReason}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>successScreen</name>
        <label>Success</label>
        <locationX>50</locationX>
        <locationY>674</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>successScreenBody</name>
            <fieldText
            >&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Contract has been successfully send!&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Job_Application</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>jobAppIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
