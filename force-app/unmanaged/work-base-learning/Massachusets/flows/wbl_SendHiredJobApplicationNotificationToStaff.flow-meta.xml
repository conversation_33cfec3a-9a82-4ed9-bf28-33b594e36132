<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Email_via_Apex</name>
        <label>Send Email via Apex</label>
        <locationX>358</locationX>
        <locationY>1658</locationY>
        <actionName>wbl_EmailNotifier</actionName>
        <actionType>apex</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>emailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipients</name>
            <value>
                <elementReference>recipientEmails</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>subject</name>
            <value>
                <elementReference>emailSubject</elementReference>
            </value>
        </inputParameters>
        <nameSegment>wbl_EmailNotifier</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <assignments>
        <name>Add_Lead_Advisor_email_to_recipient_emails</name>
        <label>Add Lead Advisor email to recipient emails</label>
        <locationX>446</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>recipientEmails</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Iterate_over_Lead_Advisor_contacts.Email</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Iterate_over_Lead_Advisor_contacts</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AddSingleUserIdToRecipientIUserIds</name>
        <label>Add Single User Id to  recipientIUserIds</label>
        <locationX>50</locationX>
        <locationY>758</locationY>
        <assignmentItems>
            <assignToReference>recipientIUserIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>settingRecipientId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>GetUsersWithEmail</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AddUserEmailToRecipientEmails</name>
        <label>Add User Email to recipientEmails</label>
        <locationX>446</locationX>
        <locationY>1466</locationY>
        <assignmentItems>
            <assignToReference>recipientEmails</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>IterateOverRecipientUsers.Email</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>IterateOverRecipientUsers</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AddUserIdToRecipientIUserIds</name>
        <label>Add User Id to  recipientIUserIds</label>
        <locationX>490</locationX>
        <locationY>974</locationY>
        <assignmentItems>
            <assignToReference>recipientIUserIds</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>IterateOverGroupMembers.UserOrGroupId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>IterateOverGroupMembers</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>IsRecipientFromSettingUserOrGroup</name>
        <label>Is recipient from Settings User or Group</label>
        <locationX>358</locationX>
        <locationY>650</locationY>
        <defaultConnectorLabel>None of them</defaultConnectorLabel>
        <rules>
            <name>settingUser</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.wbl_Settings__c.JobAppHireNotificationRecipientId__c</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>005</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AddSingleUserIdToRecipientIUserIds</targetReference>
            </connector>
            <label>User</label>
        </rules>
        <rules>
            <name>recipientGroup</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.wbl_Settings__c.JobAppHireNotificationRecipientId__c</leftValueReference>
                <operator>StartsWith</operator>
                <rightValue>
                    <stringValue>00G</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetGroupMembers</targetReference>
            </connector>
            <label>Group</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>[WBL] Send Hired Job Application Notification to Staff {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[WBL] Send Hired Job Application Notification to Staff</label>
    <loops>
        <name>Iterate_over_Lead_Advisor_contacts</name>
        <label>Iterate over Lead Advisor contacts</label>
        <locationX>358</locationX>
        <locationY>350</locationY>
        <collectionReference>Get_Lead_Advisor_for_Account</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_Lead_Advisor_email_to_recipient_emails</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>IsRecipientFromSettingUserOrGroup</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>IterateOverGroupMembers</name>
        <label>Iterate over Group Members</label>
        <locationX>402</locationX>
        <locationY>866</locationY>
        <collectionReference>GetGroupMembers</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>AddUserIdToRecipientIUserIds</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>GetUsersWithEmail</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>IterateOverRecipientUsers</name>
        <label>Iterate Over Recipient Users</label>
        <locationX>358</locationX>
        <locationY>1358</locationY>
        <collectionReference>GetUsersWithEmail</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>AddUserEmailToRecipientEmails</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Send_Email_via_Apex</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Lead_Advisor_for_Account</name>
        <label>Get Lead Advisor for Account</label>
        <locationX>358</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Iterate_over_Lead_Advisor_contacts</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>PortalRole__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Lead Advisor</stringValue>
            </value>
        </filters>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetJobApplicationWithJobFields.JobBoard__Contact__r.AccountId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetGroupMembers</name>
        <label>Get Group Members</label>
        <locationX>402</locationX>
        <locationY>758</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>IterateOverGroupMembers</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>GroupId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Setup.wbl_Settings__c.JobAppHireNotificationRecipientId__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>GroupMember</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetJobApplicationWithJobFields</name>
        <label>Get Job Application with Job fields</label>
        <locationX>358</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Lead_Advisor_for_Account</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>jobApplicationId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>JobBoard__Job_Application__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetUsersWithEmail</name>
        <label>Get Users with Email</label>
        <locationX>358</locationX>
        <locationY>1250</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>IterateOverRecipientUsers</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>recipientIUserIds</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>User</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Email</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>232</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetJobApplicationWithJobFields</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>emailBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text
        >&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Hello,&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;{!GetJobApplicationWithJobFields.JobBoard__First_Name__c} &lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;{!GetJobApplicationWithJobFields.JobBoard__Last_Name__c}&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt; from {!GetJobApplicationWithJobFields.JobBoard__Contact__r.Account.Name} has accepted the position of {!GetJobApplicationWithJobFields.JobBoard__Job__r.Name} with {!GetJobApplicationWithJobFields.JobBoard__Job__r.JobBoard__Company_Name__c}. Thank you to &lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;{!GetJobApplicationWithJobFields.JobBoard__Job__r.JobBoard__Company_Name__c}&lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt; for serving as a member of the SkillsUSA Illinois Business Partner Network. It is only through their support we are able to fulfil our mission of empowering students to become skilled professionals, career-ready leaders, and responsible community members.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Congratulations, again to &lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0); background-color: rgb(255, 255, 255);&quot;&gt;{!GetJobApplicationWithJobFields.JobBoard__First_Name__c} {!GetJobApplicationWithJobFields.JobBoard__Last_Name__c} &lt;/span&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;!&lt;/span&gt;&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>emailSubject</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text
        >{!GetJobApplicationWithJobFields.JobBoard__First_Name__c} {!GetJobApplicationWithJobFields.JobBoard__Last_Name__c} got hired!</text>
    </textTemplates>
    <variables>
        <name>jobApplicationId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>orgWideEmail</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recipientEmails</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recipientIUserIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>Id of User or Group</description>
        <name>settingRecipientId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
