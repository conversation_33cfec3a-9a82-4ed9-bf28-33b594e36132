<?xml version="1.0" encoding="UTF-8" ?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Personal Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>FirstName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>LastName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Phone__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>PersonalEmail__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>DateOfBirth__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SocialSecurityNumber__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Gender__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SelectiveServiceNumber__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>ParentFirstName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>ParentLastName__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>ParentEmail__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentPhone__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Address</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Address__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PostalCode__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>City__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>State__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Ethnicity/Race</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EthnicGroup__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Race__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Education</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EducationLevel__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SchoolName__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Career</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EmploymentStatus__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EmployerName__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CareerConnection__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Occupation__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Other</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>VeteranStatus__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HaveDisabilities__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns />
        <layoutColumns />
        <layoutColumns />
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hDT0000057XXd</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
