<?xml version="1.0" encoding="UTF-8" ?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JobBoard__Company_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JobBoard__Description__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_NumberOfApplicants__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JobBoard__External_Application_Link__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_ExternalApplicationLinkClickCount__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JobBoard__Summary__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>JobBoard__Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JobBoard__Industry__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>JobBoard__Experience__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JobBoard__Salary__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>RAP Fields</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_JobOccupation__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_NewOccupation__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_NumberOfHours__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_UsedOccupationHoursLimit__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_SpentHours__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_ClassroomInstructionsSource__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_MetricsOfinstructions__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_RelatedTechnicalInstructions__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Visibility</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_IsActive__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>wbl_IsApproved__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Location</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JobBoard__Country__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JobBoard__Address__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JobBoard__City__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JobBoard__Location__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Custom Links</label>
        <layoutColumns />
        <layoutColumns />
        <layoutColumns />
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>JobBoard__Job__c.wbl_ApproveJob</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Clone</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Share</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeOwnerOne</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>JobBoard__Job_Application__c.JobBoard__Job__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>User__c</fields>
        <fields>CREATED_DATE</fields>
        <relatedList>wbl_JobAccess__c.Job__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hEk0000023u85</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
