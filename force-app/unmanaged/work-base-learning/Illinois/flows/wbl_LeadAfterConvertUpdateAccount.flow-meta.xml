<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <description
    >After lead conversion copies RAP Company and WBL Approved values to corresponding fields on Account</description>
    <environments>Default</environments>
    <formulas>
        <name>isWblSource</name>
        <dataType>Boolean</dataType>
        <expression>ISPICKVAL({!$Record.LeadSource}, &quot;Work Base Learning Invitation&quot;)</expression>
    </formulas>
    <interviewLabel>[WBL] Lead - after convert - Update Account {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[WBL] Lead after convert - update Account</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>UpdateConvertedAccountRapCompanyWblFields</name>
        <label>Update Converted Account RAP Company, WBL Access fields</label>
        <locationX>176</locationX>
        <locationY>323</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ConvertedAccountId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>wbl_IsRapCompany__c</field>
            <value>
                <elementReference>$Record.wbl_IsRapCompany__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>wbl_IsApproved__c</field>
            <value>
                <elementReference>isWblSource</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>UpdateConvertedAccountRapCompanyWblFields</targetReference>
        </connector>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsConverted</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>ConvertedAccountId</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue />
            </value>
        </filters>
        <object>Lead</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>convertedAccount</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
</Flow>
