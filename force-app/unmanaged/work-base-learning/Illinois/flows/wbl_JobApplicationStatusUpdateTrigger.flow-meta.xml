<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <constants>
        <name>orgWideEmailIdSettingName</name>
        <dataType>String</dataType>
        <value>
            <stringValue>wbl_OrgWideEmailId</stringValue>
        </value>
    </constants>
    <decisions>
        <name>Is_Application_for_RAP_Job</name>
        <label>Is Application for RAP Job?</label>
        <locationX>710</locationX>
        <locationY>600</locationY>
        <defaultConnectorLabel>No</defaultConnectorLabel>
        <rules>
            <name>rapJobApp_Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.JobBoard__Job__r.wbl_IsRapJob__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SendOnboardingFormToHiredApplicant</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>WhatIsTheApplicationStatus</name>
        <label>What is the Application Status?</label>
        <locationX>578</locationX>
        <locationY>384</locationY>
        <defaultConnectorLabel>None of specified</defaultConnectorLabel>
        <rules>
            <name>StatusRejected</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.wbl_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Rejected</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ApplicantRejectedNotifcation</targetReference>
            </connector>
            <label>Rejected</label>
        </rules>
        <rules>
            <name>StatusReadyForInterview</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.wbl_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ready for Interview</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>InterviewReadyApplicantNotification</targetReference>
            </connector>
            <label>Ready for Interview</label>
        </rules>
        <rules>
            <name>StatusHired</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.wbl_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Hired</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SendNotificationToStaff</targetReference>
            </connector>
            <label>Hired</label>
        </rules>
    </decisions>
    <description>Triggered on job application status changes in order to send notifications</description>
    <environments>Default</environments>
    <interviewLabel>[WBL] Job Application Status Update Trigger {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[WBL] Job Application Status Update Trigger</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetOrgWideEmailAddress</name>
        <label>Get Org Wide Email Address</label>
        <locationX>578</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>WhatIsTheApplicationStatus</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Setup.wbl_Settings__c.OrgWideEmailId__c</elementReference>
            </value>
        </filters>
        <object>OrgWideEmailAddress</object>
        <outputAssignments>
            <assignToReference>orgWideEmail</assignToReference>
            <field>Address</field>
        </outputAssignments>
    </recordLookups>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <filterLogic>or</filterLogic>
        <filters>
            <field>wbl_Status__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>JobBoard__Job_Application__c</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>GetOrgWideEmailAddress</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>ApplicantRejectedNotifcation</name>
        <label>Send Application Rejected Notification to Applicant</label>
        <locationX>50</locationX>
        <locationY>492</locationY>
        <flowName>wbl_SendApplicationRejectedNotificationToApplicant</flowName>
        <inputAssignments>
            <name>jobApplicationId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>orgWideEmail</name>
            <value>
                <elementReference>orgWideEmail</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>InterviewReadyApplicantNotification</name>
        <label>Send Interview Ready Notification to Applicant</label>
        <locationX>314</locationX>
        <locationY>492</locationY>
        <flowName>wbl_SendInterviewReadyNotificationToApplicant</flowName>
        <inputAssignments>
            <name>jobApplicationId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>orgWideEmail</name>
            <value>
                <elementReference>orgWideEmail</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>SendNotificationToStaff</name>
        <label>Send Hired Job Application Notification to Staff</label>
        <locationX>710</locationX>
        <locationY>492</locationY>
        <connector>
            <targetReference>Is_Application_for_RAP_Job</targetReference>
        </connector>
        <flowName>wbl_SendHiredJobApplicationNotificationToStaff</flowName>
        <inputAssignments>
            <name>jobApplicationId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>orgWideEmail</name>
            <value>
                <elementReference>orgWideEmail</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>SendOnboardingFormToHiredApplicant</name>
        <label>Send Onboarding Form to Hired Applicant</label>
        <locationX>578</locationX>
        <locationY>708</locationY>
        <flowName>wbl_SendOnboardingFormToHiredApplicant</flowName>
        <inputAssignments>
            <name>applicationId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>orgWideEmail</name>
            <value>
                <elementReference>orgWideEmail</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <name>orgWideEmail</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>orgWideEmailId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
