<?xml version="1.0" encoding="UTF-8" ?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>SendRapEmail</name>
        <label>Send RAP Email</label>
        <locationX>440</locationX>
        <locationY>492</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>recipientAddress</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <elementReference>orgWideEmail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>emailSubject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>rapEmailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <actionCalls>
        <name>SendWblEmail</name>
        <label>Send WBL Email</label>
        <locationX>176</locationX>
        <locationY>492</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>recipientAddress</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <elementReference>orgWideEmail</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>emailSubject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>wblEmailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>61.0</apiVersion>
    <decisions>
        <name>WhatIsJobType</name>
        <label>What is the job type?</label>
        <locationX>440</locationX>
        <locationY>384</locationY>
        <defaultConnectorLabel>None of specified</defaultConnectorLabel>
        <rules>
            <name>WhatIsJobType_WBL</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Owner:User.Account.wbl_IsRapCompany__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SendWblEmail</targetReference>
            </connector>
            <label>WBL</label>
        </rules>
        <rules>
            <name>WhatIsJobType_RAP</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Owner:User.Account.wbl_IsRapCompany__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SendRapEmail</targetReference>
            </connector>
            <label>RAP</label>
        </rules>
    </decisions>
    <description>Launches once job application created. Sends notification to related job owner</description>
    <environments>Default</environments>
    <formulas>
        <name>recipientAddress</name>
        <dataType>String</dataType>
        <expression
        >IF(ISBLANK({!$Record.JobBoard__Job__r.Owner:User.Contact.Email}), {!$Record.JobBoard__Job__r.Owner:User.Email}, {!$Record.JobBoard__Job__r.Owner:User.Contact.Email})</expression>
    </formulas>
    <interviewLabel>[WBL] Job Application After Insert Trigger {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[WBL] Job Application After Insert Trigger</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetOrgWideEmailAddress</name>
        <label>Get Org Wide Email Address</label>
        <locationX>440</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>WhatIsJobType</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Setup.wbl_Settings__c.OrgWideEmailId__c</elementReference>
            </value>
        </filters>
        <object>OrgWideEmailAddress</object>
        <outputAssignments>
            <assignToReference>orgWideEmail</assignToReference>
            <field>Address</field>
        </outputAssignments>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>JobBoard__Job__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue />
            </value>
        </filters>
        <object>JobBoard__Job_Application__c</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>GetOrgWideEmailAddress</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>emailSubject</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>New job application was created under the {!$Record.JobBoard__Job__r.Name}</text>
    </textTemplates>
    <textTemplates>
        <name>jobAppPortalUrl</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>{!$Setup.wbl_Settings__c.CommunityBaseUrl__c}/detail/{!$Record.Id}</text>
    </textTemplates>
    <textTemplates>
        <name>rapEmailBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text
        >&lt;p&gt;Please review job application &lt;a href=&quot;{!jobAppPortalUrl}&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot;&gt;details&lt;/a&gt;.&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>wblEmailBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text
        >&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;Applicant details:&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0);&quot;&gt;First name: {!$Record.JobBoard__First_Name__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(0, 0, 0);&quot;&gt;Last name: {!$Record.JobBoard__Last_Name__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(0, 0, 0);&quot;&gt;Email: {!$Record.JobBoard__Email__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(0, 0, 0);&quot;&gt;Phone: {!$Record.JobBoard__Mobile__c}&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Application also available by &lt;a href=&quot;{!jobAppPortalUrl}&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot;&gt;this link&lt;/a&gt;.&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>orgWideEmail</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>orgWideEmailId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
