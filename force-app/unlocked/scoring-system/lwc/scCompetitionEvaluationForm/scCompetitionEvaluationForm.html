<template>
    <div class="slds-grid slds-wrap slds-gutters slds-grid_align-spread">
        <div class="slds-col sc-print-only">
            {evaluation.conferenceName} - {evaluation.competitionName} ({evaluation.competitionDivision})
        </div>
        <div class="slds-col sc-print-only">{labels.scScorecardIssueDate} {currentDateTime}</div>
        <div class="slds-col slds-size_1-of-1 slds-p-top_large">
            <h2 class="slds-text-heading_medium slds-align_absolute-center slds-m-bottom_medium">
                <template lwc:if={isViewMode}>{evaluation.name}</template>
                <template lwc:else>
                    <template lwc:if={evaluation.isForTeam}
                        >{labels.scTeamHeader}&nbsp;{evaluation.competitorCode}</template
                    >
                    <template lwc:else>{labels.scCompetitorHeader}&nbsp;{evaluation.competitorCode}</template>
                </template>
            </h2>
        </div>
    </div>
    <template for:each={evaluation.topics} for:item="topic">
        <article
            key={topic.scoreTopicId}
            class="slds-card slds-p-around_medium sc-print-padding_small sc-print-border_none"
        >
            <div class="slds-grid slds-gutters slds-m-bottom_small">
                <div class="slds-col slds-size_1-of-3 sc-topic-name">{topic.name}</div>
                <div class="slds-col slds-size_1-of-3 sc-topic-description">{topic.description}</div>
                <div class="slds-col slds-size_1-of-3 slds-grid slds-grid_align-end">
                    <span class="sc-topic-score">
                        <span
                            lwc:if={topic.isCommentRequired}
                            class="slds-text-color_destructive slds-text-body_small slds-m-right_small"
                            >{labels.scTopicLeaveComment}</span
                        >
                        <lightning-helptext
                            lwc:if={topic.isDeduction}
                            icon-name="utility:ban"
                            content={labels.scDeductionHelp}
                            class="slds-m-right_x-small"
                        ></lightning-helptext>
                        <template lwc:if={isEditMode}>
                            <lightning-input
                                lwc:if={topic.isScoreEditable}
                                data-score-topic-id={topic.scoreTopicId}
                                type="number"
                                min="0"
                                max={topic.maxScore}
                                value={topic.score}
                                onchange={handleTopicScoreChange}
                                variant="label-hidden"
                                class="sc-score-input"
                                required={isAllTopicsRequired}
                            ></lightning-input>
                            <template lwc:else>{topic.score}</template>
                            <span>&sol;{topic.maxScore}</span>
                        </template>
                        <template lwc:if={isViewMode}> {topic.score}&sol;{topic.maxScore} </template>
                        <template lwc:if={isTemplateMode}>
                            <template lwc:if={topic.isScoreEditable}
                                >&lowbar;&lowbar;&lowbar;&sol;{topic.maxScore}</template
                            >
                            <template lwc:else>{labels.scMaxScore}&nbsp;{topic.maxScore}</template>
                        </template>
                    </span>
                </div>
            </div>
            <template lwc:if={topic.subtopics} for:each={topic.subtopics} for:item="subtopic">
                <div key={subtopic.scoreTopicId} class="slds-grid slds-gutters slds-m-bottom_small">
                    <div class="slds-col slds-size_1-of-3 sc-subtopic-name">{subtopic.name}</div>
                    <div class="slds-col slds-size_1-of-3 sc-subtopic-description">{subtopic.description}</div>
                    <div class="slds-col slds-size_1-of-3 slds-grid slds-grid_align-end">
                        <span class="sc-subtopic-score">
                            <span
                                lwc:if={subtopic.isCommentRequired}
                                class="slds-text-color_destructive slds-text-body_small slds-m-right_small"
                                >{labels.scTopicLeaveComment}</span
                            >
                            <lightning-helptext
                                lwc:if={subtopic.isDeduction}
                                icon-name="utility:ban"
                                content={labels.scDeductionHelp}
                                class="slds-m-right_x-small"
                            ></lightning-helptext>
                            <template lwc:if={isEditMode}>
                                <lightning-input
                                    data-score-topic-id={subtopic.scoreTopicId}
                                    type="number"
                                    min="0"
                                    max={subtopic.maxScore}
                                    value={subtopic.score}
                                    onchange={handleTopicScoreChange}
                                    variant="label-hidden"
                                    class="sc-score-input"
                                    required={isAllTopicsRequired}
                                ></lightning-input>
                                <span>&sol;{subtopic.maxScore}</span>
                            </template>
                            <template lwc:if={isViewMode}>{subtopic.score}&sol;{subtopic.maxScore}</template>
                            <template lwc:if={isTemplateMode}
                                >&lowbar;&lowbar;&lowbar;&sol;{subtopic.maxScore}</template
                            >
                        </span>
                    </div>
                </div>
            </template>
        </article>
    </template>
    <template lwc:if={isViewMode}> </template>
    <lightning-textarea
        lwc:else
        label={labels.scJudgeCommentsField}
        onchange={handleCommentChange}
        value={evaluation.currentJudgeComment.body}
        class="slds-m-vertical_small"
    >
    </lightning-textarea>
    <div if:false={isTemplateMode} class="slds-grid slds-grid_align-end slds-m-vertical_medium">
        <div class="sc-total-score slds-col">{labels.scTotalPointsHeader}&nbsp;{totalScore}&sol;{totalMaxScore}</div>
    </div>
</template>
