<?xml version="1.0" encoding="UTF-8" ?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <isExposed>true</isExposed>
    <masterLabel>[SC] Lock Competition Button</masterLabel>
    <targets>
        <target>lightningCommunity__Default</target>
        <target>lightningCommunity__Page</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightningCommunity__Default">
            <property label="Competition Id" name="competitionId" type="String" default="{!recordId}" />
            <property label="Additional Text" name="additionalText" type="String" />
            <property
                name="stretch"
                label="Full width button"
                description="Take up the entire available width"
                type="Boolean"
                default="false"
            />
            <property
                name="variant"
                label="Style of the button"
                type="String"
                default="brand"
                datasource="base, neutral, brand, brand-outline, destructive, destructive-text, inverse, success"
            />
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>
