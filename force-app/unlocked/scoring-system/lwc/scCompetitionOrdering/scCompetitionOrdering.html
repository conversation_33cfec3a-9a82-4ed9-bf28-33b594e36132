<template>
    <lightning-spinner lwc:if={isSpinnerShown}></lightning-spinner>
    <c-sc-message-banner lwc:if={bannerConfig} config={bannerConfig}></c-sc-message-banner>
    <div if:true={competitionGroups} class="slds-grid slds-gutters">
        <div class="slds-col slds-size_1-of-2">
            <lightning-combobox
                label="Order By"
                value={orderBy}
                options={orderByOptions}
                onchange={handleOrderByChange}
            ></lightning-combobox>
            <lightning-combobox
                label="Order Direction"
                value={orderDirection}
                options={orderDirectionOptions}
                onchange={handleOrderDirectionChange}
                class="slds-m-vertical_small"
            ></lightning-combobox>
            <div class="slds-grid slds-grid_align-spread">
                <lightning-button onclick={handleOrderClick} variant="brand-outline" label="Order"></lightning-button>
                <lightning-button onclick={handleSaveClick} variant="brand" label="Save"></lightning-button>
            </div>
        </div>
        <div class="slds-col slds-size_1-of-2">
            <template for:each={competitionGroups} for:item="competitionGroup" for:index="index">
                <article
                    onclick={handleCompetitionClick}
                    key={competitionGroup.competitionName}
                    class="slds-card slds-card_boundary slds-p-around_xx-small"
                    data-index={index}
                    data-name={competitionGroup.competitionName}
                >
                    <p class="slds-p-left_small">
                        <span class="slds-text-color_weak slds-m-right_small"
                            >&#35;{competitionGroup.presentationOrder}</span
                        >{competitionGroup.competitionName}
                    </p>
                </article>
            </template>
            <section
                lwc:if={selectedCompetitionGroup}
                lwc:ref="orderPopover"
                class="sc-popover slds-popover slds-nubbin_top"
                role="dialog"
            >
                <lightning-button-icon
                    onclick={handleClosePopoverClick}
                    icon-name="utility:close"
                    variant="bare"
                    alternative-text="Close"
                    class="slds-float_right slds-popover__close"
                    title="Close"
                ></lightning-button-icon>
                <div class="slds-popover__body">
                    <div class="slds-media">
                        <div class="slds-media__body">
                            <header class="slds-popover__header">
                                <h2 class="slds-text-heading_small">Competition Order</h2>
                            </header>
                            <lightning-input
                                type="number"
                                min="1"
                                max={competitionGroupCount}
                                value={selectedCompetitionGroup.presentationOrder}
                                lwc:ref="orderInput"
                                variant="label-hidden"
                                required
                            ></lightning-input>
                        </div>
                    </div>
                </div>
                <footer class="slds-popover__footer">
                    <div class="slds-grid slds-grid_vertical-align-center">
                        <lightning-button
                            onclick={handleSetCompetitionOrderClick}
                            label="Set"
                            variant="brand"
                        ></lightning-button>
                    </div>
                </footer>
            </section>
        </div>
    </div>
</template>
