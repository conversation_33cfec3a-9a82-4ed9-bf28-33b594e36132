import { LightningElement, api } from 'lwc';
import getCompetitorsForRanksTable from '@salesforce/apex/sc_CompetitorsRankedTableController.getCompetitorsForRanksTable';
import { getOrdinalNumber } from './utils';
import { labels } from './labels';
import { showErrors } from 'c/errorHandler';

export default class ScCompetitorsRankedTable extends LightningElement {
    @api competitionId;
    @api get competitors() {
        return this._competitors;
    }
    set competitors(value) {
        this._competitors = value;
    }

    _competitors;
    labels = labels;

    connectedCallback() {
        this.setCompetitors();
    }

    setCompetitors() {
        if (this.competitors) {
            this._competitors = this.setDisplayedRank(this.sortCompetitorsByRank(this.competitors));
            return;
        }

        getCompetitorsForRanksTable({ competitionId: this.competitionId })
            .then((data) => {
                this._competitors = this.setDisplayedRank(this.sortCompetitorsByRank(data));
            })
            .catch((error) => {
                showErrors(this, error);
            });
    }

    sortCompetitorsByRank(competitors) {
        return [...competitors].sort((firstCompetitor, secondCompetitor) => {
            if (firstCompetitor.rank > secondCompetitor.rank) {
                return 1;
            }

            if (firstCompetitor.rank < secondCompetitor.rank) {
                return -1;
            }

            return 0;
        });
    }

    setDisplayedRank(competitors) {
        let newCompetitors = JSON.parse(JSON.stringify(competitors));
        newCompetitors.forEach((competitor) => {
            competitor.displayedRank = getOrdinalNumber(competitor.rank);
        });

        return newCompetitors;
    }
}
