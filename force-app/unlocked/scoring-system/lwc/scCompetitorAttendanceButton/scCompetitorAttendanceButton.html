<template>
    <div lwc:if={isButtonShown} class="slds-grid slds-grid_align-spread slds-gutters">
        <div lwc:if={description} class="slds-col slds-size_1-of-1 slds-medium-size_2-of-3">
            <p class="slds-text-heading_small">{description}</p>
        </div>
        <div class="slds-col">
            <lightning-button
                stretch={stretch}
                variant={variant}
                label={labels.scAttendMarkNonAttendeesBtn}
                onclick={handleButtonClick}
            ></lightning-button>
        </div>
    </div>
</template>
