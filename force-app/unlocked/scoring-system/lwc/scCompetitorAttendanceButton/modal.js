import { api } from 'lwc';
import LightningModal from 'lightning/modal';
import { showErrors } from 'c/errorHandler';
import LightningConfirm from 'lightning/confirm';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getCompetitors from '@salesforce/apex/sc_CompetitorAttendanceCtrl.getCompetitors';
import markNonAttendees from '@salesforce/apex/sc_CompetitorAttendanceCtrl.markNonAttendees';
import scAttendSaveBtn from '@salesforce/label/c.sclAttendSaveBtn';
import scCancelBtn from '@salesforce/label/c.sclCancelBtn';
import scAttendNonAttendeeField from '@salesforce/label/c.sclAttendNonAttendeeField';
import scAttendMarkConfirmMes from '@salesforce/label/c.sclAttendMarkConfirmMes';
import scGenericSaveSuccessMes from '@salesforce/label/c.sclGenericSaveSuccessMes';

export default class extends LightningModal {
    @api recordId;
    isSpinnerShown;
    competitors;
    idToNonAttendee = {};

    labels = {
        scAttendSaveBtn,
        scCancelBtn,
        scAttendNonAttendeeField,
        scAttendMarkConfirmMes,
        scGenericSaveSuccessMes
    };

    get isSaveDisabled() {
        return !this.idToNonAttendee || Object.values(this.idToNonAttendee) < 1;
    }

    connectedCallback() {
        this.isSpinnerShown = true;
        this.fetchCompetitors().finally(() => (this.isSpinnerShown = false));
    }

    fetchCompetitors() {
        return getCompetitors({ competitionId: this.recordId })
            .then((competitors) => {
                this.competitors = competitors.map((c) => ({
                    ...c,
                    isNonAttendeeInputDisabled: c.isNonAttendee
                }));
            })
            .catch((error) => showErrors(this, error));
    }

    save() {
        const competitorIds = Object.keys(this.idToNonAttendee);
        return markNonAttendees({ competitionId: this.recordId, competitorIds })
            .then(() => {
                this.dispatchEvent(
                    new ShowToastEvent({
                        message: this.labels.scGenericSaveSuccessMes,
                        variant: 'success'
                    })
                );
                this.close();
            })
            .catch((error) => showErrors(this, error));
    }

    handleNonAttendeeChange(event) {
        const competitorId = event.target.dataset.id;
        const competitor = this.competitors.find((c) => c.id === competitorId);

        if (event.target.checked) {
            competitor.isNonAttendee = true;
            this.idToNonAttendee[competitorId] = competitor;
        } else {
            competitor.isNonAttendee = false;
            delete this.idToNonAttendee[competitorId];
        }

        this.idToNonAttendee = { ...this.idToNonAttendee };
    }

    handleCancelClick() {
        this.close();
    }

    async handleSaveClick() {
        const competitorCodes = Object.values(this.idToNonAttendee).map((c) => c.individualCode);
        const message = this.labels.scAttendMarkConfirmMes.replace('{0}', competitorCodes.join(', '));
        const isSaveConfirmed = await LightningConfirm.open({
            variant: 'headerless',
            message
        });

        if (!isSaveConfirmed) {
            return;
        }

        this.isSpinnerShown = true;
        this.save()
            .then(() => this.close())
            .catch((error) => showErrors(this, error))
            .finally(() => (this.isSpinnerShown = false));
    }
}
