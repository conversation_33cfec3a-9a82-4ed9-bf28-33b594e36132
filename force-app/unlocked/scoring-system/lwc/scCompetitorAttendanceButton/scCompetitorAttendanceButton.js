import { LightningElement, api } from 'lwc';
import isAttendanceEditAvailable from '@salesforce/apex/sc_CompetitorAttendanceCtrl.isAttendanceEditAvailable';
import modal from './modal';
import scAttendMarkNonAttendeesBtn from '@salesforce/label/c.sclAttendMarkNonAttendeesBtn';

export default class extends LightningElement {
    @api recordId; //competition id
    @api description;
    @api stretch;
    @api variant;
    isButtonShown = false;

    labels = { scAttendMarkNonAttendeesBtn };

    connectedCallback() {
        this.fetchEditAvailability();
    }

    fetchEditAvailability() {
        isAttendanceEditAvailable({ competitionId: this.recordId })
            .then((isAvailable) => {
                this.isButtonShown = isAvailable;
            })
            .catch(() => {
                this.isButtonShown = false;
            });
    }

    handleButtonClick() {
        modal.open({
            recordId: this.recordId,
            size: 'small'
        });
    }
}
