<?xml version="1.0" encoding="UTF-8" ?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <isExposed>true</isExposed>
    <masterLabel>[SC] Competitor Attendance Button</masterLabel>
    <targets>
        <target>lightningCommunity__Default</target>
        <target>lightningCommunity__Page</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightningCommunity__Default">
            <property label="Competition Id" name="recordId" type="String" default="{!recordId}" />
            <property
                name="description"
                label="Description"
                type="String"
                description="Text displayed before the button"
            />
            <property name="stretch" label="Full width button" type="Boolean" default="false" />
            <property
                name="variant"
                label="Style of the button"
                type="String"
                default="brand"
                datasource="base, neutral, brand, brand-outline, destructive, destructive-text, inverse, success"
            />
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>
