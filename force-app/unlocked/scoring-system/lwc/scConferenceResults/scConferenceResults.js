import { LightningElement } from 'lwc';
import getConferenceRankings from '@salesforce/apex/sc_ConferenceResultsCtrl.getConferenceRankings';
import getConferences from '@salesforce/apex/sc_ConferenceResultsCtrl.getConferences';
import getPresentationSettings from '@salesforce/apex/sc_ConferenceResultsCtrl.getPresentationSettings';
import { showErrors } from 'c/errorHandler';
import SheetJsResource from '@salesforce/resourceUrl/sc_SheetJS';
import PptxGenResource from '@salesforce/resourceUrl/sc_PptxGenJS';
import StylesResource from '@salesforce/resourceUrl/sc_ScoringSystemStyles';
import { loadScript, loadStyle } from 'lightning/platformResourceLoader';
import { labels } from './labels';
import { exportToCsv, exportToXlsx, PptxExporter } from './export';
import { getOrdinalNumber } from './utils';

const MAX_WINNER_RANK = 3;
const WINNERS_ONLY = 'winners';

export default class extends LightningElement {
    conferenceOptions = [];
    conferenceId;
    competitorLimitOptions = [
        { value: WINNERS_ONLY, label: 'Winners only' },
        { value: 'all', label: 'All' }
    ];
    competitorLimit = this.competitorLimitOptions[0].value;
    isCompetitorNamesShown = false;
    isScoresShown = false;
    isSpinnerShown = false;
    conferenceRanking;
    displayedConferenceRanking;
    presentationSettings;
    sheetJsLoadPromise;
    pptxGenLoadPromise;
    presentationSettingsPromise;
    labels = labels;

    connectedCallback() {
        this.isSpinnerShown = true;
        this.sheetJsLoadPromise = loadScript(this, SheetJsResource);
        this.pptxGenLoadPromise = loadScript(this, PptxGenResource);
        this.presentationSettingsPromise = this.fetchPresentationSettings();
        loadStyle(this, StylesResource + '/printStyles.css');
        this.fetchConferences().finally(() => (this.isSpinnerShown = false));
    }

    fetchConferences() {
        return getConferences()
            .then((conferences) => {
                this.conferenceOptions = conferences.map((conf) => ({ value: conf.id, label: conf.name }));
            })
            .catch((err) => showErrors(this, err));
    }

    fetchConferenceRankings() {
        return getConferenceRankings({
            conferenceId: this.conferenceId
        })
            .then((conferenceRankings) => {
                this.conferenceRanking = conferenceRankings;
                this.setDisplayedConferenceRanking();
            })
            .catch((err) => showErrors(this, err));
    }

    fetchPresentationSettings() {
        return getPresentationSettings()
            .then((settings) => (this.presentationSettings = settings))
            .catch((err) => showErrors(this, err));
    }

    handleConferenceChange(event) {
        this.conferenceId = event.detail.value;
    }

    handleCompetitorLimitChange(event) {
        this.competitorLimit = event.detail.value;
        this.setDisplayedConferenceRanking();
    }

    handleDisplayNamesChange(event) {
        this.isCompetitorNamesShown = event.target.checked;
        this.setDisplayedConferenceRanking();
    }

    handleDisplayScoresChange(event) {
        this.isScoresShown = event.target.checked;
    }

    handleLoadClick() {
        const requiredInputs = this.template.querySelectorAll('.sc-required');
        const canLoadConference = Array.from(requiredInputs).every((input) => input.reportValidity());

        if (!canLoadConference) {
            return;
        }

        this.isSpinnerShown = true;
        this.fetchConferenceRankings().finally(() => (this.isSpinnerShown = false));
    }

    handleExportPdfClick() {
        //large file inside file anchor may significantly slow down print
        this.cleanFileAnchor();
        window.print();
    }

    handleExportCsvClick() {
        exportToCsv(this.displayedConferenceRanking, this.refs.fileAnchor, { isScoresShown: this.isScoresShown });
    }

    handleExportXlsClick() {
        this.sheetJsLoadPromise.then(() =>
            exportToXlsx(this.displayedConferenceRanking, { isScoresShown: this.isScoresShown })
        );
    }

    handleExportPptxClick() {
        this.isSpinnerShown = true;

        Promise.all([this.pptxGenLoadPromise, this.presentationSettingsPromise]).then(() => {
            new PptxExporter(this.displayedConferenceRanking, this.presentationSettings, this.refs.fileAnchor)
                .export()
                .finally(() => (this.isSpinnerShown = false));
        });
    }

    setDisplayedConferenceRanking() {
        if (!this.conferenceRanking) {
            return;
        }

        const displayedRanking = JSON.parse(JSON.stringify(this.conferenceRanking));
        const divisionNames = this.getUniqueDivisionNames();

        for (const competitionGroup of displayedRanking.competitionGroups) {
            const divisionNameToCompetition = new Map();

            for (const divisionName of divisionNames) {
                divisionNameToCompetition.set(divisionName, { divisionName, hasCompetitors: false });
            }

            for (const competition of competitionGroup.competitions) {
                divisionNameToCompetition.set(competition.divisionName, competition);
                competition.hasCompetitors = !!competition.competitors?.length;
                const divisionCompetitors = [];

                for (const competitor of competition.competitors) {
                    competitor.displayedName = this.getCompetitorDisplayName(competitor);
                    competitor.displayedRank = getOrdinalNumber(competitor.rank);
                    if (competitor.isTeam) {
                        competitor.teamMembers = competitor.teamMembers.map((m) => {
                            m.displayedName = this.getCompetitorDisplayName(m);
                            return m;
                        });
                    }

                    divisionCompetitors.push(competitor);
                    if (this.competitorLimit === WINNERS_ONLY && divisionCompetitors.length === MAX_WINNER_RANK) {
                        break;
                    }
                }

                competition.competitors = divisionCompetitors;
            }

            competitionGroup.competitions = Array.from(divisionNameToCompetition.values());
        }

        this.displayedConferenceRanking = displayedRanking;
    }

    getUniqueDivisionNames() {
        const allDivisions = this.conferenceRanking.competitionGroups.flatMap((cg) => cg.competitions);
        const divisionNameSet = allDivisions.reduce((nameSet, competition) => {
            nameSet.add(competition.divisionName);
            return nameSet;
        }, new Set());

        return Array.from(divisionNameSet.values());
    }

    cleanFileAnchor() {
        const fileAnchor = this.refs.fileAnchor;
        fileAnchor.href = '';
    }

    getCompetitorDisplayName(competitor) {
        if (competitor.isTeam) {
            return competitor.code;
        }

        return this.isCompetitorNamesShown ? competitor.name : competitor.individualCode;
    }
}
