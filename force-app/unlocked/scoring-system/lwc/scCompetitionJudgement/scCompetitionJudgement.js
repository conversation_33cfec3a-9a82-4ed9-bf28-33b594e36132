import { LightningElement, api, wire } from 'lwc';
import { CurrentPageReference, NavigationMixin } from 'lightning/navigation';
import getCompetitionEvaluations from '@salesforce/apex/sc_CompetitionJudgementCtrl.getCompetitionEvaluations';
import saveEvaluation from '@salesforce/apex/sc_CompetitionJudgementCtrl.saveEvaluation';
import { showErrors } from 'c/errorHandler';
import getRelatedCompetitions from '@salesforce/apex/sc_CompetitionJudgementCtrl.getRelatedCompetitions';
import { labels } from './labels';
import { TYPE_INFO as INFO_BANNER, TYPE_ERROR as ERROR_BANNER } from 'c/scMessageBanner';

const ID_PLACEHOLDER = '__id__';

export default class extends NavigationMixin(LightningElement) {
    _competitionId;
    competition = {};
    evaluations;
    _currentEvaluationIndex = 0;
    isSpinnerShown = false;
    pageRef;
    pageRefPromiseResolve;
    pageRefPromise = new Promise((resolve) => (this.pageRefPromiseResolve = resolve));
    judgementPageUrlTemplate;
    labels = labels;
    bannerConfig;

    @api get competitionId() {
        return this._competitionId;
    }

    set competitionId(value) {
        this._competitionId = value;
    }

    get currentEvaluationIndex() {
        return this._currentEvaluationIndex;
    }

    set currentEvaluationIndex(value) {
        if (!this.evaluations?.length) {
            return;
        }

        this.updateCurrentEvaluationFromForm();

        if (value >= this.evaluations.length) {
            this._currentEvaluationIndex = this.evaluations.length - 1;
        } else {
            this._currentEvaluationIndex = value;
        }

        this.setCurrentFlagOnEvaluation();
    }

    get currentEvaluation() {
        if (!this.evaluations?.length) {
            return null;
        }
        return this.evaluations[this.currentEvaluationIndex];
    }

    get isAvailableCompetitionsShown() {
        return this.availableCompetitions?.length;
    }

    get isLastEvaluation() {
        return this.currentEvaluationIndex === this.evaluations?.length - 1;
    }

    get isTeamCompetition() {
        return this.evaluations?.[0]?.isForTeam;
    }

    get competitionName() {
        return this.evaluations?.[0]?.competitionName;
    }

    get isEvaluationsAvailable() {
        return this.evaluations?.length;
    }

    @wire(CurrentPageReference)
    handlePageRefLoad(pageRef) {
        this.pageRef = pageRef;
        if (this.pageRef?.state?.competition) {
            this._competitionId = this.pageRef?.state?.competition;
        }
        this.pageRefPromiseResolve();
    }

    connectedCallback() {
        this.isSpinnerShown = true;
        this.pageRefPromise.then(() => {
            Promise.all([this.fetchRelatedCompetitions(), this.fetchEvaluations()]).finally(
                () => (this.isSpinnerShown = false)
            );
        });
    }

    fetchEvaluations() {
        return getCompetitionEvaluations({ competitionId: this.competitionId })
            .then((evaluations) => {
                this.evaluations = evaluations;
                this.validateEvaluations(evaluations);
            })
            .catch((err) => {
                this.bannerConfig = {
                    type: ERROR_BANNER,
                    message: err.body?.message || err
                };
            });
    }

    async fetchRelatedCompetitions() {
        await this.setJudgementPageUrlTemplate();

        return getRelatedCompetitions({ competitionId: this.competitionId })
            .then((competitions) => {
                this.availableCompetitions = competitions.map((c) => {
                    c.isCurrent = c.id === this.competitionId;
                    if (c.isCurrent) {
                        this.competition = c;
                    }
                    c.judgementPageUrl = this.judgementPageUrlTemplate.replace(ID_PLACEHOLDER, c.id);
                    return c;
                });
            })
            .catch((err) => showErrors(this, err));
    }

    handleCompetitorClick(event) {
        this.currentEvaluationIndex = parseInt(event.currentTarget.dataset.index, 10);
    }

    updateCurrentEvaluationFromForm() {
        const form = this.refs.form;
        if (!form) {
            return;
        }
        this.evaluations[this.currentEvaluationIndex] = JSON.parse(JSON.stringify(form.evaluation));
    }

    handleSaveClick() {
        this.saveEvaluation();
    }

    handleSaveNextClick() {
        this.saveEvaluation().then(() => this.switchToNextEvaluation());
    }

    saveEvaluation() {
        const form = this.refs.form;

        if (!form.reportValidity()) {
            return Promise.reject();
        }

        this.updateCurrentEvaluationFromForm();
        this.isSpinnerShown = true;

        return saveEvaluation({ evalDto: this.currentEvaluation })
            .then((evaluation) => {
                this.refs.form.removeEvaluationFromLocalStorage();
                this.evaluations[this.currentEvaluationIndex] = { ...evaluation, isCurrent: true };
                this.validateEvaluations([this.evaluations[this.currentEvaluationIndex]]);
            })
            .catch((err) => showErrors(this, err))
            .finally(() => (this.isSpinnerShown = false));
    }

    switchToNextEvaluation() {
        this.currentEvaluationIndex += 1;
    }

    setJudgementPageUrlTemplate() {
        const judgementPageRef = JSON.parse(JSON.stringify(this.pageRef));
        Object.assign(judgementPageRef, { state: { competition: ID_PLACEHOLDER } });

        return this[NavigationMixin.GenerateUrl](judgementPageRef).then((url) => {
            this.judgementPageUrlTemplate = url;
        });
    }

    setCurrentFlagOnEvaluation() {
        this.evaluations = this.evaluations.map((e, i) => {
            e.isCurrent = i === this.currentEvaluationIndex;
            return e;
        });
    }

    validateEvaluations(evaluations) {
        const hasNoCompetitors = !evaluations?.length;
        if (hasNoCompetitors) {
            this.bannerConfig = {
                type: INFO_BANNER,
                message: this.labels.scNoActiveCompetitorsMes
            };
            return;
        }

        for (const evaluation of evaluations) {
            evaluation.hasTopics = evaluation.topics?.length;
            if (!evaluation.hasTopics) {
                evaluation.bannerConfig = {
                    type: INFO_BANNER,
                    message: this.labels.scNoTopicAssignmentMes
                };
            }
        }
    }
}
