import getCompetitions from '@salesforce/apex/sc_JudgementProgressCtrl.getCompetitions';
import { LightningElement, api } from 'lwc';
import scNoCompetitionsMes from '@salesforce/label/c.sclNoCompetitionsMes';
import { TYPE_INFO as INFO_BANNER, TYPE_ERROR as ERROR_BANNER } from 'c/scMessageBanner';

export default class extends LightningElement {
    @api recordId; //conference id
    competitionGroups;
    competitionCountPerStatus;
    divisionsNames;
    isSpinnerShown;

    connectedCallback() {
        if (!this.recordId) {
            return;
        }

        this.isSpinnerShown = true;
        this.fetchCompetitions().finally(() => (this.isSpinnerShown = false));
    }

    handleRefreshClick() {
        this.isSpinnerShown = true;
        this.fetchCompetitions().finally(() => (this.isSpinnerShown = false));
    }

    fetchCompetitions() {
        return getCompetitions({ conferenceId: this.recordId })
            .then((competitions) => {
                if (!competitions?.length) {
                    this.bannerConfig = {
                        type: INFO_BANNER,
                        message: scNoCompetitionsMes
                    };
                    return;
                }
                const divisionNames = new Set();
                const nameToCompetitions = {};
                const nameToCompetitionCountPerStatus = {};

                for (const competition of competitions) {
                    divisionNames.add(competition.divisionName);
                    nameToCompetitions[competition.name] = nameToCompetitions[competition.name] || {};
                    nameToCompetitions[competition.name][competition.divisionName] = competition;
                    if (!nameToCompetitionCountPerStatus[competition.judgementStatus]) {
                        nameToCompetitionCountPerStatus[competition.judgementStatus] = {
                            name: competition.judgementStatus,
                            count: 0
                        };
                    }
                    nameToCompetitionCountPerStatus[competition.judgementStatus].count += 1;
                }

                const competitionGroups = [];

                for (const competitionName of Object.keys(nameToCompetitions)) {
                    const group = { name: competitionName, divisions: [] };
                    for (const divisionName of divisionNames) {
                        const technicalChairs = nameToCompetitions[competitionName]?.[divisionName]?.technicalChairs;
                        const technicalChairNames = technicalChairs?.map((tc) => tc.name)?.join(', ');
                        group.divisions.push({
                            name: divisionName,
                            status: nameToCompetitions[competitionName]?.[divisionName]?.judgementStatus,
                            technicalChairNames
                        });
                    }
                    competitionGroups.push(group);
                }

                this.competitionGroups = competitionGroups;
                this.divisionsNames = divisionNames;
                this.competitionCountPerStatus = Object.values(nameToCompetitionCountPerStatus);
            })
            .catch((error) => {
                this.bannerConfig = {
                    type: ERROR_BANNER,
                    message: error.body?.message || error
                };
            });
    }
}
