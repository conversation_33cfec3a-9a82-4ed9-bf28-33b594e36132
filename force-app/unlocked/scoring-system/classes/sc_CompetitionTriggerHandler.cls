public with sharing class sc_CompetitionTriggerHandler {
    private System.TriggerOperation triggerOperation;
    private List<cmp_Competition__c> competitionsNew;
    private Map<Id, cmp_Competition__c> idToCompetitionOld;

    public sc_CompetitionTriggerHandler(
        System.TriggerOperation triggerOperation,
        List<cmp_Competition__c> competitionsNew,
        Map<Id, cmp_Competition__c> idToCompetitionOld
    ) {
        this.triggerOperation = triggerOperation;
        this.competitionsNew = competitionsNew;
        this.idToCompetitionOld = idToCompetitionOld;
    }

    public void run() {
        switch on this.triggerOperation {
            when BEFORE_INSERT {
                beforeInsert();
            }
            when BEFORE_UPDATE {
                beforeUpdate();
            }
            when AFTER_UPDATE {
                afterUpdate();
            }
        }
    }

    private void beforeInsert() {
        setDefaultResultPublishDates();
    }

    private void beforeUpdate() {
        unlockEvaluationsOnRoundUpgrade();
    }

    private void afterUpdate() {
        handleConditionsForRankingUpdate();
        updateCompetitionsJudgementProgress();
    }

    private void setDefaultResultPublishDates() {
        Decimal publishPeriod = sc_ScoringSystemSettings__c.getInstance()?.CompetitionResultsPublishPeriod__c;

        if (publishPeriod == null) {
            return;
        }

        Set<Id> requestConferenceIds = new Set<Id>();

        for (cmp_Competition__c competition : this.competitionsNew) {
            if (competition.sc_ResultsPublishDate__c == null && competition.Conference__c != null) {
                requestConferenceIds.add(competition.Conference__c);
            }
        }

        if (requestConferenceIds.isEmpty()) {
            return;
        }

        Map<Id, cm_Conference__c> idToConference = new Map<Id, cm_Conference__c>(
            [SELECT EndDate__c FROM cm_Conference__c WHERE Id IN :requestConferenceIds WITH USER_MODE]
        );

        for (cmp_Competition__c competition : this.competitionsNew) {
            if (competition.sc_ResultsPublishDate__c == null && competition.Conference__c != null) {
                Date conferenceEndDate = idToConference.get(competition.Conference__c)?.EndDate__c?.dateGmt();
                if (conferenceEndDate != null) {
                    competition.sc_ResultsPublishDate__c = conferenceEndDate.addDays(Integer.valueOf(publishPeriod));
                }
            }
        }
    }

    private void unlockEvaluationsOnRoundUpgrade() {
        List<String> competitionRoundOptions = sc_CompetitionService.getCompetitionRoundOptions();

        for (cmp_Competition__c competitionNew : this.competitionsNew) {
            cmp_Competition__c competitionOld = this.idToCompetitionOld.get(competitionNew.Id);
            Integer newRoundIndex = competitionRoundOptions.indexOf(competitionNew.sc_CurrentRound__c);
            Integer oldRoundIndex = competitionRoundOptions.indexOf(competitionOld.sc_CurrentRound__c);

            if (newRoundIndex > oldRoundIndex) {
                competitionNew.sc_IsEvaluationsLocked__c = false;
            }
        }
    }

    private void handleConditionsForRankingUpdate() {
        Set<Id> competitionIdsForUpdate = new Set<Id>();
        Map<Id, String> competitionIdToPreviousRound = new Map<Id, String>();
        Map<Id, cmp_Competition__c> idToCompetitionNew = new Map<Id, cmp_Competition__c>(this.competitionsNew);

        for (cmp_Competition__c competitionNew : this.competitionsNew) {
            cmp_Competition__c competitionOld = this.idToCompetitionOld.get(competitionNew.Id);
            Boolean isRoundChanged = competitionNew.sc_CurrentRound__c != competitionOld.sc_CurrentRound__c;
            if (isRoundChanged) {
                competitionIdToPreviousRound.put(competitionNew.Id, competitionOld.sc_CurrentRound__c);
                competitionIdsForUpdate.add(competitionNew.Id);
                continue;
            }

            Boolean isBecameLocked =
                !competitionOld.sc_IsEvaluationsLocked__c && competitionNew.sc_IsEvaluationsLocked__c;
            if (isBecameLocked) {
                competitionIdsForUpdate.add(competitionNew.Id);
            }
        }

        if (competitionIdsForUpdate.isEmpty()) {
            return;
        }

        List<cmp_Participant__c> competitors = sc_CompetitorRankingService.retrieveCompetitorsWithScore(
            competitionIdsForUpdate,
            competitionIdToPreviousRound,
            System.AccessLevel.SYSTEM_MODE
        );

        Map<String, List<SObject>> competitionIdToCompetitors = sc_CollectionHelper.mapRecordsToField(
            'Competition__c',
            competitors
        );

        List<String> competitionRoundOptions = sc_CompetitionService.getCompetitionRoundOptions();

        for (String competitionId : competitionIdToCompetitors.keySet()) {
            List<cmp_Participant__c> competitionCompetitors = (List<cmp_Participant__c>) competitionIdToCompetitors.get(
                competitionId
            );
            cmp_Competition__c competition = idToCompetitionNew.get((Id) competitionId);
            Integer eliminationRank = (Integer) competition.sc_NumberOfRoundWinners__c;

            if (eliminationRank > 0 && competitionIdToPreviousRound.containsKey(competitionId)) {
                Integer previousRoundIndex = competitionRoundOptions.indexOf(
                    competitionIdToPreviousRound.get(competition.Id)
                );
                Integer currentRoundIndex = competitionRoundOptions.indexOf(competition.sc_CurrentRound__c);

                if (currentRoundIndex > previousRoundIndex) {
                    sc_CompetitorRankingService.markEliminatedCompetitors(
                        competitionCompetitors,
                        eliminationRank,
                        competitionIdToPreviousRound
                    );
                }
            }
        }

        WS.updateRecords(competitors);
    }

    private void updateCompetitionsJudgementProgress() {
        Set<Id> competitionIdsForUpdate = new Set<Id>();

        for (cmp_Competition__c competitionNew : this.competitionsNew) {
            cmp_Competition__c competitionOld = this.idToCompetitionOld.get(competitionNew.Id);

            if (competitionNew.sc_CurrentRound__c != competitionOld.sc_CurrentRound__c) {
                competitionIdsForUpdate.add(competitionNew.Id);
            }
        }

        if (!competitionIdsForUpdate.isEmpty()) {
            sc_JudgementProgressService progressService = new sc_JudgementProgressService(competitionIdsForUpdate);
            progressService.updateCompetitionStatuses();
        }
    }
}