public with sharing class sc_CompetitorsRankedTableController {
    @AuraEnabled(cacheable=true)
    public static List<sc_DTO.Competitor> getCompetitorsForRanksTable(Id competitionId) {
        try {
            System.AccessLevel accessLevel = System.AccessLevel.USER_MODE;
            Id currentContactId = PortalUser.getContactId();
            Boolean isTechnicalChair =
                currentContactId != null &&
                sc_CompetitionService.isActualTechnicalChair(competitionId, currentContactId);
            if (isTechnicalChair) {
                accessLevel = System.AccessLevel.SYSTEM_MODE;
            }
            return sc_CompetitorRankingService.getCompetitorsForRanksTable(competitionId, accessLevel);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}