@IsTest
class sc_ScorecardTopicTriggerTest {
    static final String SCORECARD_NAME = 'Topic Trigger Test';

    @TestSetup
    static void setup() {
        User adminUser = sc_TestUtils.createAdminUser();
        insert adminUser;
        sc_TestUtils.assignPermissionSet(adminUser.Id, sc_TestUtils.ADMIN_PS_NAME);
        sc_TestUtils.assignPermissionSet(adminUser.Id, sc_TestUtils.CONF_ADMIN_PS_NAME);

        System.runAs(adminUser) {
            sc_Scorecard__c scorecard = sc_TestUtils.createScorecard(SCORECARD_NAME);
            insert scorecard;

            List<sc_ScorecardTopic__c> topics = sc_TestUtils.createScorecardTopics(scorecard.Id, 5);
            insert topics;

            List<sc_ScorecardTopic__c> subtopics = sc_TestUtils.createScorecardSubtopics(
                scorecard.Id,
                topics.get(0).Id,
                5
            );
            subtopics.addAll(sc_TestUtils.createScorecardSubtopics(scorecard.Id, topics.get(1).Id, 5));
            insert subtopics;
        }
    }

    @IsTest
    static void afterInsert() {
        sc_ScorecardTopic__c storedSubtopic = [
            SELECT Scorecard__c, ParentTopic__r.MaxScore__c
            FROM sc_ScorecardTopic__c
            WHERE ParentTopic__c != null AND Scorecard__r.Name = :SCORECARD_NAME
            LIMIT 1
        ];

        sc_ScorecardTopic__c newSubtopic = sc_TestUtils.createScorecardSubtopics(storedSubtopic.Scorecard__c, storedSubtopic.ParentTopic__c, 1).get(0);
        newSubtopic.MaxScore__c = 15;

        Test.startTest();
        System.runAs(sc_TestUtils.getAdminUser()) {
            insert newSubtopic;
        }
        Test.stopTest();

        sc_ScorecardTopic__c parentTopicAfterNewSubtopic = [SELECT MaxScore__c FROM sc_ScorecardTopic__c WHERE Id = :storedSubtopic.ParentTopic__c];

        Assert.areEqual(
            storedSubtopic.ParentTopic__r.MaxScore__c + newSubtopic.MaxScore__c,
            parentTopicAfterNewSubtopic.MaxScore__c,
            'After creation of new subtopic max score of parent topic max score should increase by topic max score value'
        );
    }

    @IsTest
    static void afterUpdate() {
        List<sc_ScorecardTopic__c> subtopics = [
            SELECT MaxScore__c, ParentTopic__r.MaxScore__c
            FROM sc_ScorecardTopic__c
            WHERE ParentTopic__c != null AND Scorecard__r.Name = :SCORECARD_NAME
        ];

        Map<Id, Integer> topicIdToMaxScore = new Map<Id, Integer>();

        for (sc_ScorecardTopic__c subtopic : subtopics) {
            Integer expectedMaxScore = topicIdToMaxScore.get(subtopic.ParentTopic__c) ?? 0;
            subtopic.MaxScore__c += 1;
            expectedMaxScore += Integer.valueOf(subtopic.MaxScore__c);
            topicIdToMaxScore.put(subtopic.ParentTopic__c, expectedMaxScore);
        }

        Test.startTest();
        System.runAs(sc_TestUtils.getAdminUser()) {
            update subtopics;
        }
        Test.stopTest();

        List<sc_ScorecardTopic__c> updatedParentTopics = [SELECT MaxScore__c FROM sc_ScorecardTopic__c WHERE Id IN :topicIdToMaxScore.keySet()];

        Assert.areEqual(topicIdToMaxScore.size(), updatedParentTopics.size(), 'Invalid parent topics count');

        for (sc_ScorecardTopic__c topic : updatedParentTopics) {
            Integer expectedMaxScore = topicIdToMaxScore.get(topic.Id);
            Assert.areEqual(expectedMaxScore, topic.MaxScore__c, 'Max score of parent topic should have been updated after increasing scores of it\'s child topics');
        }
    }

    @IsTest
    static void afterDelete() {
        List<sc_ScorecardTopic__c> subtopics = [
            SELECT MaxScore__c, ParentTopic__r.MaxScore__c
            FROM sc_ScorecardTopic__c
            WHERE ParentTopic__c != null AND Scorecard__r.Name = :SCORECARD_NAME
            LIMIT 3
        ];

        Map<Id, Integer> topicIdToMaxScore = new Map<Id, Integer>();

        for (sc_ScorecardTopic__c subtopic : subtopics) {
            Integer expectedMaxScore = topicIdToMaxScore.get(subtopic.ParentTopic__c) ?? Integer.valueOf(subtopic.ParentTopic__r.MaxScore__c);
            expectedMaxScore -= Integer.valueOf(subtopic.MaxScore__c);
            topicIdToMaxScore.put(subtopic.ParentTopic__c, expectedMaxScore);
        }

        Test.startTest();
        System.runAs(sc_TestUtils.getAdminUser()) {
            delete subtopics;
        }
        Test.stopTest();

        List<sc_ScorecardTopic__c> updatedParentTopics = [SELECT MaxScore__c FROM sc_ScorecardTopic__c WHERE Id IN :topicIdToMaxScore.keySet()];

        Assert.areEqual(topicIdToMaxScore.size(), updatedParentTopics.size(), 'Invalid parent topics count');

        for (sc_ScorecardTopic__c topic : updatedParentTopics) {
            Integer expectedMaxScore = topicIdToMaxScore.get(topic.Id);
            Assert.areEqual(expectedMaxScore, topic.MaxScore__c, 'After removing subtopics parent topic max score should have been decreased');
        }
    }
}