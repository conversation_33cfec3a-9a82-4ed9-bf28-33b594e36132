public with sharing class sc_ScorecardTopicAssignmentCtrl {
    @AuraEnabled
    public static Boolean getAssignmentAvailability(Id competitionId) {
        try {
            if (!isCurrentUserAuthorized(competitionId)) {
                return false;
            }

            return sc_CompetitionService.hasMasterScorecards(competitionId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static sc_DTO.ScorecardTopicAssignmentData getAssignmentData(Id competitionId) {
        if (!isCurrentUserAuthorized(competitionId)) {
            throw Error.toLWC(System.Label.sclOperationNoPermittedErr);
        }
        try {
            cmp_Competition__c competition = sc_CompetitionService.getCompetition(competitionId);
            List<cmp_Judge__c> judges = sc_CompetitionService.queryCompetitionJudgesWs(competitionId);

            Set<Id> masterScorecardIds = new Set<Id>();
            Map<String, sc_ScorecardTopicAssignment__c> topicKeyToAssignment = new Map<String, sc_ScorecardTopicAssignment__c>();

            for (sc_CompetitionScorecard__c competitionScorecard : competition.ScorecardJunctions__r) {
                if (!competitionScorecard.IsMasterScorecard__c) {
                    continue;
                }
                masterScorecardIds.add(competitionScorecard.Scorecard__c);
                for (sc_ScorecardTopicAssignment__c topicAssignment : competitionScorecard.TopicAssignments__r) {
                    topicKeyToAssignment.put(
                        competitionScorecard.Id + '' + topicAssignment.ScorecardTopic__c,
                        topicAssignment
                    );
                }
            }

            List<sc_DTO.Judge> judgeDtos = new List<sc_DTO.Judge>();

            for (cmp_Judge__c judge : judges) {
                judgeDtos.add(new sc_DTO.Judge(judge));
            }

            Map<Id, sc_Scorecard__c> idToScorecard = new Map<Id, sc_Scorecard__c>(
                sc_ScorecardService.queryScorecardsWithTopLevelTopics(masterScorecardIds)
            );

            List<sc_DTO.Evaluation> scorecardDtos = composeAssignmentScorecardDtos(
                competition.ScorecardJunctions__r,
                idToScorecard,
                topicKeyToAssignment
            );

            sc_DTO.ScorecardTopicAssignmentData dataDto = new sc_DTO.ScorecardTopicAssignmentData();
            dataDto.judges = judgeDtos;
            dataDto.scorecards = scorecardDtos;
            return dataDto;
        } catch (Exception e) {
            throw Error.toLWC(e.getMessage());
        }
    }

    @AuraEnabled
    public static void saveAssignments(List<sc_DTO.ScorecardTopicAssignment> topicAssignmentDtos) {
        try {
            List<sc_ScorecardTopicAssignment__c> recordsForUpsert = new List<sc_ScorecardTopicAssignment__c>();
            List<sc_ScorecardTopicAssignment__c> recordsForDelete = new List<sc_ScorecardTopicAssignment__c>();

            for (sc_DTO.ScorecardTopicAssignment topicAssignmentDto : topicAssignmentDtos) {
                sc_ScorecardTopicAssignment__c topicAssignment = topicAssignmentDto.toSObject();
                if (topicAssignment.CompetitionJudge__c != null) {
                    recordsForUpsert.add(topicAssignment);
                } else if (topicAssignment.Id != null) {
                    recordsForDelete.add(topicAssignment);
                }
            }

            WS.upsertRecords(recordsForUpsert);
            WS.deleteRecords(recordsForDelete);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    private static List<sc_DTO.Evaluation> composeAssignmentScorecardDtos(
        List<sc_CompetitionScorecard__c> competitionScorecards,
        Map<Id, sc_Scorecard__c> idToScorecard,
        Map<String, sc_ScorecardTopicAssignment__c> topicKeyToAssignment
    ) {
        List<sc_DTO.Evaluation> scorecardDtos = new List<sc_DTO.Evaluation>();

        for (sc_CompetitionScorecard__c competitionScorecard : competitionScorecards) {
            if (!competitionScorecard.IsMasterScorecard__c) {
                continue;
            }
            sc_Scorecard__c scorecard = idToScorecard.get(competitionScorecard.Scorecard__c);

            sc_DTO.Evaluation scorecardDto = new sc_DTO.Evaluation();
            scorecardDto.name = scorecard.Name;
            scorecardDto.competitionRound = competitionScorecard.CompetitionRound__c;
            scorecardDto.competitionScorecardId = competitionScorecard.Id;
            scorecardDto.topicAssignments = new List<sc_DTO.ScorecardTopicAssignment>();

            for (sc_ScorecardTopic__c topic : scorecard.Topics__r) {
                sc_ScorecardTopicAssignment__c topicAssignment = topicKeyToAssignment.get(
                    competitionScorecard.Id + '' + topic.Id
                );
                sc_DTO.ScorecardTopicAssignment topicAssignmentDto = new sc_DTO.ScorecardTopicAssignment();
                topicAssignmentDto.id = topicAssignment?.Id;
                topicAssignmentDto.topicId = topic.Id;
                topicAssignmentDto.topicName = topic.Name;
                topicAssignmentDto.competitionJudgeId = topicAssignment?.CompetitionJudge__c;
                topicAssignmentDto.competitionScorecardId = topicAssignment?.CompetitionScorecard__c ??
                    competitionScorecard.Id;
                scorecardDto.topicAssignments.add(topicAssignmentDto);
            }

            scorecardDtos.add(scorecardDto);
        }

        return scorecardDtos;
    }

    private static Boolean isCurrentUserAuthorized(Id competitionId) {
        Id currentContactId = PortalUser.getContactId();
        if (currentContactId == null) {
            return Schema.SObjectType.sc_ScorecardTopicAssignment__c.isCreateable();
        }
        return sc_CompetitionService.isActualTechnicalChair(competitionId, currentContactId);
    }
}