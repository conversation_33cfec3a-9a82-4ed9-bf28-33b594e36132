@IsTest
class sc_CompetitionEvaluationTriggerTest {
    static final String CONFERENCE_NAME = 'Conference';
    static final String COMPETITION_NAME = 'Competition';
    static final String SCORECARD_NAME = 'Scorecard';
    static final Integer COMPETITOR_COUNT = 2;

    @TestSetup
    static void setup() {
        User adminUser = sc_TestUtils.createAdminUser();
        insert adminUser;
        sc_TestUtils.assignPermissionSet(adminUser.Id, sc_TestUtils.ADMIN_PS_NAME);
        sc_TestUtils.assignPermissionSet(adminUser.Id, sc_TestUtils.CMP_ADMIN_PS_NAME);

        System.runAs(adminUser) {
            cm_Conference__c conference = sc_TestUtils.createConference(CONFERENCE_NAME);
            insert conference;

            List<String> divisions = sc_TestUtils.getPicklistValues(cmp_Competition__c.Division__c.getDescribe());
            cmp_Competition__c competition = sc_TestUtils.createCompetition(
                conference.Id,
                COMPETITION_NAME,
                divisions.get(0)
            );
            sc_Scorecard__c scorecard = sc_TestUtils.createScorecard(SCORECARD_NAME);

            insert new List<SObject>{ competition, scorecard };
            insert sc_TestUtils.createCompetitionScorecard(competition.Id, scorecard.Id);

            List<sc_ScorecardTopic__c> topics = sc_TestUtils.createScorecardTopics(scorecard.Id, 3);
            insert topics;

            Account judgeAccount = sc_TestUtils.createAccount('Judge Account');
            Account schoolAccount = sc_TestUtils.createAccount('School Account');
            insert new List<Account>{ judgeAccount, schoolAccount };

            Contact judgeContact = sc_TestUtils.createContact(
                judgeAccount.Id,
                'Competition',
                'Judge',
                '<EMAIL>'
            );
            judgeContact.PortalRole__c = PortalUser.ROLES.get(PortalUser.Role.JUDGE);

            List<Contact> competitorContacts = new List<Contact>{
                sc_TestUtils.createContact(
                    schoolAccount.Id,
                    'Competitor FN1',
                    'Competitor LN1',
                    '<EMAIL>'
                ),
                sc_TestUtils.createContact(
                    schoolAccount.Id,
                    'Competitor FN2',
                    'Competitor LN2',
                    '<EMAIL>'
                )
            };

            List<Contact> contactsForInsert = new List<Contact>(competitorContacts);
            contactsForInsert.add(judgeContact);
            insert contactsForInsert;

            insert sc_TestUtils.createCompetitionJudges(competition.Id, new Set<Id>{ judgeContact.Id });
            Map<Id, cm_Participant__c> contactIdToParticipant = sc_TestUtils.createConferenceParticipants(
                conference.Id,
                competitorContacts
            );
            insert contactIdToParticipant.values();

            insert sc_TestUtils.createCompetitors(competition.Id, competitorContacts, contactIdToParticipant);
        }
    }

    @IsTest
    static void afterInsertInProgressCompetition() {
        cmp_Competition__c competition = getCompetition();

        Assert.areEqual(
            sc_JudgementProgressService.PENDING_STATUS,
            competition.sc_JudgementStatus__c,
            'Competition without evaluations should have pending status'
        );

        Test.startTest();
        System.runAs(sc_TestUtils.getAdminUser()) {
            insert sc_TestUtils.createEvaluation(
                competition.ScorecardJunctions__r.get(0).Scorecard__c,
                competition.Id,
                competition.CompetitionParticipants__r.get(0).ContestantCode__c
            );
        }
        Test.stopTest();

        cmp_Competition__c conferenceAfterEvaluation = getCompetition();
        Assert.areEqual(
            sc_JudgementProgressService.IN_PROGRESS_STATUS,
            conferenceAfterEvaluation.sc_JudgementStatus__c,
            'After evaluation is created for one of competitors competition status should change to In Progress'
        );
    }

    @IsTest
    static void afterInsertCompletedCompetition() {
        cmp_Competition__c competition = getCompetition();

        List<sc_CompetitionEvaluation__c> evals = new List<sc_CompetitionEvaluation__c>();

        for (cmp_Participant__c competitor : competition.CompetitionParticipants__r) {
            evals.add(
                sc_TestUtils.createEvaluation(
                    competition.ScorecardJunctions__r.get(0).Scorecard__c,
                    competition.Id,
                    competitor.ContestantCode__c
                )
            );
        }

        Test.startTest();
        System.runAs(sc_TestUtils.getAdminUser()) {
            insert evals;
        }
        Test.stopTest();

        cmp_Competition__c conferenceAfterEvaluations = getCompetition();
        Assert.areEqual(
            sc_JudgementProgressService.COMPLETED_STATUS,
            conferenceAfterEvaluations.sc_JudgementStatus__c,
            'After evaluations is created for all competitors competition status should change to Completed'
        );
    }

    @IsTest
    static void afterDeletePendingCompetition() {
        cmp_Competition__c competition = getCompetition();
        sc_CompetitionEvaluation__c eval = sc_TestUtils.createEvaluation(
            competition.ScorecardJunctions__r.get(0).Scorecard__c,
            competition.Id,
            competition.CompetitionParticipants__r.get(0).ContestantCode__c
        );
        User adminUser = sc_TestUtils.getAdminUser();

        System.runAs(adminUser) {
            insert eval;
        }

        Test.startTest();
        System.runAs(adminUser) {
            delete eval;
        }
        Test.stopTest();

        cmp_Competition__c conferenceAfterEvaluationDelete = getCompetition();
        Assert.areEqual(
            sc_JudgementProgressService.PENDING_STATUS,
            conferenceAfterEvaluationDelete.sc_JudgementStatus__c,
            'After last evaluation is deleted competition should have Pending status'
        );
    }

    static cmp_Competition__c getCompetition() {
        return [
            SELECT
                sc_JudgementStatus__c,
                (SELECT ContestantCode__c FROM CompetitionParticipants__r),
                (SELECT Id FROM CompetitionJudges__r),
                (SELECT Scorecard__c FROM ScorecardJunctions__r)
            FROM cmp_Competition__c
            WHERE Name = :COMPETITION_NAME
            LIMIT 1
        ];
    }
}