public with sharing class sc_DTO {
    public class Evaluation {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public String judgeId { get; set; }
        @AuraEnabled
        public String conferenceName { get; set; }
        @AuraEnabled
        public String competitionId { get; set; }
        @AuraEnabled
        public String competitionName { get; set; }
        @AuraEnabled
        public String competitionDivision { get; set; }
        @AuraEnabled
        public String competitionRound { get; set; }
        @AuraEnabled
        public Id competitionScorecardId { get; set; }
        @AuraEnabled
        public String scorecardId { get; set; }
        @AuraEnabled
        public Boolean isMasterScorecard { get; set; }
        @AuraEnabled
        public String competitorCode { get; set; }
        @AuraEnabled
        public Boolean isForTeam { get; set; }
        @AuraEnabled
        public EvaluationComment currentJudgeComment { get; set; }
        @AuraEnabled
        public List<EvaluationTopic> topics { get; set; }
        @AuraEnabled
        public List<ScorecardTopicAssignment> topicAssignments { get; set; }
        private sc_CompetitionEvaluation__c record;
        @AuraEnabled
        public Boolean hasIndividualTopicAssignments { get; set; }

        public Evaluation() {
        }

        public Evaluation(sc_CompetitionEvaluation__c eval, List<sc_CompetitionEvaluationTopic__c> topics) {
            this.id = eval.Id;
            this.name = eval.Name;
            this.judgeId = eval.Judge__c;
            this.competitionId = eval.Competition__c;
            this.scorecardId = eval.Scorecard__c;
            this.competitorCode = eval.CompetitorCode__c;
            this.isMasterScorecard = eval.IsMasterScorecard__c;
            this.topics = groupSubtopics(convertTopics(topics));
            this.isForTeam = eval.Competition__r.IsTeamCompetition__c;
            this.competitionName = eval.Competition__r.Name;
            this.competitionRound = eval.CompetitionRound__c;
            this.hasIndividualTopicAssignments =
                this.isMasterScorecard && sc_CompetitionEvaluationService.isTopicAssignmentsEnabled();
        }

        public Evaluation(
            sc_Scorecard__c scorecard,
            List<sc_ScorecardTopic__c> topics,
            sc_CompetitionScorecard__c competitionScorecard
        ) {
            this.scorecardId = scorecard.Id;
            this.isMasterScorecard = isMasterScorecard;
            this.competitionId = competitionScorecard.Competition__c;
            this.isMasterScorecard = competitionScorecard.IsMasterScorecard__c;
            this.competitionRound = competitionScorecard.CompetitionRound__c;
            this.topics = groupSubtopics(convertTopics(topics));
            this.hasIndividualTopicAssignments =
                this.isMasterScorecard && sc_CompetitionEvaluationService.isTopicAssignmentsEnabled();
        }

        public sc_CompetitionEvaluation__c toSObject() {
            if (this.record != null) {
                return this.record;
            }

            this.record = new sc_CompetitionEvaluation__c();
            this.record.Id = this.id;
            this.record.Judge__c = this.judgeId;
            this.record.Competition__c = this.competitionId;
            this.record.Scorecard__c = this.scorecardId;
            this.record.CompetitorCode__c = this.competitorCode;
            this.record.IsMasterScorecard__c = this.isMasterScorecard;
            this.record.CompetitionRound__c = this.competitionRound;

            return this.record;
        }
    }

    public class EvaluationTopic {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String parentTopicId { get; set; }
        @AuraEnabled
        public String scoreTopicId { get; set; }
        @AuraEnabled
        public String parentScoreTopicId { get; set; }
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public String description { get; set; }
        @AuraEnabled
        public Integer maxScore { get; set; }
        @AuraEnabled
        public Integer score { get; set; }
        @AuraEnabled
        public Boolean isDeduction { get; set; }
        @AuraEnabled
        public Decimal order { get; set; }
        @AuraEnabled
        public List<EvaluationTopic> subtopics { get; set; }
        @AuraEnabled
        public Boolean isScoreEditable {
            get {
                return this.subtopics == null || this.subtopics.isEmpty();
            }
            set;
        }
        private sc_CompetitionEvaluationTopic__c record;

        public EvaluationTopic() {
        }

        public EvaluationTopic(sc_CompetitionEvaluationTopic__c evalTopic) {
            this.id = evalTopic.Id;
            this.scoreTopicId = evalTopic.ScorecardTopic__c;
            this.parentScoreTopicId = evalTopic.ScorecardTopic__r.ParentTopic__c;
            this.name = evalTopic.ScorecardTopic__r.Name;
            this.description = evalTopic.ScorecardTopic__r.Description__c;
            this.maxScore = Integer.valueOf(evalTopic.ScorecardTopic__r.MaxScore__c);
            this.score = Integer.valueOf(evalTopic.Score__c);
            this.isDeduction = evalTopic.ScorecardTopic__r.IsDeduction__c;
            this.order = evalTopic.ScorecardTopic__r.Order__c;
        }

        public EvaluationTopic(sc_ScorecardTopic__c scoreTopic) {
            this.scoreTopicId = scoreTopic.Id;
            this.parentScoreTopicId = scoreTopic.ParentTopic__c;
            this.name = scoreTopic.Name;
            this.description = scoreTopic.Description__c;
            this.maxScore = Integer.valueOf(scoreTopic.MaxScore__c);
            this.isDeduction = scoreTopic.IsDeduction__c;
            this.order = scoreTopic.Order__c;
        }

        public sc_CompetitionEvaluationTopic__c toSObject() {
            if (this.record != null) {
                return this.record;
            }

            this.record = new sc_CompetitionEvaluationTopic__c();
            this.record.Id = this.id;
            this.record.ScorecardTopic__c = this.scoreTopicId;
            this.record.Score__c = this.score;
            this.record.IsDeduction__c = this.isDeduction;

            return this.record;
        }
    }

    public class EvaluationComment {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String evaluationId { get; set; }
        @AuraEnabled
        public String judgeId { get; set; }
        @AuraEnabled
        public String body { get; set; }

        public EvaluationComment() {
        }

        public EvaluationComment(Id evaluationId, Id judgeId, String body) {
            this.evaluationId = evaluationId;
            this.judgeId = judgeId;
            this.body = body;
        }

        public EvaluationComment(sc_CompetitionEvaluationComment__c comment) {
            this.id = comment.Id;
            this.evaluationId = comment.Evaluation__c;
            this.judgeId = comment.Judge__c;
            this.body = comment.Body__c;
        }

        public sc_CompetitionEvaluationComment__c toSObject() {
            sc_CompetitionEvaluationComment__c comment = new sc_CompetitionEvaluationComment__c();
            comment.Id = this.Id;
            comment.Evaluation__c = this.evaluationId;
            comment.Judge__c = this.judgeId;
            comment.Body__c = this.body;
            return comment;
        }
    }

    public class Conference {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public List<CompetitionGroup> competitionGroups { get; set; }

        public Conference() {
        }

        public Conference(cm_Conference__c sobjConference) {
            this.id = sobjConference.Id;
            this.name = sobjConference.Name;
        }
    }

    public class CompetitionGroup {
        @AuraEnabled
        public String competitionName { get; set; }
        @AuraEnabled
        public List<Competition> competitions { get; set; }
        @AuraEnabled
        public Integer presentationOrder { get; set; }
    }

    public class Competition {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public String fullName { get; set; }
        @AuraEnabled
        public String conferenceName { get; set; }
        @AuraEnabled
        public Boolean isForTeams { get; set; }
        @AuraEnabled
        public String divisionName { get; set; }
        @AuraEnabled
        public String judgementStatus { get; set; }
        @AuraEnabled
        public Evaluation scorecard { get; set; }
        @AuraEnabled
        public List<Competitor> competitors { get; set; }
        @AuraEnabled
        public List<Person> technicalChairs { get; set; }
        @AuraEnabled
        public Integer presentationOrder { get; set; }

        public Competition() {
        }

        public Competition(cmp_Competition__c sobjCompetition) {
            Map<String, Object> sobjMap = sobjCompetition.getPopulatedFieldsAsMap();

            this.id = sobjCompetition.Id;
            this.name = sobjCompetition.Name;
            this.divisionName = (String) sobjMap.get('Division__c');
            this.conferenceName = ((cm_Conference__c) sobjMap.get('Conference__r'))?.Name;
            this.isForTeams = (Boolean) sobjMap.get('IsTeamCompetition__c');
            this.judgementStatus = (String) sobjMap.get('sc_JudgementStatus__c');
            this.presentationOrder = Integer.valueOf(sobjMap.get('sc_PresentationOrder__c'));
            this.fullName = this.divisionName != null ? this.name + ' (' + this.divisionName + ')' : this.name;
        }

        public Competition(cmp_Competition__c sobjCompetition, List<cmp_Participant__c> sobjCompetitors) {
            this(sobjCompetition);
            this.competitors = convertCompetitors(sobjCompetitors);
        }
    }

    public class Competitor {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public String code { get; set; }
        @AuraEnabled
        public String individualCode { get; set; }
        @AuraEnabled
        public String schoolName { get; set; }
        @AuraEnabled
        public Id competitionId { get; set; }
        @AuraEnabled
        public String competitionName { get; set; }
        @AuraEnabled
        public String division { get; set; }
        @AuraEnabled
        public Integer rank { get; set; }
        @AuraEnabled
        public Integer maxRank { get; set; }
        @AuraEnabled
        public Integer score { get; set; }
        @AuraEnabled
        public Date resultPublishDate { get; set; }
        @AuraEnabled
        public Boolean isNonAttendee { get; set; }
        @AuraEnabled
        public Boolean isDisqualified { get; set; }
        @AuraEnabled
        public Boolean isTeam { get; set; }
        @AuraEnabled
        public List<Evaluation> evaluations { get; set; }
        @AuraEnabled
        public List<Competitor> teamMembers { get; set; }

        public Competitor() {
        }

        public Competitor(cmp_Participant__c sobjCompetitor) {
            Map<String, Object> sobjMap = sobjCompetitor.getPopulatedFieldsAsMap();

            this.id = (Id) sobjMap.get('Id');
            this.name = (String) ((Contact) sobjMap.get('Participant__r'))?.get('Name');
            this.code = (String) sobjMap.get('ContestantCode__c');
            this.individualCode = (String) sobjMap.get('ContestantNumber__c');
            this.competitionId = (Id) sobjMap.get('Competition__c');
            this.competitionName = (String) ((cmp_Competition__c) sobjMap.get('Competition__r'))?.get('Name');
            this.division = (String) ((cmp_Competition__c) sobjMap.get('Competition__r'))?.get('Division__c');
            this.isNonAttendee = (Boolean) sobjMap.get('sc_IsNonAttendee__c');
            this.isDisqualified = (Boolean) sobjMap.get('IsDisqualified__c');
            this.rank = Integer.valueOf(sobjMap.get('sc_Rank__c'));
            this.score = Integer.valueOf(sobjMap.get('sc_Score__c'));
            this.resultPublishDate = (Date) ((cmp_Competition__c) sobjMap.get('Competition__r'))
                ?.get('sc_ResultsPublishDate__c');
            this.schoolName = (String) ((Account) (((Contact) sobjMap.get('Participant__r'))?.getPopulatedFieldsAsMap())
                    ?.get('Account'))
                ?.get('Name');
        }

        public Competitor(List<cmp_Participant__c> sobjTeamMembers) {
            this.teamMembers = new List<Competitor>();
            this.isDisqualified = true;

            for (cmp_Participant__c sobjCompetitor : sobjTeamMembers) {
                this.teamMembers.add(new Competitor(sobjCompetitor));
                if (this.isDisqualified && !sobjCompetitor.IsDisqualified__c) {
                    this.isDisqualified = false;
                }
                this.rank = this.rank ?? Integer.valueOf(sobjCompetitor.sc_Rank__c);
                this.score = this.score ?? Integer.valueOf(sobjCompetitor.sc_Score__c);
            }

            Competitor sourceCompetitor = this.teamMembers.get(0);
            this.code = this.code ?? sourceCompetitor.code;
            this.schoolName = this.schoolName ?? sourceCompetitor.schoolName;
            this.competitionId = this.competitionId ?? sourceCompetitor.competitionId;
            this.competitionName = this.competitionName ?? sourceCompetitor.competitionName;
            this.division = this.division ?? sourceCompetitor.division;
            this.maxRank = this.maxRank ?? sourceCompetitor.maxRank;
            this.resultPublishDate = this.resultPublishDate ?? sourceCompetitor.resultPublishDate;
            this.isTeam = true;
        }
    }

    public class Person {
        @AuraEnabled
        public String name { get; set; }

        public Person(Contact con) {
            this.name = con.Name;
        }
    }

    public class Judge {
        @AuraEnabled
        public Id id { get; set; }
        @AuraEnabled
        public Id contactId { get; set; }
        @AuraEnabled
        public String name { get; set; }

        public Judge(cmp_Judge__c judge) {
            this.id = judge.Id;
            this.contactId = judge.Contact__c;
            this.name = judge.Contact__r.Name;
        }
    }

    public class ScorecardTopicAssignment {
        @AuraEnabled
        public Id id { get; set; }
        @AuraEnabled
        public Id topicId { get; set; }
        @AuraEnabled
        public String topicName { get; set; }
        @AuraEnabled
        public Id competitionScorecardId { get; set; }
        @AuraEnabled
        public Id competitionJudgeId { get; set; }

        public sc_ScorecardTopicAssignment__c toSObject() {
            sc_ScorecardTopicAssignment__c record = new sc_ScorecardTopicAssignment__c();
            record.Id = this.id;
            record.ScorecardTopic__c = this.topicId;
            record.CompetitionScorecard__c = this.competitionScorecardId;
            record.CompetitionJudge__c = this.competitionJudgeId;
            return record;
        }
    }

    public class ScorecardTopicAssignmentData {
        @AuraEnabled
        public List<Judge> judges { get; set; }
        @AuraEnabled
        public List<Evaluation> scorecards { get; set; }
    }

    public class RankingPresentationSettings {
        @AuraEnabled
        public String backgroundUrl { get; set; }
        @AuraEnabled
        public String competitionColor { get; set; }
        @AuraEnabled
        public String divisionColor { get; set; }
        @AuraEnabled
        public String ranksColor { get; set; }
        @AuraEnabled
        public String competitorColor { get; set; }
    }

    public class CompetitionRound {
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public String label { get; set; }
        @AuraEnabled
        public Integer order { get; set; }
    }

    public static List<sc_DTO.Competition> convertCompetitions(List<cmp_Competition__c> sobjCompetitions) {
        List<Competition> competitions = new List<Competition>();

        for (cmp_Competition__c sobjCompetition : sobjCompetitions) {
            competitions.add(new Competition(sobjCompetition));
        }

        return competitions;
    }

    public static List<EvaluationTopic> convertTopics(List<SObject> sobjTopics) {
        List<EvaluationTopic> topics = new List<EvaluationTopic>();

        for (SObject sobjTopic : sobjTopics) {
            if (sobjTopic instanceof sc_CompetitionEvaluationTopic__c) {
                topics.add(new EvaluationTopic((sc_CompetitionEvaluationTopic__c) sobjTopic));
            }
            if (sobjTopic instanceof sc_ScorecardTopic__c) {
                topics.add(new EvaluationTopic((sc_ScorecardTopic__c) sobjTopic));
            }
        }

        return topics;
    }

    public static Map<Id, EvaluationTopic> mapTopicsToScoreTopicId(Evaluation eval) {
        Map<Id, EvaluationTopic> scoreTopicIdTopic = new Map<Id, EvaluationTopic>();

        for (EvaluationTopic topic : eval.topics) {
            scoreTopicIdTopic.put(topic.scoreTopicId, topic);
            if (topic.subtopics == null) {
                continue;
            }
            for (EvaluationTopic subtopic : topic.subtopics) {
                scoreTopicIdTopic.put(subtopic.scoreTopicId, subtopic);
            }
        }

        return scoreTopicIdTopic;
    }

    private static List<EvaluationTopic> groupSubtopics(List<EvaluationTopic> topics) {
        Map<Id, EvaluationTopic> idToTopic = new Map<Id, EvaluationTopic>();
        Map<Id, List<EvaluationTopic>> parentIdToChildTopics = new Map<Id, List<EvaluationTopic>>();
        Set<Id> subtopicIds = new Set<Id>(idToTopic.keySet());

        for (EvaluationTopic topic : topics) {
            idToTopic.put(topic.scoreTopicId, topic);

            if (topic.parentScoreTopicId != null) {
                subtopicIds.add(topic.scoreTopicId);

                if (!parentIdToChildTopics.containsKey(topic.parentScoreTopicId)) {
                    parentIdToChildTopics.put(topic.parentScoreTopicId, new List<EvaluationTopic>());
                }
                parentIdToChildTopics.get(topic.parentScoreTopicId).add(topic);
            }
        }

        idToTopic.keySet().removeAll(subtopicIds);

        for (EvaluationTopic topic : idToTopic.values()) {
            topic.subtopics = parentIdToChildTopics.get(topic.scoreTopicId);
        }

        return idToTopic.values();
    }

    public static List<Competitor> convertCompetitors(List<cmp_Participant__c> sobjCompetitors) {
        List<Competitor> competitors = new List<Competitor>();
        Map<String, List<cmp_Participant__c>> codeToSobjCompetitors = sc_CollectionHelper.mapRecordsToField(
            'ContestantCode__c',
            sobjCompetitors
        );

        for (String competitorCode : codeToSobjCompetitors.keySet()) {
            List<cmp_Participant__c> sobjCompetitorsWithCode = codeToSobjCompetitors.get(competitorCode);
            Boolean isSingleCompetitorPerCode = sobjCompetitorsWithCode.size() == 1;
            Boolean isCodeAndNumberMatch =
                sobjCompetitorsWithCode.get(0).ContestantNumber__c == sobjCompetitorsWithCode.get(0).ContestantCode__c;

            sc_DTO.Competitor competitor = isSingleCompetitorPerCode && isCodeAndNumberMatch
                ? new sc_DTO.Competitor(sobjCompetitorsWithCode.get(0))
                : new sc_DTO.Competitor(sobjCompetitorsWithCode);

            competitors.add(competitor);
        }

        return competitors;
    }
}