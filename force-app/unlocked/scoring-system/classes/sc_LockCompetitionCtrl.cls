public with sharing class sc_LockCompetitionCtrl {
    @AuraEnabled(cacheable=true)
    public static Boolean isButtonVisible(Id competitionId) {
        try {
            return sc_CompetitionService.isActualTechnicalChair(competitionId, PortalUser.getContactId()) ||
                sc_CompetitionService.isUserAbleToLockCompetition(competitionId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static Boolean isCompetitionLocked(Id competitionId) {
        try {
            return [
                SELECT sc_IsEvaluationsLocked__c
                FROM cmp_Competition__c
                WHERE Id = :competitionId
                WITH SYSTEM_MODE
                LIMIT 1
            ]
            ?.sc_IsEvaluationsLocked__c;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void lockCompetition(Id competitionId, String awardsExceptions) {
        try {
            sc_CompetitionService.lockCompetition(competitionId, awardsExceptions);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void unlockCompetition(Id competitionId) {
        try {
            sc_CompetitionService.unlockCompetition(competitionId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}