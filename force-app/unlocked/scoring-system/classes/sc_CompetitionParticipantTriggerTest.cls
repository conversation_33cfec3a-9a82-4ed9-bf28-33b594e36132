@IsTest
class sc_CompetitionParticipantTriggerTest {
    static final Integer COMPETITOR_COUNT = 5;

    @TestSetup
    static void setup() {
        User adminUser = sc_TestUtils.createAdminUser();
        insert adminUser;
        insert sc_TestUtils.createPermissionSetAssignments(
            adminUser.Id,
            new Set<String>{
                sc_TestUtils.ADMIN_PS_NAME,
                sc_TestUtils.CONF_ADMIN_PS_NAME,
                sc_TestUtils.CMP_ADMIN_PS_NAME
            }
        );

        System.runAs(adminUser) {
            Account competitorsAccount = sc_TestUtils.createAccount('Competitor Account');
            cm_Conference__c conference = sc_TestUtils.createConference('Conference Name');
            insert new List<SObject>{ competitorsAccount, conference };

            List<Contact> competitorContacts = new List<Contact>();

            for (Integer i = 0; i < COMPETITOR_COUNT; i++) {
                competitorContacts.add(
                    sc_TestUtils.createContact(
                        competitorsAccount.Id,
                        'Competitor FN' + i,
                        'Competitor LN' + i,
                        'competitor' + i + '@actest.salesforce.com'
                    )
                );
            }
            insert competitorContacts;

            List<String> divisions = sc_TestUtils.getPicklistValues(cmp_Competition__c.Division__c.getDescribe());
            cmp_Competition__c competition = sc_TestUtils.createCompetition(
                conference.Id,
                'Competition Name',
                divisions.get(0)
            );
            insert competition;

            Map<Id, cm_Participant__c> contactIdToParticipant = sc_TestUtils.createConferenceParticipants(
                conference.Id,
                competitorContacts
            );
            insert contactIdToParticipant.values();

            insert sc_TestUtils.createCompetitors(competition.Id, competitorContacts, contactIdToParticipant);
        }
    }

    @IsTest
    static void resetRankOnDisqualification() {
        cmp_Participant__c competitor = [SELECT sc_Rank__c, sc_Score__c FROM cmp_Participant__c LIMIT 1];

        Assert.isNotNull(competitor.sc_Rank__c, 'Before disqualification competitor should have a rank');
        Assert.isNotNull(competitor.sc_Score__c, 'Before disqualification competitor should have a score');

        Test.startTest();
        System.runAs(sc_TestUtils.getAdminUser()) {
            competitor.IsDisqualified__c = true;
            update competitor;
        }
        Test.stopTest();

        cmp_Participant__c disqualifiedCompetitor = [
            SELECT sc_Rank__c, sc_Score__c, IsDisqualified__c
            FROM cmp_Participant__c
            WHERE Id = :competitor.Id
        ];

        Assert.isTrue(
            disqualifiedCompetitor.IsDisqualified__c,
            'Competitor should be disqualified once updated by admin'
        );
        Assert.isNull(
            disqualifiedCompetitor.sc_Rank__c,
            'Competitor\'s rank should have been cleared after disqualification'
        );
        Assert.isNull(
            disqualifiedCompetitor.sc_Score__c,
            'Competitor\'s score should have been cleared after disqualification'
        );
    }
}