public with sharing class sc_CompetitorRankingService {
    public static final Integer DEFAULT_NUM_OF_ROUND_WINNERS = 12;
    private static final Integer RANK_TABLE_NUM_OF_COMPETITORS = 6;
    public static final String FINAL_ROUND = 'Final';
    private Id conferenceId;
    private Set<Id> competitionIds;

    public static sc_DTO.Conference getConferenceRankings(Id conferenceId) {
        List<cmp_Competition__c> competitions = queryConferenceCompetitionsWithCompetitors(conferenceId);

        sc_DTO.Conference conferenceDto = new sc_DTO.Conference();
        conferenceDto.name = competitions.get(0).Conference__r.Name;
        conferenceDto.competitionGroups = new List<sc_DTO.CompetitionGroup>();
        Map<String, List<sc_DTO.Competition>> nameToDivisionRankingDtos = new Map<String, List<sc_DTO.Competition>>();

        for (cmp_Competition__c competition : competitions) {
            if (!nameToDivisionRankingDtos.containsKey(competition.Name)) {
                nameToDivisionRankingDtos.put(competition.Name, new List<sc_DTO.Competition>());
            }
            sc_DTO.Competition competitionDto = new sc_DTO.Competition(
                competition,
                competition.CompetitionParticipants__r
            );
            competitionDto.presentationOrder = Integer.valueOf(competition.sc_PresentationOrder__c);
            nameToDivisionRankingDtos.get(competition.Name).add(competitionDto);
        }

        for (String competitionName : nameToDivisionRankingDtos.keySet()) {
            sc_DTO.CompetitionGroup competitionGroupDto = new sc_DTO.CompetitionGroup();
            competitionGroupDto.competitionName = competitionName;
            competitionGroupDto.competitions = nameToDivisionRankingDtos.get(competitionName);
            competitionGroupDto.presentationOrder = competitionGroupDto.competitions.get(0).presentationOrder;
            conferenceDto.competitionGroups.add(competitionGroupDto);
        }

        return conferenceDto;
    }

    public static List<sc_DTO.Competitor> getPublishedCompetitorsRankingsWs(
        Id conferenceId,
        Set<Id> competitorContactIds
    ) {
        List<cmp_Participant__c> competitors = queryCompetitorForConference(conferenceId, competitorContactIds);
        Set<Id> publishedCompetitionIds = new Set<Id>();
        Set<String> publishedCompetitorCodes = new Set<String>();

        for (cmp_Participant__c competitor : competitors) {
            if (sc_CompetitionService.isCompetitionResultsPublished(competitor.Competition__r)) {
                publishedCompetitionIds.add(competitor.Competition__c);
                publishedCompetitorCodes.add(competitor.ContestantCode__c);
            } else {
                competitor.sc_Rank__c = null;
                competitor.sc_Score__c = null;
            }
        }

        Map<Id, Integer> competitionIdToMaxRank = new Map<Id, Integer>();
        Map<String, List<sc_CompetitionEvaluation__c>> competitorCodeToEvals = new Map<String, List<sc_CompetitionEvaluation__c>>();

        if (!publishedCompetitionIds.isEmpty()) {
            competitionIdToMaxRank = queryMaxRankPerCompetition(publishedCompetitionIds);
            List<sc_CompetitionEvaluation__c> evals = sc_CompetitionEvaluationService.queryEvaluationsByContactIds(
                publishedCompetitionIds,
                publishedCompetitorCodes,
                AccessLevel.SYSTEM_MODE
            );
            competitorCodeToEvals = sc_CollectionHelper.mapRecordsToField('CompetitorCode__c', evals);
        }

        List<sc_DTO.Competitor> competitorDtos = sc_DTO.convertCompetitors(competitors);

        for (sc_DTO.Competitor competitorDto : competitorDtos) {
            if (!publishedCompetitionIds.contains(competitorDto.competitionId)) {
                continue;
            }
            competitorDto.maxRank = competitionIdToMaxRank?.get(competitorDto.competitionId);

            if (competitorDto.isDisqualified || !competitorCodeToEvals.containsKey(competitorDto.code)) {
                continue;
            }
            competitorDto.evaluations = new List<sc_DTO.Evaluation>();

            for (sc_CompetitionEvaluation__c eval : competitorCodeToEvals.get(competitorDto.code)) {
                sc_DTO.Evaluation evalDto = new sc_DTO.Evaluation();
                evalDto.id = eval.Id;
                evalDto.name = eval.Name;
                evalDto.competitionRound = eval.CompetitionRound__c;
                competitorDto.evaluations.add(evalDto);
            }
        }

        return competitorDtos;
    }

    public static List<cmp_Participant__c> updateCompetitorRankings(Set<Id> competitionIds) {
        List<cmp_Participant__c> competitorsForUpdate = retrieveCompetitorsWithScore(
            competitionIds,
            null,
            AccessLevel.SYSTEM_MODE
        );
        WS.updateRecords(competitorsForUpdate);

        return competitorsForUpdate;
    }

    public static List<sc_DTO.Competitor> getCompetitorsForRanksTable(Id competitionId, AccessLevel access) {
        List<cmp_Participant__c> competitorsWithScore = retrieveCompetitorsWithScore(
            new Set<Id>{ competitionId },
            null,
            access
        );
        Integer rankThreshold = (Integer) sc_ScoringSystemSettings__c.getInstance().LimitForCompetitorsRankedTable__c ??
            RANK_TABLE_NUM_OF_COMPETITORS;
        competitorsWithScore = sliceCompetitorsByRank(competitorsWithScore, rankThreshold);
        Map<Id, cmp_Participant__c> competitorById = new Map<Id, cmp_Participant__c>(competitorsWithScore);
        Set<Id> competitorsIds = competitorById.keySet();
        List<cmp_Participant__c> fullCompetitorDetailsList = new List<cmp_Participant__c>();

        for (cmp_Participant__c competitor : [
            SELECT
                Id,
                ContestantCode__c,
                ContestantNumber__c,
                Participant__r.Account.Name,
                Participant__r.Name,
                IsDisqualified__c
            FROM cmp_Participant__c
            WHERE Id IN :competitorsIds AND IsApprovedForConference__c = TRUE
            WITH SYSTEM_MODE
        ]) {
            cmp_Participant__c competitorWithScore = competitorById.get(competitor.Id);
            competitor.sc_Score__c = competitorWithScore.sc_Score__c;
            competitor.sc_Rank__c = competitorWithScore.sc_Rank__c;
            competitor.IsDisqualified__c = competitorWithScore.IsDisqualified__c;
            fullCompetitorDetailsList.add(competitor);
        }

        return sc_DTO.convertCompetitors(fullCompetitorDetailsList);
    }

    public static List<cmp_Participant__c> retrieveCompetitorsWithScore(
        Set<Id> competitionIds,
        Map<Id, String> competitionIdToRound,
        AccessLevel access
    ) {
        List<cmp_Participant__c> competitors = queryCompetitorsForCompetitions(competitionIds);
        Map<String, List<cmp_Participant__c>> codeToCompetitors = sc_CollectionHelper.mapRecordsToField(
            'ContestantCode__c',
            competitors
        );
        Map<String, Integer> competitorCodeToScore = new Map<String, Integer>();
        Map<String, Integer> competitorCodeToRank = new Map<String, Integer>();
        Map<String, Integer> competitionKeyToNextRank = new Map<String, Integer>();
        Set<Id> eliminatedCompetitorIds = new Set<Id>();

        for (AggregateResult aggRes : queryTotalScoresPerCompetitorCode(competitionIds, access)) {
            Id competitionId = (Id) aggRes.get('competitionId');
            String competitorCode = (String) aggRes.get('competitorCode');
            String currentRound = competitionIdToRound?.get(competitionId) ??
                codeToCompetitors.get(competitorCode).get(0).Competition__r.sc_CurrentRound__c;
            String resultRound = (String) aggRes.get('competitionRound');
            String competitionKey = competitionId + resultRound;
            Integer resultScore = Integer.valueOf(aggRes.get('totalScore'));
            Integer cumulativeScore = competitorCodeToScore.get(competitorCode) ?? 0;
            cumulativeScore += currentRound == resultRound || currentRound == FINAL_ROUND ? resultScore : 0;
            competitorCodeToScore.put(competitorCode, cumulativeScore);

            Boolean hasActiveCompetitors = false;
            String eliminationRound;

            for (cmp_Participant__c competitor : codeToCompetitors.get(competitorCode)) {
                eliminationRound = competitor.sc_EliminationRound__c;
                if (!competitor.IsDisqualified__c) {
                    hasActiveCompetitors = true;
                    break;
                }
            }

            if (!competitionKeyToNextRank.containsKey(competitionKey)) {
                competitionKeyToNextRank.put(competitionKey, 1);
            }

            if (hasActiveCompetitors) {
                if (currentRound == resultRound || eliminationRound == resultRound) {
                    competitorCodeToRank.put(competitorCode, competitionKeyToNextRank.get(competitionKey));
                }
                competitionKeyToNextRank.put(competitionKey, competitionKeyToNextRank.get(competitionKey) + 1);
            }
        }

        for (cmp_Participant__c competitor : competitors) {
            if (competitor.IsDisqualified__c) {
                competitor.sc_Score__c = null;
                competitor.sc_Rank__c = null;
                continue;
            }
            competitor.sc_Score__c = competitorCodeToScore.get(competitor.ContestantCode__c);
            competitor.sc_Rank__c = competitorCodeToRank.get(competitor.ContestantCode__c);
        }

        return competitors;
    }

    private static List<cmp_Participant__c> sliceCompetitorsByRank(
        List<cmp_Participant__c> competitorsWithScore,
        Integer rankThreshold
    ) {
        List<cmp_Participant__c> competitorsWithScoreUnderTheLimit = new List<cmp_Participant__c>();
        for (cmp_Participant__c competitor : competitorsWithScore) {
            if (competitor.sc_Rank__c <= rankThreshold) {
                competitorsWithScoreUnderTheLimit.add(competitor);
            }
        }

        return competitorsWithScoreUnderTheLimit;
    }

    public static void markEliminatedCompetitors(
        List<cmp_Participant__c> competitors,
        Integer rankThreshold,
        Map<Id, String> competitionIdToRound
    ) {
        Boolean isAnyCompetitorsRanked = false;

        for (cmp_Participant__c competitor : competitors) {
            if (competitor.sc_Rank__c != null) {
                isAnyCompetitorsRanked = true;
                break;
            }
        }
        for (cmp_Participant__c competitor : competitors) {
            Boolean hasWinnerRank = competitor.sc_Rank__c <= rankThreshold;

            if (hasWinnerRank || !isAnyCompetitorsRanked) {
                competitor.sc_EliminationRound__c = null;
            } else {
                competitor.sc_EliminationRound__c = competitionIdToRound.get(competitor.Competition__c) ??
                    competitor.Competition__r.sc_CurrentRound__c;
            }
        }
    }

    private static List<cmp_Participant__c> composeCompetitorsWithRemovedRanking(Set<Id> competitorIds) {
        List<cmp_Participant__c> competitors = new List<cmp_Participant__c>();

        for (Id competitorId : competitorIds) {
            competitors.add(new cmp_Participant__c(Id = competitorId, sc_Rank__c = null, sc_Score__c = null));
        }

        return competitors;
    }

    private static List<AggregateResult> queryTotalScoresPerCompetitorCode(Set<Id> competitionIds, AccessLevel access) {
        String query =
            'SELECT SUM(TotalScore__c) totalScore, Competition__c competitionId, CompetitorCode__c competitorCode, ' +
            'CompetitionRound__c competitionRound ' +
            'FROM sc_CompetitionEvaluation__c ' +
            'WHERE Competition__c IN :competitionIds ' +
            'GROUP BY Competition__c, CompetitorCode__c, CompetitionRound__c ' +
            'ORDER BY SUM(TotalScore__c) DESC';

        if (access == AccessLevel.SYSTEM_MODE) {
            return WS.retrieveAggregateWithBinds(query, new Map<String, Object>{ 'competitionIds' => competitionIds });
        }

        return Database.queryWithBinds(query, new Map<String, Object>{ 'competitionIds' => competitionIds }, access);
    }

    private static List<cmp_Competition__c> queryConferenceCompetitionsWithCompetitors(Id conferenceId) {
        return [
            SELECT
                Name,
                Division__c,
                Conference__r.Name,
                sc_PresentationOrder__c,
                IsTeamCompetition__c,
                (
                    SELECT
                        Name,
                        IsDisqualified__c,
                        Participant__r.Name,
                        ContestantNumber__c,
                        ContestantCode__c,
                        Participant__r.Account.Name,
                        sc_Rank__c,
                        sc_Score__c
                    FROM CompetitionParticipants__r
                    WHERE IsApprovedForConference__c = TRUE
                    ORDER BY sc_Rank__c NULLS LAST
                )
            FROM cmp_Competition__c
            WHERE Conference__c = :conferenceId
            WITH USER_MODE
            ORDER BY Name
        ];
    }

    private static Map<Id, Integer> queryMaxRankPerCompetition(Set<Id> competitionIds) {
        String query =
            'SELECT MAX(sc_Rank__c) maxRank, Competition__c competition FROM cmp_Participant__c ' +
            'WHERE Competition__c IN :competitionIds ' +
            ' AND IsDisqualified__c = false AND IsApprovedForConference__c = TRUE ' +
            'GROUP BY Competition__c';
        Map<String, Object> queryBinds = new Map<String, Object>{ 'competitionIds' => competitionIds };

        Map<Id, Integer> competitionIdToCompetitorCount = new Map<Id, Integer>();

        for (AggregateResult aggRes : WS.retrieveAggregateWithBinds(query, queryBinds)) {
            competitionIdToCompetitorCount.put((Id) aggRes.get('competition'), Integer.valueOf(aggRes.get('maxRank')));
        }

        return competitionIdToCompetitorCount;
    }

    private static List<cmp_Participant__c> queryCompetitorsForCompetitions(Set<Id> competitionIds) {
        return [
            SELECT
                Competition__c,
                ContestantCode__c,
                IsDisqualified__c,
                sc_EliminationRound__c,
                Competition__r.sc_CurrentRound__c
            FROM cmp_Participant__c
            WHERE Competition__c IN :competitionIds AND IsApprovedForConference__c = TRUE
        ];
    }

    private static List<cmp_Participant__c> queryCompetitorForConference(
        Id conferenceId,
        Set<Id> competitorContactIds
    ) {
        Set<String> codes = sc_CompetitionService.queryCompetitorCodesForConferenceByContacts(
            conferenceId,
            competitorContactIds,
            null
        );

        String recordsQuery =
            'SELECT Participant__r.Name, ContestantNumber__c, ContestantCode__c, ' +
            'IsDisqualified__c, sc_Rank__c, sc_Score__c, Competition__r.Name, ' +
            'Competition__r.Division__c, Competition__r.sc_ResultsPublishDate__c ' +
            'FROM cmp_Participant__c ' +
            'WHERE Competition__r.Conference__c = :conferenceId ' +
            'AND ContestantCode__c IN :codes AND IsApprovedForConference__c = TRUE ' +
            'ORDER BY sc_Rank__c NULLS LAST';

        Map<String, Object> recordsQueryBinds = new Map<String, Object>{
            'conferenceId' => conferenceId,
            'codes' => codes
        };

        return (List<cmp_Participant__c>) WS.retrieveRecords(recordsQuery, recordsQueryBinds);
    }
}