public with sharing class sc_CompetitorAttendanceCtrl {
    @AuraEnabled
    public static Boolean isAttendanceEditAvailable(Id competitionId) {
        try {
            return isCurrentUserAuthorized(competitionId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static List<sc_DTO.Competitor> getCompetitors(Id competitionId) {
        if (!isCurrentUserAuthorized(competitionId)) {
            throw Error.toLWC(System.Label.sclOperationNoPermittedErr);
        }
        try {
            //tech chair doesn't have a read access to Contact (Participant__r)
            List<cmp_Participant__c> competitors = (List<cmp_Participant__c>) WS.retrieveRecords(
                'SELECT Participant__r.Name, ContestantNumber__c, sc_IsNonAttendee__c ' +
                    'FROM cmp_Participant__c ' +
                    'WHERE Competition__c = :competitionId AND IsApprovedForConference__c = TRUE',
                new Map<String, Object>{ 'competitionId' => competitionId }
            );
            List<sc_DTO.Competitor> competitorDtos = new List<sc_DTO.Competitor>();

            for (cmp_Participant__c competitor : competitors) {
                competitorDtos.add(new sc_DTO.Competitor(competitor));
            }

            return competitorDtos;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void markNonAttendees(Id competitionId, List<Id> competitorIds) {
        if (!isCurrentUserAuthorized(competitionId)) {
            throw Error.toLWC(System.Label.sclOperationNoPermittedErr);
        }
        try {
            List<cmp_Participant__c> competitors = [
                SELECT Id
                FROM cmp_Participant__c
                WHERE Competition__c = :competitionId AND Id IN :competitorIds
                WITH USER_MODE
            ];

            for (cmp_Participant__c competitor : competitors) {
                competitor.sc_IsNonAttendee__c = true;
            }

            WS.updateRecords(competitors);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    private static Boolean isCurrentUserAuthorized(Id competitionId) {
        Id currentContactId = PortalUser.getContactId();
        if (currentContactId == null) {
            return Schema.SObjectType.cmp_Participant__c.fields.sc_IsNonAttendee__c.isUpdateable();
        }
        return sc_CompetitionService.isActualTechnicalChair(competitionId, currentContactId);
    }
}