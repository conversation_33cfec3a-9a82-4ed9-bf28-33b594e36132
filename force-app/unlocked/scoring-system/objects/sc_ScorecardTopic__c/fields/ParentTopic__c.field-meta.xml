<?xml version="1.0" encoding="UTF-8" ?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>ParentTopic__c</fullName>
    <deleteConstraint>SetNull</deleteConstraint>
    <description>Lookup to another topic which makes current topic a subtopic</description>
    <externalId>false</externalId>
    <label>Parent Standard</label>
    <lookupFilter>
        <active>true</active>
        <errorMessage
        >Substandard cannot be used as Parent Standard. Parent Standard must be related to the same Scorecard.</errorMessage>
        <filterItems>
            <field>sc_ScorecardTopic__c.Scorecard__c</field>
            <operation>equals</operation>
            <valueField>$Source.Scorecard__c</valueField>
        </filterItems>
        <filterItems>
            <field>sc_ScorecardTopic__c.ParentTopic__c</field>
            <operation>equals</operation>
            <value />
        </filterItems>
        <isOptional>false</isOptional>
    </lookupFilter>
    <referenceTo>sc_ScorecardTopic__c</referenceTo>
    <relationshipLabel>Substandards</relationshipLabel>
    <relationshipName>Subtopics</relationshipName>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Lookup</type>
</CustomField>
