<?xml version="1.0" encoding="UTF-8" ?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>wbl_RapJobApprovedWithoutOccupation</fullName>
    <active>true</active>
    <description
    >Throws error when job owned by user from RAP account getting approved without Job Occupation</description>
    <errorConditionFormula
    >RecordType.DeveloperName = &apos;RAP&apos; &amp;&amp; wbl_IsApproved__c &amp;&amp;  wbl_JobOccupation__c = NULL</errorConditionFormula>
    <errorMessage>RAP Jobs should have Occupation Code to be approved</errorMessage>
</ValidationRule>
