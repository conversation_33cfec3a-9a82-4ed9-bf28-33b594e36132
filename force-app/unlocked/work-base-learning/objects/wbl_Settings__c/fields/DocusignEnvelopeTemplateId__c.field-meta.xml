<?xml version="1.0" encoding="UTF-8" ?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>DocusignEnvelopeTemplateId__c</fullName>
    <description>Specifies template which used for sending job contracts for signing</description>
    <externalId>false</externalId>
    <inlineHelpText
    >Do not use Salesforce record Id. This field requires value from DocuSign template ID field of Envelope Template</inlineHelpText>
    <label>DocuSign Envelope Template Id</label>
    <length>255</length>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
