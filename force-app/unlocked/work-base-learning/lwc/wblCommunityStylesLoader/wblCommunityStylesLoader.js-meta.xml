<?xml version="1.0" encoding="UTF-8" ?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <isExposed>true</isExposed>
    <masterLabel>WBL Community Styles Loader</masterLabel>
    <targets>
        <target>lightningCommunity__Default</target>
        <target>lightningCommunity__Page</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightningCommunity__Default">
            <property label="Disable List View Selection" name="isListViewSelectionDisabled" type="Boolean" />
            <property label="Hide List View Inline Actions" name="isListViewInlineActionsHidden" type="Boolean" />
            <property label="Disable List View Actions on Mobile" name="isMobileActionsDisabled" type="Boolean" />
            <property
                label="Hide Record Edit Button on Desktop"
                name="isDesktopRecordEditHidden"
                type="Boolean"
                description="Hides standard edit button on adjacent Record Banner component"
            />
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>
