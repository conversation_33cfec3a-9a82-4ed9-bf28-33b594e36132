import { LightningElement, api, wire } from 'lwc';
import stdJobApplicationBtn from '@salesforce/label/JobBoard.Apply_Position';
import APP_LINK_FIELD from '@salesforce/schema/JobBoard__Job__c.JobBoard__External_Application_Link__c';
import { getFieldValue, getRecord } from 'lightning/uiRecordApi';
import { loadStyle } from 'lightning/platformResourceLoader';
import stylesResource from '@salesforce/resourceUrl/wbl_Styles';
import incrementApplicationClickCount from '@salesforce/apex/wbl_JobApplicationCtrl.incrementApplicationClickCount';
import { getObjectInfo } from 'lightning/uiObjectInfoApi';
import jobApplicationObject from '@salesforce/schema/JobBoard__Job_Application__c';

export default class extends LightningElement {
    @api recordId;
    @api isFileUploadHelpTextShown;
    job;
    applicationLink;
    isCustomBtnDisplayed = false;
    jobApplicationInfo;
    jobApplicationInfoResolve;
    jobApplicationInfoPromise = new Promise((resolve) => (this.jobApplicationInfoResolve = resolve));

    labels = {
        stdJobApplicationBtn
    };

    connectedCallback() {
        loadStyle(this, stylesResource + '/jobApplicationButton.css');

        if (this.isFileUploadHelpTextShown) {
            loadStyle(this, stylesResource + '/jobApplicationFileHelpText.css');
        }
    }

    @wire(getRecord, { recordId: '$recordId', fields: [APP_LINK_FIELD] })
    jobReceived({ data, error }) {
        if (data) {
            this.job = data;
            this.applicationLink = getFieldValue(this.job, APP_LINK_FIELD);
            this.jobApplicationInfoPromise.then(() => this.displayApplicationButton()).catch((e) => console.error(e));
        }
        if (error) {
            console.error(error);
        }
    }

    @wire(getObjectInfo, { objectApiName: jobApplicationObject })
    objectInfoLoaded({ data, error }) {
        if (data) {
            this.jobApplicationInfo = data;
            this.jobApplicationInfoResolve();
        }
        if (error) {
            console.error(error);
        }
    }

    displayApplicationButton() {
        this.isCustomBtnDisplayed = !!this.applicationLink && this.jobApplicationInfo?.createable;

        if (!this.isCustomBtnDisplayed && this.jobApplicationInfo?.createable) {
            this.displayJobBoardButtons();
        }
    }

    displayJobBoardButtons() {
        //setting custom class on host parent which will enable display
        //of standard job board buttons in sibling job detail components
        this.template.host.parentNode.classList.add('wbl-display-job-board-button');
    }

    handleApplicationButtonClick() {
        this.updateApplicationLinkClickCount();
    }

    updateApplicationLinkClickCount() {
        incrementApplicationClickCount({ jobId: this.recordId });
    }
}
