<template>
    <div class="slds-grid">
        <lightning-button
            if:true={isButtonShown}
            class={buttonClass}
            onclick={handleRequestButtonClick}
            disabled={hasPendingRequests}
            stretch={isButtonStretched}
            variant={buttonVariant}
            label={buttonLabel}
        ></lightning-button>
        <lightning-helptext
            if:true={hasPendingRequests}
            class="slds-m-left_x-small"
            content={labels.wblRequestAccessPendingHelpText}
        ></lightning-helptext>
    </div>

    <c-wbl-modal data-id="confirmation-modal">
        <lightning-spinner slot="spinner" if:true={isSpinnerShown}></lightning-spinner>
        <h2 class="slds-modal__title" slot="header">{labels.wblRequestAccessAreYouSure}</h2>
        <p slot="body">{confirmationMessage}</p>
        <div class="slds-grid slds-grid_align-spread" slot="footer">
            <button class="slds-button slds-button_neutral" onclick={handleModalCancelButtonClick}>
                {labels.wblRequestAccessCancelBtn}
            </button>
            <button class="slds-button slds-button_brand" onclick={handleModalSubmitButtonClick}>
                {labels.wblRequestAccessSubmitBtn}
            </button>
        </div>
    </c-wbl-modal>
</template>
