import { LightningElement, api } from 'lwc';
import invitePartnerPermission from '@salesforce/customPermission/wbl_CanInvitePartnerCompanyToWblPlatform';
import sendInvitationEmail from '@salesforce/apex/wbl_InvitePartnerCompanyCtrl.sendInvitationEmail';
import { showErrors } from 'c/errorHandler';
import { labels } from './labels';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

export default class extends LightningElement {
    @api buttonLabel;
    @api isButtonStretched;
    @api buttonVariant;

    invitePartnerPermission = invitePartnerPermission;
    isSpinnerShown = false;
    labels = labels;

    handleInviteButtonClick() {
        this.getInvitationModal().open();
    }

    handleModalCancelButtonClick() {
        this.getInvitationModal().cancel();
    }

    handleInviteSubmitClick() {
        const emailInput = this.template.querySelector('[data-id="email-input"]');
        const isInputValid = emailInput.reportValidity();
        if (!isInputValid) {
            return;
        }
        const recipientEmail = emailInput.value;
        this.toggleSpinnerDuring(this.sendInvitation(recipientEmail)).then(() => {
            this.getInvitationModal().submit();
        });
    }

    sendInvitation(email) {
        return sendInvitationEmail({ recipientEmail: email })
            .then(() => this.showSuccessToast())
            .catch((err) => showErrors(this, err));
    }

    getInvitationModal() {
        return this.template.querySelector('[data-id="invitation-modal"]');
    }

    toggleSpinnerDuring(promise) {
        this.isSpinnerShown = true;
        return promise.finally(() => (this.isSpinnerShown = false));
    }

    showSuccessToast() {
        this.dispatchEvent(
            new ShowToastEvent({
                variant: 'success',
                message: labels.wblInvitePartnerSuccessToast
            })
        );
    }
}
