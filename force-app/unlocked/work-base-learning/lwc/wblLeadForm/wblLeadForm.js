import { NavigationMixin } from 'lightning/navigation';
import { LightningElement, api } from 'lwc';
import { showErrors } from 'c/errorHandler';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getForm from '@salesforce/apex/wbl_LeadFormCtrl.getForm';
import saveRecord from '@salesforce/apex/wbl_LeadFormCtrl.saveRecord';
import wblLeadSaveSuccessToast from '@salesforce/label/c.wblLeadSaveSuccessToast';
import wblLeadSaveBtn from '@salesforce/label/c.wblLeadSaveBtn';
import wblCaptchaValidationErr from '@salesforce/label/c.wblCaptchaValidationErr';

export default class extends NavigationMixin(LightningElement) {
    @api postSavePageName;
    @api isCaptchaDisabled = false;
    isSpinnerShown = false;
    formRetrieveMethod = getForm;
    formOverrides = [{name: 'Email', required: true}];
    labels = {
        wblLeadSaveBtn,
        wblCaptchaValidationErr
    };

    get enableCaptcha() {
        return !this.isCaptchaDisabled;
    }

    handleFormSubmit(event) {
        const leadRecord = event.detail.record;
        const captchaToken = event.detail.captchaToken;

        if (!this.isCaptchaDisabled && !captchaToken) {
            showErrors(this, this.labels.wblCaptchaValidationErr);
            return;
        }

        this.toggleSpinnerDuring(saveRecord({ leadRecord, captchaToken }))
            .then(() => {
                this.showSuccessSaveToast();
                this.redirectToPostSavePage();
            })
            .catch((error) => {
                showErrors(this, error);
            });
    }

    redirectToPostSavePage() {
        if (!this.postSavePageName) {
            return;
        }
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: this.postSavePageName
            }
        });
    }

    toggleSpinnerDuring(promise) {
        this.isSpinnerShown = true;
        return promise.finally(() => (this.isSpinnerShown = false));
    }

    showSuccessSaveToast() {
        this.dispatchEvent(
            new ShowToastEvent({
                variant: 'success',
                message: wblLeadSaveSuccessToast
            })
        );
    }
}
