@IsTest
public with sharing class wbl_TestDataFactory {
    private static final String ADMIN_USER_EMAIL = '<EMAIL>';
    private static final String ADMIN_ROLE_NAME = 'RoleName';
    private static final String ADMIN_PROFILE_NAME = 'System Administrator';
    private static final String INTERNAL_PROFILE_NAME = 'Standard User';
    private static final String INTERNAL_USER_EMAIL = '<EMAIL>';
    private static final String EXTERNAL_PROFILE_NAME = 'Customer Community Plus User';
    public static final String UNPRIVILEGED_USER_EMAIL = '<EMAIL>';
    private static final String WBL_PS_NAME = 'wbl_Admin';
    private static final String JOB_PROVIDER_PS_NAME = 'AC_Job_Board_Provider';
    private static final String WBL_JOB_PROVIDER_PS_NAME = 'wbl_JobProvider';
    private static final String JOB_APPLICANT_PS_NAME = 'wbl_JobApplicant';
    private static final String WBL_JOB_APPLICANT_PS_NAME = 'AC_Job_Board_Applicant';
    private static Profile externalProfile;

    public static User createAdminUser() {
        Profile adminProfile = [SELECT Id FROM Profile WHERE Name = :ADMIN_PROFILE_NAME];
        UserRole role = getAdminUserRole();
        return new User(
            Alias = 'admtst',
            Email = ADMIN_USER_EMAIL,
            Username = ADMIN_USER_EMAIL,
            EmailEncodingKey = 'UTF-8',
            LastName = 'Admin User',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = adminProfile.Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            UserRoleId = role.Id
        );
    }

    public static User createInternalUser() {
        Profile internalProfile = [SELECT Id FROM Profile WHERE Name = :INTERNAL_PROFILE_NAME];
        return new User(
            Alias = 'inttest',
            Email = INTERNAL_USER_EMAIL,
            Username = INTERNAL_USER_EMAIL,
            EmailEncodingKey = 'UTF-8',
            LastName = 'Internal User',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = internalProfile.Id,
            TimeZoneSidKey = 'America/Los_Angeles'
        );
    }

    public static User createExternalUser(Contact con) {
        Profile profile = getExternalProfile();
        return new User(
            Email = con.Email,
            Username = con.Email,
            ContactId = con.Id,
            CommunityNickname = (con.FirstName + con.LastName).left(40),
            Alias = con.FirstName.left(1) + con.LastName.left(4),
            EmailEncodingKey = 'UTF-8',
            LastName = con.LastName,
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            TimeZoneSidKey = 'America/Los_Angeles',
            ProfileId = profile.Id
        );
    }

    public static wbl_JobOccupation__c createJobOccupation(String name, Id accountId, Decimal hourLimit) {
        return new wbl_JobOccupation__c(
            Name = name,
            Account__c = accountId,
            NumberOfHours__c = hourLimit,
            Code__c = String.valueOf('Occupation Code #' + Integer.valueOf(Math.random() * 10000))
        );
    }

    public static List<JobBoard__Job__c> createJobs(Integer count) {
        List<JobBoard__Job__c> jobs = new List<JobBoard__Job__c>();
        List<String> industryOptions = getPicklistValues(
            Schema.SObjectType.JobBoard__Job__c.fields.JobBoard__Industry__c
        );
        List<String> experienceOptions = getPicklistValues(
            Schema.SObjectType.JobBoard__Job__c.fields.JobBoard__Experience__c
        );
        List<String> typeOptions = getPicklistValues(Schema.SObjectType.JobBoard__Job__c.fields.JobBoard__Type__c);

        for (Integer i = 0; i < count; i++) {
            Integer randKey = Integer.valueOf(Math.random() * 1000);
            Integer randIndustryIndex = Integer.valueOf(Math.random() * (industryOptions.size() - 1));
            Integer randExperienceIndex = Integer.valueOf(Math.random() * (experienceOptions.size() - 1));
            Integer randTypeIndex = Integer.valueOf(Math.random() * (industryOptions.size() - 1));

            jobs.add(
                new JobBoard__Job__c(
                    Name = 'Job Name ' + randKey,
                    JobBoard__Description__c = 'Job Description ' + randKey,
                    JobBoard__Industry__c = industryOptions.get(randIndustryIndex),
                    JobBoard__Experience__c = experienceOptions.get(randExperienceIndex),
                    JobBoard__Type__c = typeOptions.get(randTypeIndex),
                    JobBoard__Publish_start_date__c = Date.today().addDays(-1),
                    JobBoard__Publish_end_date__c = Date.today().addDays(365),
                    wbl_NewOccupation__c = 'Job Occupation ' + randKey
                )
            );
        }

        return jobs;
    }

    public static List<JobBoard__Job_Application__c> createJobApplications(
        JobBoard__Job__c job,
        List<Contact> contacts
    ) {
        List<JobBoard__Job_Application__c> jobApps = new List<JobBoard__Job_Application__c>();

        for (Contact con : contacts) {
            jobApps.add(
                new JobBoard__Job_Application__c(
                    JobBoard__Job__c = job.Id,
                    JobBoard__First_Name__c = con.FirstName,
                    JobBoard__Last_Name__c = con.LastName,
                    JobBoard__Email__c = con.Email,
                    JobBoard__Mobile__c = con.Phone,
                    JobBoard__Contact__c = con.Id
                )
            );
        }

        return jobApps;
    }

    public static List<wbl_OnboardingForm__c> createWblOnboardingForms(List<JobBoard__Job_Application__c> jobApps) {
        List<wbl_OnboardingForm__c> onboardForms = new List<wbl_OnboardingForm__c>();
        Integer jobAppI = 0;

        for (JobBoard__Job_Application__c jobApp : jobApps) {
            Integer randAge = Math.mod(jobAppI, 2) == 0
                ? wbl_Constant.ADULT_AGE - 1
                : wbl_Constant.ADULT_AGE + Integer.valueOf(Math.random() * 10);

            onboardForms.add(
                new wbl_OnboardingForm__c(
                    FirstName__c = jobApp.JobBoard__First_Name__c,
                    LastName__c = jobApp.JobBoard__Last_Name__c,
                    PersonalEmail__c = jobApp.JobBoard__Email__c,
                    DateOfBirth__c = Date.today().addYears(-1 * randAge),
                    ParentFirstName__c = jobApp.JobBoard__First_Name__c + ' Parent',
                    ParentLastName__c = jobApp.JobBoard__Last_Name__c + ' Parent',
                    ParentEmail__c = jobApp.JobBoard__Email__c + '.parent'
                )
            );

            jobAppI += 1;
        }

        return onboardForms;
    }

    public static List<wbl_TimeLog__c> createTimeLogs(
        Date startDate,
        Date endDate,
        Id jobAppId,
        Integer dailyWorkingHours
    ) {
        List<wbl_TimeLog__c> timeLogs = new List<wbl_TimeLog__c>();
        for (Date currentDate = startDate; currentDate <= endDate; currentDate = currentDate.addDays(1)) {
            Integer currentDayOfWeek = Integer.valueOf(((Datetime) currentDate).formatGmt('u'));
            timeLogs.add(
                new wbl_TimeLog__c(
                    StartDate__c = currentDate,
                    EndDate__c = currentDate,
                    SpentHours__c = dailyWorkingHours,
                    JobApplication__c = jobAppId
                )
            );
        }
        return timeLogs;
    }

    public static wbl_Settings__c createCustomSettings(Id internalUserId) {
        return new wbl_Settings__c(
            DocusignAdminUserId__c = internalUserId,
            DocusignApplicantRole__c = 'INTERMEDIARY',
            DocusignPartnerRole__c = 'IN_PERSON_SIGNER',
            DocusignAdminRole__c = 'CARBON_COPY',
            DocusignEnvelopeTemplateId__c = '00a0f0ea-0000-000c-bafd-d0c0cda00000',
            OrgWideEmailId__c = '000000000000000000',
            CommunityBaseUrl__c = 'https://test.salesforce.com',
            PartnerLeadCreationPageUrl__c = 'https://test.salesforce.com/partner',
            SetupOwnerId = UserInfo.getOrganizationId()
        );
    }

    public static UserRole getAdminUserRole() {
        List<UserRole> insertedRoles = [SELECT Id FROM UserRole WHERE DeveloperName = :ADMIN_ROLE_NAME];
        UserRole role = insertedRoles.isEmpty()
            ? new UserRole(DeveloperName = ADMIN_ROLE_NAME, Name = ADMIN_ROLE_NAME)
            : insertedRoles.get(0);
        if (String.isBlank(role.Id)) {
            insert role;
        }
        return role;
    }

    public static Profile getExternalProfile() {
        if (externalProfile != null) {
            return externalProfile;
        }
        return [SELECT Id FROM Profile WHERE Name = :EXTERNAL_PROFILE_NAME];
    }

    public static User getUser(String username) {
        return [SELECT AccountId, ContactId FROM User WHERE Username = :username];
    }

    public static User getAdminUser() {
        return getUser(ADMIN_USER_EMAIL);
    }

    public static User getInternalUser() {
        return getUser(INTERNAL_USER_EMAIL);
    }

    public static User getUnprivilegedUser() {
        return getUser(UNPRIVILEGED_USER_EMAIL);
    }

    public static void assignPermissionSet(Id userId, String psName) {
        PermissionSet ps = [SELECT Id FROM PermissionSet WHERE Name = :psName];
        Integer currentAssignmentCount = [
            SELECT COUNT()
            FROM PermissionSetAssignment
            WHERE PermissionSetId = :ps.Id AND AssigneeId = :userId
        ];
        if (currentAssignmentCount == 0) {
            insert new PermissionSetAssignment(PermissionSetId = ps.Id, AssigneeId = userId);
        }
    }

    public static void assignWblPermissionSet(Id userId) {
        assignPermissionSet(userId, WBL_PS_NAME);
    }

    public static void assignJobProviderPermissionSet(Id userId) {
        List<String> psNames = new List<String>{ JOB_PROVIDER_PS_NAME, WBL_JOB_PROVIDER_PS_NAME };
        List<PermissionSetAssignment> psAssignments = new List<PermissionSetAssignment>();
        for (PermissionSet ps : [SELECT Id FROM PermissionSet WHERE Name IN :psNames]) {
            psAssignments.add(new PermissionSetAssignment(PermissionSetId = ps.Id, AssigneeId = userId));
        }
        insert psAssignments;
    }

    public static void assignJobApplicantPermissionSet(Id userId) {
        List<String> psNames = new List<String>{ JOB_APPLICANT_PS_NAME, WBL_JOB_APPLICANT_PS_NAME };
        List<PermissionSetAssignment> psAssignments = new List<PermissionSetAssignment>();
        for (PermissionSet ps : [SELECT Id FROM PermissionSet WHERE Name IN :psNames]) {
            psAssignments.add(new PermissionSetAssignment(PermissionSetId = ps.Id, AssigneeId = userId));
        }
        insert psAssignments;
    }

    public static User createUnprivilegedUser() {
        Profile chatterFreeProfile = [SELECT Id FROM Profile WHERE UserLicense.Name = 'Chatter Free' LIMIT 1];
        return new User(
            Alias = 'noperm',
            Email = UNPRIVILEGED_USER_EMAIL,
            Username = UNPRIVILEGED_USER_EMAIL,
            EmailEncodingKey = 'UTF-8',
            LastName = 'NoPermission',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = chatterFreeProfile.Id,
            TimeZoneSidKey = 'America/Los_Angeles'
        );
    }

    public static List<String> getPicklistValues(Schema.DescribeFieldResult fieldDesc) {
        List<String> picklistValues = new List<String>();

        for (Schema.PicklistEntry pickEntry : fieldDesc.getPicklistValues()) {
            picklistValues.add(pickEntry.getValue());
        }

        return picklistValues;
    }
}