public with sharing class wbl_JobService {
    private static final Map<String, Schema.RecordTypeInfo> NAME_TO_JOB_RT = Schema.SObjectType.JobBoard__Job__c.getRecordTypeInfosByDeveloperName();

    public static Boolean isJobAutoapprovable(JobBoard__Job__c job) {
        wbl_Settings__c settings = wbl_Settings__c.getInstance();
        Boolean isWblJob = job.RecordTypeId == getWblRecordTypeId();
        Boolean isRapJob = job.RecordTypeId == getRapRecordTypeId();

        return isWblJob && settings.IsWblJobsAutoapproved__c || isRapJob && settings.IsRapJobsAutoapproved__c;
    }

    public static Id getWblRecordTypeId() {
        return NAME_TO_JOB_RT.get(wbl_Constant.WBL_JOB_RT_NAME).getRecordTypeId();
    }

    public static Id getRapRecordTypeId() {
        return NAME_TO_JOB_RT.get(wbl_Constant.RAP_JOB_RT_NAME).getRecordTypeId();
    }
}