public with sharing class wbl_EmployerTimeLogNotifier {
    static final Integer QUARTER_MONTH_DURATION = 3;
    static final Integer YEAR_START_MONTH = 1;

    @TestVisible
    private List<wbl_EmployerTimeLog__c> mismatchingEmpTimeLogs;

    public void sendNotificationsForMismatchingLogs(List<wbl_EmployerTimeLog__c> empTimeLogs) {
        Map<Id, List<wbl_TimeLog__c>> jobAppIdToTimeLogs = getApplicantTimeLogsInSameDateRange(empTimeLogs);
        this.mismatchingEmpTimeLogs = findMismatchingTimeLogs(empTimeLogs, jobAppIdToTimeLogs);

        if (this.mismatchingEmpTimeLogs.isEmpty()) {
            return;
        }

        Set<Id> mismatchingEmpTimeLogIds = new Map<Id, wbl_EmployerTimeLog__c>(mismatchingEmpTimeLogs).keySet();
        List<wbl_EmployerTimeLog__c> empTimeLogsWithUsers = wbl_Selector.getEmployerTimeLogsWithUserFields(
            mismatchingEmpTimeLogIds
        );
        sendEmailsForMismatchingLogs(empTimeLogsWithUsers);
    }

    private Map<Id, List<wbl_TimeLog__c>> getApplicantTimeLogsInSameDateRange(
        List<wbl_EmployerTimeLog__c> empTimeLogs
    ) {
        Set<Id> jobAppIds = new Set<Id>();
        List<Date> firstEmpTimeLogDates = getEmployerTimeLogDates(empTimeLogs.get(0));
        Date minStartDate = firstEmpTimeLogDates.get(0);
        Date maxEndDate = firstEmpTimeLogDates.get(1);

        for (wbl_EmployerTimeLog__c empTimeLog : empTimeLogs) {
            jobAppIds.add(empTimeLog.JobApplication__c);
            List<Date> empTimeLogDates = getEmployerTimeLogDates(empTimeLog);
            minStartDate = empTimeLogDates.get(0) < minStartDate ? empTimeLogDates.get(0) : minStartDate;
            maxEndDate = empTimeLogDates.get(1) > maxEndDate ? empTimeLogDates.get(1) : maxEndDate;
        }

        List<wbl_TimeLog__c> timeLogs = wbl_Selector.getTimeLogsByJobApplicationIdsForDates(
            jobAppIds,
            minStartDate,
            maxEndDate
        );
        Map<Id, List<wbl_TimeLog__c>> jobAppIdToTimeLogs = new Map<Id, List<wbl_TimeLog__c>>();

        for (wbl_TimeLog__c timeLog : timeLogs) {
            if (!jobAppIdToTimeLogs.containsKey(timeLog.JobApplication__c)) {
                jobAppIdToTimeLogs.put(timeLog.JobApplication__c, new List<wbl_TimeLog__c>{ timeLog });
                continue;
            }
            jobAppIdToTimeLogs.get(timeLog.JobApplication__c).add(timeLog);
        }

        return jobAppIdToTimeLogs;
    }

    private List<wbl_EmployerTimeLog__c> findMismatchingTimeLogs(
        List<wbl_EmployerTimeLog__c> empTimeLogs,
        Map<Id, List<wbl_TimeLog__c>> jobAppIdToTimeLogs
    ) {
        List<wbl_EmployerTimeLog__c> empTimeLogsWithMismatch = new List<wbl_EmployerTimeLog__c>();

        for (wbl_EmployerTimeLog__c empTimeLog : empTimeLogs) {
            List<Date> empTimeLogDates = getEmployerTimeLogDates(empTimeLog);
            List<wbl_TimeLog__c> jobAppTimeLogs = jobAppIdToTimeLogs.get(empTimeLog.JobApplication__c);
            jobAppTimeLogs = jobAppTimeLogs == null ? new List<wbl_TimeLog__c>() : jobAppTimeLogs;

            Decimal applicantLoggedHours = 0;

            for (wbl_TimeLog__c timeLog : jobAppTimeLogs) {
                Boolean isInEmployerLogPeriod =
                    empTimeLogDates.get(0) <= timeLog.StartDate__c &&
                    empTimeLogDates.get(1) >= timeLog.EndDate__c;
                if (isInEmployerLogPeriod) {
                    applicantLoggedHours += timeLog.SpentHours__c;
                    continue;
                }
            }

            if (empTimeLog.SpentHours__c != applicantLoggedHours) {
                empTimeLogsWithMismatch.add(empTimeLog);
            }
        }

        return empTimeLogsWithMismatch;
    }

    private void sendEmailsForMismatchingLogs(List<wbl_EmployerTimeLog__c> empTimeLogs) {
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        wbl_Settings__c settings = wbl_Settings__c.getInstance();
        EmailTemplate template = wbl_Selector.getEmailTemplate(settings.TimeLogMismatchEmailTemplate__c);
        List<String> staffEmails = getInternalStaffEmails();

        for (wbl_EmployerTimeLog__c empTimeLog : empTimeLogs) {
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            List<Date> empTimeLogDates = getEmployerTimeLogDates(empTimeLog);

            String logPeriod = empTimeLogDates.get(0).format() + ' - ' + empTimeLogDates.get(1).format();
            String body = template.HtmlValue.replace(
                    '{!applicantName}',
                    empTimeLog.JobApplication__r.JobBoard__First_Name__c +
                    empTimeLog.JobApplication__r.JobBoard__Last_Name__c
                )
                .replace('{!employerLogPeriod}', logPeriod)
                .replace('{!employerName}', empTimeLog.CreatedBy.Name);

            List<String> allRecipientEmails = new List<String>(staffEmails);
            allRecipientEmails.add(empTimeLog.CreatedBy.Email);

            email.setToAddresses(allRecipientEmails);
            email.setSubject(template.Subject);
            email.setHtmlBody(body);
            if (String.isNotBlank(settings.OrgWideEmailId__c)) {
                email.setOrgWideEmailAddressId(settings.OrgWideEmailId__c);
            }
            emails.add(email);
        }

        if (!Test.isRunningTest()) {
            Messaging.sendEmail(emails);
        }
    }

    @TestVisible
    private static List<Date> getEmployerTimeLogDates(wbl_EmployerTimeLog__c empTimeLog) {
        Integer quarterNumber = Integer.valueOf(empTimeLog.Quarter__c);
        Integer quarterMonthOffset = quarterNumber > 1 ? (quarterNumber - 1) * QUARTER_MONTH_DURATION : 0;
        Date startDate = Date.newInstance(
            Integer.valueOf(empTimeLog.Year__c),
            YEAR_START_MONTH + quarterMonthOffset,
            1
        );
        Date endDate = startDate.addMonths(QUARTER_MONTH_DURATION).toStartOfMonth().addDays(-1);

        return new List<Date>{ startDate, endDate };
    }

    private List<String> getInternalStaffEmails() {
        List<String> staffEmails = new List<String>();
        Id groupId = (Id) wbl_Settings__c.getInstance().InternalStaffGroupId__c;

        if (groupId == null) {
            return staffEmails;
        }

        List<User> staffUsers = wbl_Selector.getGroupMemberUsersWs(groupId);

        for (User staffUser : staffUsers) {
            staffEmails.add(staffUser.Email);
        }
        return staffEmails;
    }
}