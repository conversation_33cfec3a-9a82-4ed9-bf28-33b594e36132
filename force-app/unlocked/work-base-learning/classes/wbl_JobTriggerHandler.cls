public with sharing class wbl_JobTriggerHandler {
    private static Set<Id> notifiableJobIds = new Set<Id>();
    private System.TriggerOperation triggerOperation;
    private List<JobBoard__Job__c> jobsNew;
    private Map<Id, JobBoard__Job__c> idToJobOld;

    public wbl_JobTriggerHandler(
        System.TriggerOperation triggerOperation,
        List<JobBoard__Job__c> jobsNew,
        Map<Id, JobBoard__Job__c> idToJobOld
    ) {
        this.triggerOperation = triggerOperation;
        this.jobsNew = jobsNew;
        this.idToJobOld = idToJobOld;
    }

    public void run() {
        switch on this.triggerOperation {
            when BEFORE_INSERT {
                beforeInsert();
            }
            when AFTER_INSERT {
                afterInsert();
            }
            when BEFORE_UPDATE {
                beforeUpdate();
            }
            when AFTER_UPDATE {
                afterUpdate();
            }
            when AFTER_DELETE {
                afterDelete();
            }
        }
    }

    private void beforeInsert() {
        setDefaultCountry();
        assignDefaultRecordType();
        approveEligibleJobs();
    }

    private void afterInsert() {
        wbl_JobAccessService.createJobAccessesForJobOwners(this.jobsNew);
        wbl_JobAccessService.createJobAccessesForPartnerLeads(this.jobsNew);
        updateReservedHoursOnOccupations();
        updateSpentHoursOnOccupations();
        notifyAboutJobsForApproval();
    }

    private void beforeUpdate() {
        resetApprovalForEditedJobs();
    }

    private void afterUpdate() {
        createAccessRecordsForNewJobOwners();
        updateReservedHoursOnOccupations();
        updateSpentHoursOnOccupations();
        notifyAboutJobsForApproval();
    }

    private void afterDelete() {
        updateReservedHoursOnOccupations();
        updateSpentHoursOnOccupations();
    }

    private void assignDefaultRecordType() {
        User currentUser = wbl_Selector.getCurrentUserWithContact();
        Boolean isRapUser = currentUser.Account.wbl_IsRapCompany__c;
        Map<String, Schema.RecordTypeInfo> nameToJobRt = Schema.SObjectType.JobBoard__Job__c.getRecordTypeInfosByDeveloperName();

        for (JobBoard__Job__c job : this.jobsNew) {
            if (job.RecordTypeId == null) {
                job.RecordTypeId = isRapUser
                    ? wbl_JobService.getRapRecordTypeId()
                    : wbl_JobService.getWblRecordTypeId();
            }
        }
    }

    private void setDefaultCountry() {
        String defaultValue = wbl_Settings__c.getInstance()?.DefaultJobCountry__c;

        if (String.isBlank(defaultValue)) {
            return;
        }

        for (JobBoard__Job__c job : this.jobsNew) {
            if (String.isBlank(job.JobBoard__Country__c)) {
                job.JobBoard__Country__c = defaultValue;
            }
        }
    }

    private void approveEligibleJobs() {
        wbl_Settings__c settings = wbl_Settings__c.getInstance();

        for (JobBoard__Job__c job : this.jobsNew) {
            if (job.wbl_IsApproved__c) {
                continue;
            }

            job.wbl_IsApproved__c = wbl_JobService.isJobAutoapprovable(job);

            if (!job.wbl_IsApproved__c) {
                notifiableJobIds.add(job.Id);
            }
        }
    }

    private void createAccessRecordsForNewJobOwners() {
        List<JobBoard__Job__c> changedOwnerJobs = new List<JobBoard__Job__c>();

        for (JobBoard__Job__c jobNew : this.jobsNew) {
            JobBoard__Job__c jobOld = this.idToJobOld.get(jobNew.Id);
            if (jobNew.OwnerId != jobOld.OwnerId) {
                changedOwnerJobs.add(jobNew);
            }
        }

        if (changedOwnerJobs.isEmpty()) {
            return;
        }

        wbl_JobAccessService.createJobAccessesForJobOwners(changedOwnerJobs);
    }

    private void updateSpentHoursOnOccupations() {
        List<JobBoard__Job__c> changedJobs = this.triggerOperation == System.TriggerOperation.AFTER_DELETE
            ? this.idToJobOld.values()
            : wbl_TriggerHelper.filterRecordsWithChangedField(
                  JobBoard__Job__c.wbl_SpentHours__c,
                  this.jobsNew,
                  this.idToJobOld
              );

        if (changedJobs.isEmpty()) {
            return;
        }

        wbl_SummaryService summaryService = new wbl_SummaryService(
            changedJobs,
            JobBoard__Job__c.wbl_SpentHours__c,
            wbl_JobOccupation__c.SpentHours__c,
            JobBoard__Job__c.wbl_JobOccupation__c
        );

        if (this.triggerOperation != System.TriggerOperation.AFTER_DELETE) {
            summaryService.validateSummarizedRecordsAgainstLimit(
                wbl_JobOccupation__c.NumberOfHours__c,
                System.Label.wblHourLimitForOccupationExceededErr
            );
        }

        summaryService.updateSummaryRecords();
    }

    private void updateReservedHoursOnOccupations() {
        List<JobBoard__Job__c> changedJobs = this.triggerOperation == System.TriggerOperation.AFTER_DELETE
            ? this.idToJobOld.values()
            : wbl_TriggerHelper.filterRecordsWithChangedField(
                  JobBoard__Job__c.wbl_UsedOccupationHoursLimit__c,
                  this.jobsNew,
                  this.idToJobOld
              );

        if (changedJobs.isEmpty()) {
            return;
        }

        wbl_SummaryService summaryService = new wbl_SummaryService(
            changedJobs,
            JobBoard__Job__c.wbl_UsedOccupationHoursLimit__c,
            wbl_JobOccupation__c.UsedHoursLimit__c,
            JobBoard__Job__c.wbl_JobOccupation__c
        );

        if (this.triggerOperation != System.TriggerOperation.AFTER_DELETE) {
            summaryService.validateSummarizedRecordsAgainstLimit(
                wbl_JobOccupation__c.NumberOfHours__c,
                System.Label.wblHourLimitForOccupationExceededErr
            );
        }
        summaryService.updateSummaryRecords();
    }

    private void resetApprovalForEditedJobs() {
        if (FeatureManagement.checkPermission(wbl_Constant.SKIP_JOB_APPROVE_RESET_PERMISSION)) {
            return;
        }

        String resettingFieldSetName = wbl_Settings__c.getInstance().JobApprovalResetFieldSet__c;
        List<Schema.FieldSetMember> resettingFieldSetMembers = new List<Schema.FieldSetMember>();

        if (String.isNotBlank(resettingFieldSetName)) {
            Schema.FieldSet resettingFieldSet = Schema.SObjectType.JobBoard__Job__c.fieldSets.getMap()
                .get(resettingFieldSetName);
            if (resettingFieldSet != null) {
                resettingFieldSetMembers = resettingFieldSet.getFields();
            }
        }

        for (JobBoard__Job__c jobNew : this.jobsNew) {
            if (wbl_JobService.isJobAutoapprovable(jobNew)) {
                continue;
            }

            JobBoard__Job__c jobOld = this.idToJobOld.get(jobNew.Id);
            Boolean isJobReactivated = !jobOld.wbl_IsActive__c && jobNew.wbl_IsActive__c;
            jobNew.wbl_IsApproved__c = jobNew.wbl_IsApproved__c && !isJobReactivated;

            if (!jobNew.wbl_IsApproved__c) {
                notifiableJobIds.add(jobNew.Id);
                continue;
            }

            for (Schema.FieldSetMember fieldSetMember : resettingFieldSetMembers) {
                String fieldName = fieldSetMember.getFieldPath();
                jobNew.wbl_IsApproved__c = jobNew.get(fieldName) == jobOld.get(fieldName);

                if (!jobNew.wbl_IsApproved__c) {
                    notifiableJobIds.add(jobNew.Id);
                    break;
                }
            }
        }
    }

    private void notifyAboutJobsForApproval() {
        wbl_Settings__c settings = wbl_Settings__c.getInstance();
        Id recipientQueueId = wbl_Settings__c.getInstance().JobApproversQueueId__c;
        if (recipientQueueId == null) {
            return;
        }
        List<JobBoard__Job__c> unapprovedJobs = filterUnapprovedJobs();

        //preventing mass creation/update of jobs by internal users to trigger emails
        if (unapprovedJobs.size() == 1 && notifiableJobIds.contains(unapprovedJobs.get(0).Id)) {
            wbl_EmailNotifier.WithoutSharing notifier = new wbl_EmailNotifier.WithoutSharing(
                recipientQueueId,
                unapprovedJobs.get(0).Id,
                settings.NewJobEmailTemplate__c
            );
            System.enqueueJob(notifier);
        }
    }

    private List<JobBoard__Job__c> filterUnapprovedJobs() {
        List<JobBoard__Job__c> unapprovedJobs = new List<JobBoard__Job__c>();

        for (JobBoard__Job__c jobNew : jobsNew) {
            JobBoard__Job__c jobOld = this.idToJobOld?.get(jobNew.Id);

            if ((jobOld == null || jobOld.wbl_IsApproved__c) && !jobNew.wbl_IsApproved__c) {
                unapprovedJobs.add(jobNew);
            }
        }
        return unapprovedJobs;
    }
}