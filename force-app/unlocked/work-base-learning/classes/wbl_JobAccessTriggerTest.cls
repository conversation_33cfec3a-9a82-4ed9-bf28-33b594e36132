@IsTest
class wbl_JobAccessTriggerTest {
    static final String MAIN_JOB_PROVIDER_EMAIL = '<EMAIL>';
    static final String SECONDARY_JOB_PROVIDER_EMAIL = '<EMAIL>';
    static final Integer SETUP_JOB_COUNT = 5;

    @TestSetup
    static void setup() {
        User adminUser = wbl_TestDataFactory.createAdminUser();
        insert adminUser;
        wbl_TestDataFactory.assignWblPermissionSet(adminUser.Id);
        wbl_TestDataFactory.assignJobProviderPermissionSet(adminUser.Id);

        User mainJobProvider;
        User secondJobProvider;

        System.runAs(adminUser) {
            Account jobProviderAcc = new Account(Name = 'Job Provider Account', wbl_IsRapCompany__c = true);
            insert jobProviderAcc;

            Contact mainJobProviderCon = new Contact(
                FirstName = 'Main',
                LastName = 'Job Provider',
                Email = MAIN_JOB_PROVIDER_EMAIL,
                AccountId = jobProviderAcc.Id
            );
            Contact secondJobProviderCon = new Contact(
                FirstName = 'Secondary',
                LastName = 'Job Provider',
                Email = SECONDARY_JOB_PROVIDER_EMAIL,
                AccountId = jobProviderAcc.Id
            );
            insert new List<Contact>{ mainJobProviderCon, secondJobProviderCon };

            mainJobProvider = wbl_TestDataFactory.createExternalUser(mainJobProviderCon);
            secondJobProvider = wbl_TestDataFactory.createExternalUser(secondJobProviderCon);
            insert new List<User>{ mainJobProvider, secondJobProvider };

            wbl_TestDataFactory.assignJobProviderPermissionSet(mainJobProvider.Id);
            wbl_TestDataFactory.assignJobProviderPermissionSet(secondJobProvider.Id);
            //At least Portal User Visibility expected to be enabled, in test using Apex sharing
            insert new UserShare(
                UserAccessLevel = 'Read',
                UserOrGroupId = mainJobProvider.Id,
                UserId = secondJobProvider.Id,
                RowCause = 'Manual'
            );
        }

        System.runAs(mainJobProvider) {
            insert wbl_TestDataFactory.createJobs(SETUP_JOB_COUNT);
        }
    }

    @IsTest
    static void afterInsert() {
        User mainJobProvider = wbl_TestDataFactory.getUser(MAIN_JOB_PROVIDER_EMAIL);
        User secondJobProvider = wbl_TestDataFactory.getUser(SECONDARY_JOB_PROVIDER_EMAIL);

        List<JobBoard__Job__c> jobs = [SELECT Id FROM JobBoard__Job__c WHERE OwnerId = :mainJobProvider.Id];
        Assert.areEqual(SETUP_JOB_COUNT, jobs.size(), 'Setup method failed to create predefined amount of jobs');

        List<wbl_JobAccess__c> jobAccesses = new List<wbl_JobAccess__c>();
        for (JobBoard__Job__c job : jobs) {
            jobAccesses.add(new wbl_JobAccess__c(User__c = secondJobProvider.Id, Job__c = job.Id));
        }

        Test.startTest();
        System.runAs(mainJobProvider) {
            insert jobAccesses;
        }
        Test.stopTest();

        Set<Id> jobIds = new Map<Id, JobBoard__Job__c>(jobs).keySet();
        Integer sharingRecordCount = queryJobSharingRecordCount(jobIds, secondJobProvider.Id);
        List<JobBoard__Job__c> jobsAfterAccessesInsert = [
            SELECT wbl_JobAccessUserIds__c
            FROM JobBoard__Job__c
            WHERE Id IN :jobs
        ];
        Boolean jobAccessIdsUpdated = true;
        for (JobBoard__Job__c job : jobsAfterAccessesInsert) {
            if (!job.wbl_JobAccessUserIds__c.contains(secondJobProvider.Id)) {
                jobAccessIdsUpdated = false;
                break;
            }
        }

        Assert.areEqual(
            jobs.size(),
            sharingRecordCount,
            'Job provider granted with access to jobs via wbl_JobAccess__c does ' +
            'not have edit permissions to all jobs'
        );
        Assert.isTrue(
            jobAccessIdsUpdated,
            'Second job provider id not included in wbl_JobAccessUserIds__c field of jobs shared with wbl_JobAccess__c'
        );
    }

    @IsTest
    static void afterInsertNoJobEditPermissionFail() {
        User mainJobProvider = wbl_TestDataFactory.getUser(MAIN_JOB_PROVIDER_EMAIL);
        User secondJobProvider = wbl_TestDataFactory.getUser(SECONDARY_JOB_PROVIDER_EMAIL);

        List<JobBoard__Job__c> jobs = [SELECT Id FROM JobBoard__Job__c WHERE OwnerId = :mainJobProvider.Id];

        List<wbl_JobAccess__c> jobAccesses = new List<wbl_JobAccess__c>();
        for (JobBoard__Job__c job : jobs) {
            jobAccesses.add(new wbl_JobAccess__c(User__c = secondJobProvider.Id, Job__c = job.Id));
        }

        Test.startTest();
        //running as another job provider which has no edit access to the job
        System.runAs(secondJobProvider) {
            try {
                insert jobAccesses;
                Assert.fail(
                    'Insertion of job access records should fail if inserted by user with no edit permission to the job'
                );
            } catch (Exception e) {
                Assert.isNotNull(e.getMessage(), 'Exception message cannot be null');
            }
        }
        Test.stopTest();
    }

    @IsTest
    static void afterDelete() {
        User mainJobProvider = wbl_TestDataFactory.getUser(MAIN_JOB_PROVIDER_EMAIL);
        User secondJobProvider = wbl_TestDataFactory.getUser(SECONDARY_JOB_PROVIDER_EMAIL);

        List<JobBoard__Job__c> jobs = [SELECT Id FROM JobBoard__Job__c WHERE OwnerId = :mainJobProvider.Id];

        List<wbl_JobAccess__c> jobAccesses = new List<wbl_JobAccess__c>();
        for (JobBoard__Job__c job : jobs) {
            jobAccesses.add(new wbl_JobAccess__c(User__c = secondJobProvider.Id, Job__c = job.Id));
        }

        System.runAs(mainJobProvider) {
            insert jobAccesses;
        }

        Set<Id> jobIds = new Map<Id, JobBoard__Job__c>(jobs).keySet();
        Integer sharingCountBeforeTest = queryJobSharingRecordCount(jobIds, secondJobProvider.Id);
        Assert.areEqual(
            jobs.size(),
            sharingCountBeforeTest,
            'Failed to share jobs to another job provider before delete test'
        );

        Test.startTest();
        System.runAs(mainJobProvider) {
            delete jobAccesses;
        }
        Test.stopTest();

        Integer sharingCountAfterTest = queryJobSharingRecordCount(jobIds, secondJobProvider.Id);
        Assert.areEqual(
            0,
            sharingCountAfterTest,
            'After job access records deleted no sharing records for job should be left'
        );
    }

    static Integer queryJobSharingRecordCount(Set<Id> jobIds, Id sharedToId) {
        return [
            SELECT COUNT()
            FROM JobBoard__Job__Share
            WHERE
                ParentId IN :jobIds
                AND AccessLevel = :wbl_JobAccessService.JOB_ACCESS_LEVEL
                AND UserOrGroupId = :sharedToId
        ];
    }
}