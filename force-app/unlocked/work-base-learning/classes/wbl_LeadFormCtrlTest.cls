@IsTest
class wbl_LeadFormCtrlTest {
    static final String EXTERNAL_USER_EMAIL = '<EMAIL>';
    static final String APPROVER_QUEUE_NAME = 'wbl_LeadPartnerApprovers';

    @TestSetup
    static void setup() {
        User adminUser = wbl_TestDataFactory.createAdminUser();
        User unprivilegedUser = wbl_TestDataFactory.createUnprivilegedUser();
        insert new List<User>{ adminUser, unprivilegedUser };
        wbl_TestDataFactory.assignWblPermissionSet(adminUser.Id);

        System.runAs(adminUser) {
            Account communityAcc = new Account(Name = 'Community Account');
            insert communityAcc;

            Contact con = new Contact(
                FirstName = 'External',
                LastName = 'WBL Contact',
                Email = EXTERNAL_USER_EMAIL,
                AccountId = communityAcc.Id
            );
            insert con;

            User externalUser = wbl_TestDataFactory.createExternalUser(con);
            insert externalUser;

            Group approverQueue = [SELECT Id FROM Group WHERE DeveloperName = :APPROVER_QUEUE_NAME];
            wbl_Settings__c settings = wbl_TestDataFactory.createCustomSettings(adminUser.Id);
            settings.LeadFormLayoutName__c = null;
            settings.LeadPartnerQueueId__c = approverQueue.Id;
            insert settings;
            insert new GroupMember(UserOrGroupId = adminUser.Id, GroupId = approverQueue.Id);
        }
    }

    @IsTest
    static void getFormFail() {
        Test.startTest();
        System.runAs(wbl_TestDataFactory.getUser(EXTERNAL_USER_EMAIL)) {
            try {
                wbl_LeadFormCtrl.getForm();
                Assert.fail('getForm() method expected to fail because there is no layout specified in settings');
            } catch (Exception e) {
                Assert.isNotNull(e.getMessage(), 'exception message cannot be null');
            }
        }
        Test.stopTest();
    }

    @IsTest
    static void saveRecordSuccess() {
        aclab.GoogleReCaptchaService.IS_CAPTCHA_DISABLED = true;

        Lead leadRecord = new Lead(
            FirstName = 'Lead',
            LastName = 'Save Success',
            Company = 'Apex Test Ltd.',
            Email = '<EMAIL>',
            Description = 'Should be removed'
        );

        Test.startTest();
        System.runAs(wbl_TestDataFactory.getUser(EXTERNAL_USER_EMAIL)) {
            wbl_LeadFormCtrl.saveRecord(leadRecord, 'token');
        }
        Test.stopTest();

        Lead leadAfterInsert = [
            SELECT FirstName, LastName, Email, Company, Description
            FROM Lead
            WHERE Email = :leadRecord.Email
        ];

        Assert.areEqual(
            leadRecord.FirstName,
            leadAfterInsert.FirstName,
            'saveRecord() saved lead with invalid FirstName'
        );
        Assert.areEqual(leadRecord.LastName, leadAfterInsert.LastName, 'saveRecord() saved lead with invalid LastName');
        Assert.areEqual(leadRecord.Email, leadAfterInsert.Email, 'saveRecord() saved lead with invalid Email');
        Assert.areEqual(leadRecord.Company, leadAfterInsert.Company, 'saveRecord() saved lead with invalid Company');
    }

    @IsTest
    static void saveRecordFail() {
        aclab.GoogleReCaptchaService.IS_CAPTCHA_DISABLED = true;

        Lead leadRecord = new Lead(
            FirstName = 'Lead',
            LastName = 'Save Fail',
            Company = 'Apex Test Inc.',
            Email = '<EMAIL>'
        );

        Test.startTest();
        System.runAs(wbl_TestDataFactory.getUnprivilegedUser()) {
            try {
                wbl_LeadFormCtrl.saveRecord(leadRecord, 'token');
                Assert.fail('saveRecord() should fail when invoked by unprivileged user');
            } catch (Exception e) {
                Assert.isNotNull(e.getMessage(), 'Exception message cannot be null');
            }
        }
        Test.stopTest();
    }
}