public with sharing class wbl_InvitePartnerCompanyCtrl {
    @AuraEnabled
    public static void sendInvitationEmail(String recipientEmail) {
        Boolean hasInvitePermission = FeatureManagement.checkPermission(wbl_Constant.INVITE_WBL_USERS_PERMISSION);
        if (!hasInvitePermission) {
            throw Error.toLWC(System.Label.wblNoInvitationPermissionErr);
        }

        try {
            wbl_Settings__c settings = wbl_Settings__c.getInstance();
            String emailTemplateName = settings.PartnerInvitationEmailTemplate__c;
            Map<String, String> mergeFields = new Map<String, String>{
                '{invitationUrl}' => settings.PartnerLeadCreationPageUrl__c
            };

            wbl_EmailNotifier notifier = new wbl_EmailNotifier.WithoutSharing(
                new List<String>{ recipientEmail },
                emailTemplateName
            );
            notifier.setCustomMergeFields(mergeFields);
            notifier.execute(null);
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
}