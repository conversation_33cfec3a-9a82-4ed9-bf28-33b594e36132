@IsTest
class wbl_OnboardingFormCtrlTest {
    static final String EXTERNAL_USER_EMAIL = '<EMAIL>';
    static final String SETUP_JOB_NAME = 'Setup Job';
    static final String APPROVER_QUEUE_NAME = 'wbl_OnboardingFormApprovers';
    static User externalUser;

    @TestSetup
    static void setup() {
        User adminUser = wbl_TestDataFactory.createAdminUser();
        User unprivilegedUser = wbl_TestDataFactory.createUnprivilegedUser();
        insert new List<User>{ adminUser, unprivilegedUser };
        wbl_TestDataFactory.assignWblPermissionSet(adminUser.Id);

        User externalUser;
        JobBoard__Job__c job;
        Contact externalUserContact;
        JobBoard__Job_Application__c externalUserJobApp;

        System.runAs(adminUser) {
            Account communityAcc = new Account(Name = 'Community Account');
            insert communityAcc;

            externalUserContact = new Contact(
                FirstName = 'External',
                LastName = 'WBL Contact',
                Email = EXTERNAL_USER_EMAIL,
                AccountId = communityAcc.Id
            );
            insert externalUserContact;

            externalUser = wbl_TestDataFactory.createExternalUser(externalUserContact);
            insert externalUser;
            wbl_TestDataFactory.assignJobApplicantPermissionSet(externalUser.Id);

            job = wbl_TestDataFactory.createJobs(1).get(0);
            job.Name = SETUP_JOB_NAME;
            insert job;

            Group approverQueue = [SELECT Id FROM Group WHERE DeveloperName = :APPROVER_QUEUE_NAME];
            wbl_Settings__c settings = wbl_TestDataFactory.createCustomSettings(adminUser.Id);
            settings.OnboardingFormApproversQueueId__c = approverQueue.Id;
            settings.OnboardingFormLayoutName__c = null;
            insert settings;
            insert new GroupMember(UserOrGroupId = adminUser.Id, GroupId = approverQueue.Id);
        }

        System.runAs(externalUser) {
            externalUserJobApp = wbl_TestDataFactory.createJobApplications(
                    job,
                    new List<Contact>{ externalUserContact }
                )
                .get(0);
            insert externalUserJobApp;
        }

        System.runAs(adminUser) {
            externalUserJobApp.wbl_Status__c = wbl_Constant.HIRED_JOB_APP_STATUS;
            update externalUserJobApp;
        }
    }

    @IsTest
    static void getJobApplicationForOnboardingSuccess() {
        User externalUser = getSetupExternalUser();
        JobBoard__Job_Application__c jobApp = getSetupJobApplication();
        JobBoard__Job_Application__c retrievedJobApp;

        Test.startTest();
        System.runAs(externalUser) {
            retrievedJobApp = wbl_OnboardingFormCtrl.getJobApplicationForOnboarding(jobApp.Id);
        }
        Test.stopTest();

        Assert.isNotNull(
            retrievedJobApp,
            'getJobApplicationForOnboarding() should have returned job application for external user'
        );
        Assert.areEqual(
            jobApp.Id,
            retrievedJobApp.Id,
            'getJobApplicationForOnboarding() returned job application with invalid id'
        );

        Assert.isNotNull(
            retrievedJobApp.JobBoard__Last_Name__c,
            'getJobApplicationForOnboarding() should returned application with last name'
        );
    }

    @IsTest
    static void getJobApplicationForOnboardingFail() {
        User externalUser = getSetupExternalUser();
        JobBoard__Job_Application__c jobApp = getSetupJobApplication();

        Test.startTest();
        System.runAs(wbl_TestDataFactory.getUnprivilegedUser()) {
            try {
                wbl_OnboardingFormCtrl.getJobApplicationForOnboarding(jobApp.Id);
                Assert.fail(
                    'getJobApplicationForOnboarding() should thrown an exception when called by unprivileged user'
                );
            } catch (Exception e) {
                Assert.isNotNull(e.getMessage(), 'Exception message cannot be null');
            }
        }
        Test.stopTest();
    }

    @IsTest
    static void getFormFail() {
        Test.startTest();
        System.runAs(getSetupExternalUser()) {
            try {
                wbl_OnboardingFormCtrl.getForm();
                Assert.fail('getForm() method expected to fail because there is no layout specified in settings');
            } catch (Exception e) {
                Assert.isNotNull(e.getMessage(), 'exception message cannot be null');
            }
        }
        Test.stopTest();
    }

    @IsTest
    static void saveRecordSuccess() {
        User externalUser = getSetupExternalUser();
        JobBoard__Job_Application__c jobApp = getSetupJobApplication();

        wbl_OnboardingForm__c formRecord = new wbl_OnboardingForm__c(
            FirstName__c = 'First Name',
            LastName__c = 'Last Name',
            PersonalEmail__c = EXTERNAL_USER_EMAIL,
            DateOfBirth__c = Date.today().addDays(-365 * 20)
        );

        Test.startTest();
        System.runAs(externalUser) {
            wbl_OnboardingFormCtrl.saveRecord(jobApp.Id, formRecord);
        }
        Test.stopTest();

        Integer createdRecordCount = [
            SELECT COUNT()
            FROM wbl_OnboardingForm__c
            WHERE
                FirstName__c = :formRecord.FirstName__c
                AND LastName__c = :formRecord.LastName__c
                AND PersonalEmail__c = :formRecord.PersonalEmail__c
                AND DateOfBirth__c = :formRecord.DateOfBirth__c
        ];

        Assert.areEqual(1, createdRecordCount, 'saveRecord() failed to create record with provided field values');
    }

    @IsTest
    static void saveRecordWithOnboardingPassedFail() {
        User externalUser = getSetupExternalUser();
        JobBoard__Job_Application__c jobApp = getSetupJobApplication();

        wbl_OnboardingForm__c formRecord = new wbl_OnboardingForm__c(
            FirstName__c = 'First Name',
            LastName__c = 'Last Name',
            PersonalEmail__c = EXTERNAL_USER_EMAIL,
            DateOfBirth__c = Date.today().addDays(-365 * 20)
        );
        insert formRecord;

        jobApp.wbl_OnboardingForm__c = formRecord.Id;
        update jobApp;

        Test.startTest();
        System.runAs(externalUser) {
            try {
                wbl_OnboardingFormCtrl.saveRecord(jobApp.Id, formRecord);
                Assert.fail('saveRecord() should fail if onboarding form record already created');
            } catch (Exception e) {
                Assert.areEqual(
                    System.Label.wblJobAppWithOnboardingErr,
                    e.getMessage(),
                    'saveRecord() should throw exception with System.Label.wblJobAppWithOnboardingErr message if onboarding already completed'
                );
            }
        }
        Test.stopTest();
    }

    @IsTest
    static void saveRecordPermissionFail() {
        wbl_OnboardingForm__c formRecord = new wbl_OnboardingForm__c(
            FirstName__c = 'First Name',
            LastName__c = 'Last Name',
            PersonalEmail__c = EXTERNAL_USER_EMAIL,
            DateOfBirth__c = Date.today().addDays(-365 * 20)
        );

        Test.startTest();
        System.runAs(wbl_TestDataFactory.getUnprivilegedUser()) {
            try {
                wbl_OnboardingFormCtrl.saveRecord(null, formRecord);
                Assert.fail('saveRecord() should fail when invoked by unprivileged user');
            } catch (Exception e) {
                Assert.isNotNull(e.getMessage(), 'Exception message cannot be null');
            }
        }
        Test.stopTest();
    }

    static JobBoard__Job_Application__c getSetupJobApplication() {
        User externalUser = getSetupExternalUser();
        List<JobBoard__Job_Application__c> jobApps = [
            SELECT Id
            FROM JobBoard__Job_Application__c
            WHERE
                CreatedById = :externalUser.Id
                AND wbl_Status__c = :wbl_Constant.HIRED_JOB_APP_STATUS
                AND JobBoard__Job__r.Name = :SETUP_JOB_NAME
        ];
        Assert.isFalse(jobApps.isEmpty(), 'Setup method failed to created hired job application for external user');
        return jobApps.get(0);
    }

    static User getSetupExternalUser() {
        if (externalUser == null) {
            externalUser = wbl_TestDataFactory.getUser(EXTERNAL_USER_EMAIL);
        }
        return externalUser;
    }
}