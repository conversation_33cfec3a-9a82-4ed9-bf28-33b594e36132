public with sharing class wbl_JobAccessTriggerHandler {
    private System.TriggerOperation triggerOperation;
    private List<wbl_JobAccess__c> jobAccessesNew;
    private Map<Id, wbl_JobAccess__c> idToJobAccessOld;

    public wbl_JobAccessTriggerHandler(
        System.TriggerOperation triggerOperation,
        List<wbl_JobAccess__c> jobAccessesNew,
        Map<Id, wbl_JobAccess__c> idToJobAccessOld
    ) {
        this.triggerOperation = triggerOperation;
        this.jobAccessesNew = jobAccessesNew;
        this.idToJobAccessOld = idToJobAccessOld;
    }

    public void run() {
        switch on this.triggerOperation {
            when AFTER_INSERT {
                afterInsert();
            }
            when BEFORE_DELETE {
                beforeDelete();
            }
            when AFTER_DELETE {
                afterDelete();
            }
        }
    }

    private void afterInsert() {
        wbl_JobAccessService.shareJobAndChildRecords(this.jobAccessesNew);
        wbl_JobAccessService.updateJobAccessUserIdOnJobs(this.jobAccessesNew);
        wbl_JobAccessService.shareAccessRecordsToUsersWithAccess(this.jobAccessesNew);
    }

    private void beforeDelete() {
        preventPartnerLeadAccessDelete();
    }

    private void afterDelete() {
        wbl_JobAccessService.removeJobAndChildRecordsShare(this.idToJobAccessOld.values());
        wbl_JobAccessService.updateJobAccessUserIdOnJobs(this.idToJobAccessOld.values());
    }

    private void preventPartnerLeadAccessDelete() {
        Map<Id, List<wbl_JobAccess__c>> userIdToJobAccesses = new Map<Id, List<wbl_JobAccess__c>>();

        for (wbl_JobAccess__c jobAccess : this.idToJobAccessOld.values()) {
            if (!userIdToJobAccesses.containsKey(jobAccess.User__c)) {
                userIdToJobAccesses.put(jobAccess.User__c, new List<wbl_JobAccess__c>());
            }
            userIdToJobAccesses.get(jobAccess.User__c).add(jobAccess);
        }

        String partnerLeadQuery = 'SELECT Id FROM User WHERE Id IN :userIds AND Contact.PortalRole__c INCLUDES (:partnerLeadRole)';
        Map<String, Object> queryBinds = new Map<String, Object>{
            'userIds' => userIdToJobAccesses.keySet(),
            'partnerLeadRole' => PortalUser.ROLES.get(PortalUser.Role.PARTNER_LEAD)
        };

        List<User> partnerLeads = WS.retrieveRecords(partnerLeadQuery, queryBinds);

        for (User partnerLead : partnerLeads) {
            for (wbl_JobAccess__c jobAccess : userIdToJobAccesses.get(partnerLead.Id)) {
                jobAccess.addError(System.Label.wblJobAccessForPartnerLeadDelErr);
            }
        }
    }
}