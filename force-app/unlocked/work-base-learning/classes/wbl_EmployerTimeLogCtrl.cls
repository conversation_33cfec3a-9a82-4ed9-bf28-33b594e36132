public with sharing class wbl_EmployerTimeLogCtrl {
    @AuraEnabled
    public static List<wbl_DTO.EmployerTimeLog> getTimeLogs(Integer year, String quarter) {
        try {
            List<wbl_DTO.EmployerTimeLog> timeLogDtos = new List<wbl_DTO.EmployerTimeLog>();
            List<JobBoard__Job_Application__c> jobApps = wbl_Selector.getJobApplicationsWithEmployerLogs(year, quarter);

            for (JobBoard__Job_Application__c jobApp : jobApps) {
                wbl_DTO.EmployerTimeLog timeLogDto = new wbl_DTO.EmployerTimeLog();
                timeLogDto.jobName = jobApp.JobBoard__Job__r.Name;
                timeLogDto.jobAppId = jobApp.Id;
                timeLogDto.jobAppCreatedDate = jobApp.CreatedDate.date();
                timeLogDto.year = year;
                timeLogDto.quarter = quarter;
                timeLogDto.employeeName = String.join(
                    new List<String>{ jobApp.JobBoard__First_Name__c, jobApp.JobBoard__Last_Name__c },
                    ' '
                );

                if (!jobApp.wbl_EmployerTimeLogs__r.isEmpty()) {
                    timeLogDto.id = jobApp.wbl_EmployerTimeLogs__r.get(0).Id;
                    timeLogDto.spentHours = jobApp.wbl_EmployerTimeLogs__r.get(0).SpentHours__c;
                    timeLogDto.comment = jobApp.wbl_EmployerTimeLogs__r.get(0).Comment__c;
                }

                timeLogDtos.add(timeLogDto);
            }

            return timeLogDtos;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void saveTimeLogs(List<wbl_DTO.EmployerTimeLog> timeLogDtos) {
        try {
            List<wbl_EmployerTimeLog__c> timeLogs = new List<wbl_EmployerTimeLog__c>();

            for (wbl_DTO.EmployerTimeLog timeLogDto : timeLogDtos) {
                wbl_EmployerTimeLog__c timeLog = new wbl_EmployerTimeLog__c();
                timeLog.Id = timeLogDto.id;
                timeLog.JobApplication__c = timeLogDto.jobAppId;
                timeLog.Quarter__c = timeLogDto.quarter;
                timeLog.SpentHours__c = timeLogDto.spentHours;
                timeLog.Comment__c = timeLogDto.comment;

                timeLogs.add(timeLog);
            }

            Database.upsert(timeLogs, AccessLevel.USER_MODE);
        } catch (DmlException e) {
            throw Error.toLWC(e.getDmlMessage(0));
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}