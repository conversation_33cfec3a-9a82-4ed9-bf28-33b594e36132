@IsTest
class wbl_DocuSignJobContractSenderTest {
    static final Integer SETUP_APPLICATION_COUNT = 5;

    @TestSetup
    static void setup() {
        User adminUser = wbl_TestDataFactory.createAdminUser();
        insert adminUser;
        wbl_TestDataFactory.assignWblPermissionSet(adminUser.Id);
        wbl_TestDataFactory.assignJobProviderPermissionSet(adminUser.Id);

        User externalJobProvider;
        JobBoard__Job__c job;
        List<Contact> applicantContacts = new List<Contact>();

        System.runAs(adminUser) {
            Account communityAcc = new Account(Name = 'Community Account');
            Account partnerAcc = new Account(Name = 'Partner Account', wbl_IsRapCompany__c = true);
            insert new List<Account>{ communityAcc, partnerAcc };

            for (Integer i = 0; i < SETUP_APPLICATION_COUNT; i++) {
                applicantContacts.add(
                    new Contact(
                        FirstName = 'Name ' + i,
                        LastName = 'LastName ' + i,
                        Email = 'applicant.email' + i + '@actest.salesforce.com',
                        AccountId = communityAcc.Id
                    )
                );
            }

            Contact jobProviderContact = new Contact(
                FirstName = 'Job',
                LastName = 'Provider',
                Email = '<EMAIL>',
                AccountId = partnerAcc.Id
            );
            List<Contact> allContacts = new List<Contact>(applicantContacts);
            allContacts.add(jobProviderContact);
            insert allContacts;

            externalJobProvider = wbl_TestDataFactory.createExternalUser(jobProviderContact);
            insert externalJobProvider;

            wbl_TestDataFactory.assignJobProviderPermissionSet(externalJobProvider.Id);

            insert wbl_TestDataFactory.createCustomSettings(adminUser.Id);
        }

        System.runAs(externalJobProvider) {
            job = wbl_TestDataFactory.createJobs(1).get(0);
            insert job;
        }

        System.runAs(adminUser) {
            List<JobBoard__Job_Application__c> jobApps = wbl_TestDataFactory.createJobApplications(
                job,
                applicantContacts
            );
            List<wbl_OnboardingForm__c> onboardForms = wbl_TestDataFactory.createWblOnboardingForms(jobApps);
            insert onboardForms;

            for (Integer i = 0; i < jobApps.size(); i++) {
                jobApps.get(i).wbl_Status__c = wbl_Constant.HIRED_JOB_APP_STATUS;
                jobApps.get(i).wbl_OccupationHoursLimit__c = 1000;
                jobApps.get(i).wbl_OnboardingForm__c = onboardForms.get(i).Id;
            }
            insert jobApps;
        }
    }

    @IsTest
    static void sendContractsForSigning() {
        List<JobBoard__Job_Application__c> jobApps = [SELECT Id FROM JobBoard__Job_Application__c];
        Assert.isFalse(jobApps.isEmpty(), 'Setup method failed to create any job applications');

        dfsle.TestUtils.setMock(new dfsle.ESignatureAPIMock());
        User docuSignUser = dfsle.UserMock.createDocuSignSender();
        wbl_TestDataFactory.assignWblPermissionSet(docuSignUser.Id);
        wbl_TestDataFactory.assignJobProviderPermissionSet(docuSignUser.Id);
        wbl_TestDataFactory.assignPermissionSet(docuSignUser.Id, 'DocuSign_User');

        Boolean isNoException = true;
        Set<Id> jobAppIds = new Map<Id, JobBoard__Job_Application__c>(jobApps).keySet();

        Test.startTest();
        System.runAs(docuSignUser) {
            try {
                wbl_DocuSignJobContractSender.sendContractsForSigning(new List<Id>(jobAppIds));
            } catch (Exception e) {
                isNoException = false;
            }
        }
        Test.stopTest();

        Assert.isTrue(isNoException, 'Execution of wbl_DocuSignJobContractSender failed with exception');
    }
}