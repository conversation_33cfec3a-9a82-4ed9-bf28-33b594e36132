<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>EnableRandomQuestionsAction</name>
        <label>Enable Random Questions Action</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <actionName>as_EnableQuestionsInvocable</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>CompleteMessageScreen</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>assessmentIds</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputParameters>
        <nameSegment>as_AssessmentCtrl</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <environments>Default</environments>
    <interviewLabel>Enable Random Questions {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[AS] Enable Random Questions</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <screens>
        <name>CompleteMessageScreen</name>
        <label>Complete Message Screen</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>CompleteMessage</name>
            <fieldText>&lt;p&gt;{!$Label.as_EnableRandomAssessmentQuestionsCompleteMessage}&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>EnableRandomQuestionsAction</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
