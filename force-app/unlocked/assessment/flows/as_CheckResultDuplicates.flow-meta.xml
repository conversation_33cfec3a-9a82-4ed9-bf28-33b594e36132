<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>61.0</apiVersion>
    <customErrors>
        <name>DuplicateError</name>
        <label>Duplicate Error</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <customErrorMessages>
            <errorMessage>{!$Label.as_ResultDuplicateErrorMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>CheckDuplicate</name>
        <label>Check Duplicate</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IsDuplicateExists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetResult</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>DuplicateError</targetReference>
            </connector>
            <label>Is Duplicate Exists</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>[AS] Check Result Duplicates {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[AS] Check Result Duplicates</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetResult</name>
        <label>Get Result</label>
        <locationX>182</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CheckDuplicate</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Assessment__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Assessment__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Participant__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Participant__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>as_Result__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetResult</targetReference>
        </connector>
        <object>as_Result__c</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
