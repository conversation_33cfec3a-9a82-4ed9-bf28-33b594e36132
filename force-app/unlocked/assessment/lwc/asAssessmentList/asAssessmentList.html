<template>
    <div class="slds-is-relative slds-scrollable">
        <lightning-spinner
            lwc:if={isLoading}
            size="medium"
        ></lightning-spinner>
        <template lwc:else>
            <template lwc:if={hasAssessments}>
                <table class="slds-table slds-table_cell-buffer slds-table_bordered">
                    <thead>
                    <tr class="slds-line-height_reset">
                        <th class="" scope="col">
                            <div class="slds-truncate" title={labels.nameColumnLabel}>{labels.nameColumnLabel}</div>
                        </th>
                        <th class="" scope="col" if:true={useDistricts}>
                            <div class="slds-truncate" title={labels.openDatesColumnLabel}>{labels.openDatesColumnLabel}</div>
                        </th>
                        <th class="" scope="col" if:true={showScores}>
                            <div class="slds-truncate" title={labels.resultColumnLabel}>{labels.resultColumnLabel}</div>
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr
                        for:each={assessments}
                        for:item="assessment"
                        key={assessment.assessmentResultId}
                        class="slds-hint-parent"
                    >
                        <td data-label="Name">
                            <a
                                lwc:if={assessment.isActual}
                                href="#"
                                onclick={handleLinkClick}
                                class="slds-truncate"
                                title="Name"
                                data-id={assessment.assessmentResultId}
                            >
                                {assessment.name}
                            </a>
                            <div lwc:else class="slds-truncate" title={assessment.name}>
                                {assessment.name}
                            </div>
                        </td>
                        <td data-label={labels.openDatesColumnLabel} if:true={useDistricts}>
                            <div class="slds-truncate" title={assessment.openDates}>{assessment.openDates}</div>
                        </td>
                        <td data-label={labels.resultColumnLabel} if:true={showScores}>
                            <div class="slds-truncate" title={assessment.gainedScore}>{assessment.gainedScore}</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </template>
            <template lwc:else> {labels.noAssessmentsMsg} </template>
        </template>
    </div>

</template>
