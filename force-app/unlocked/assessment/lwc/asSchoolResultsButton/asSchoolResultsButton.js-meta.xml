<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <description>[AS] School Results Button</description>
    <isExposed>true</isExposed>
    <masterLabel>[AS] School Results Button</masterLabel>
    <targets>
        <target>lightningCommunity__Default</target>
        <target>lightningCommunity__Page</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightningCommunity__Default">
            <property
                    name="conferenceId"
                    type="String"
                    label="Conference Id"
                    default="{!conferenceId}"
                    description="The id of the conference to display results for."
                    required="true"
            />
            <property
                    required="true"
                    name="label"
                    label="Button text"
                    description="Text inside the button"
                    type="String"
                    default="Open Page"
            />
            <property
                    name="stretch"
                    label="Full width button"
                    description="Take up the entire available width"
                    type="Boolean"
                    default="false"
            />
            <property
                    required="true"
                    name="variant"
                    label="Style of the button"
                    type="String"
                    default="brand"
                    datasource="base, neutral, brand, brand-outline, destructive, destructive-text, inverse, success"
            />
            <property
                    required="true"
                    name="pageApiName"
                    label="API name of the page to open"
                    type="String"
                    default=""
            />
            <property
                    name="queryParamsJson"
                    label="Query Parameters"
                    description="Query parameters in the JSON format. For e.g: {&quot;id&quot;:&quot;{!recordId}&quot;,&quot;filter&quot;:&quot;name&quot;}"
                    type="String"
            />
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>
