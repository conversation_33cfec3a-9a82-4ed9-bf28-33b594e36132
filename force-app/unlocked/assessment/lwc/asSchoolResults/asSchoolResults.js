import { LightningElement, api } from 'lwc';
import { showErrors } from 'c/errorHandler';
import getResults from '@salesforce/apex/as_SchoolResultCtrl.getResults';

export default class extends LightningElement {
    @api recordId;
    isLoading = true;
    data = [];
    columns = [
        { label: 'Assessment', fieldName: 'Assessment__r.Name', sortable: true },
        { label: 'Participant', fieldName: 'Participant', sortable: true },
        { label: 'Start Date', fieldName: 'StartTime__c', sortable: true, type: "date",
            typeAttributes:{
                year: "numeric",
                month: "short",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit"
            }
        },
        { label: 'End Date', fieldName: 'EndTime__c', sortable: true, type: "date",
            typeAttributes:{
                year: "numeric",
                month: "short",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit"
            }
        },
        { label: 'Is Assessment Completed', fieldName: 'IsAssessmentCompleted', sortable: true },
    ];
    defaultSortDirection = 'asc';
    sortDirection = 'asc';
    sortedBy;

    connectedCallback() {
        getResults({recordId: this.recordId})
            .then(data => {
                if (data.isAssessmentScoreEnabled === true) {
                    this.columns.push({
                        label: 'Total Score',
                        fieldName: 'TotalScore__c',
                        sortable: true,
                        cellAttributes: {
                            alignment: 'right'
                        }
                    });
                }

                this.data = data.results.map((item) => {
                    let row = {
                        ...item,
                        'Assessment__r.Name': item.Assessment__r.Name,
                        'Participant': item.Participant__r.Contact.Name,
                        'StartTime__c': item.StartTime__c,
                        'EndTime__c': item.EndTime__c,
                        'IsAssessmentCompleted': item.EndTime__c ? 'Yes' : 'No',
                        'TotalScore__c': 'N/A'
                    };

                    if (data.isAssessmentScoreEnabled === true) {
                        row.TotalScore__c = item.EndTime__c ? item.TotalScore__c : '-'
                    }

                    return row;
                });
                this.isLoading = false;
            })
            .catch(error => {
                console.error(error);
                showErrors(this, error);
                this.isLoading = false;
            })
    }

    sortBy(field, reverse, primer) {
        const key = primer ? x => primer(x[field]) : x => x[field];
        return function (a, b) {
            a = key(a);
            b = key(b);
            return reverse * ((a > b) - (b > a));
        };
    }

    onHandleSort(event) {
        const { fieldName: sortedBy, sortDirection } = event.detail;
        const cloneData = [...this.data];

        cloneData.sort(this.sortBy(sortedBy, sortDirection === 'asc' ? 1 : -1));
        this.data = cloneData;
        this.sortDirection = sortDirection;
        this.sortedBy = sortedBy;
    }

}