public with sharing class as_AssessmentProcessCtrl {
    @AuraEnabled(Cacheable=false)
    public static as_UserAssessmentDTO getUserAssessment(Id recordId) {
        try {
            return as_AssessmentService.getUserAssessment(recordId);
        } catch (Exception e) {
            throw Error.toLWC(e.getMessage());
        }
    }

    @AuraEnabled(Cacheable=false)
    public static void saveUserAnswer(Id resultId, Id answerId, Id questionId) {
        try {
            as_AssessmentService.saveUserAnswer(resultId, answerId, questionId);
        } catch (Exception e) {
            throw Error.toLWC(e.getMessage());
        }
    }
    @AuraEnabled(Cacheable=false)
    public static void finishAssessment(Id resultId) {
        try {
            update as user new as_Result__c(Id = resultId, EndTime__c = System.now());
        } catch (Exception e) {
            throw Error.toLWC(e.getMessage());
        }
    }
}