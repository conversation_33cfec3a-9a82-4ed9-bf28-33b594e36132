<?xml version="1.0" encoding="UTF-8" ?>
<CustomLabels xmlns="http://soap.sforce.com/2006/04/metadata">
    <labels>
        <fullName>as_AssessmentAgreeButton</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentAgreeButton</shortDescription>
        <value>Agree</value>
    </labels>
    <labels>
        <fullName>as_AssessmentAgreeMessage</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentAgreeMessage</shortDescription>
        <value
        >Do you agree to begin the assessment? Please note you cannot re-take or return to the exam after you click next. You will have one hour to complete the exam within one attempt (one time). Good luck!</value>
    </labels>
    <labels>
        <fullName>as_AssessmentEnterPassCodeMessage</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentEnterPassCodeMessage</shortDescription>
        <value>Please enter pass code provided by your advisor to start exam:</value>
    </labels>
    <labels>
        <fullName>as_AssessmentEnterPassCodeLabel</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentEnterPassCodeLabel</shortDescription>
        <value>Pass Code</value>
    </labels>
    <labels>
        <fullName>as_AssessmentStartButton</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentStartButton</shortDescription>
        <value>Start</value>
    </labels>
    <labels>
        <fullName>as_AssessmentCompleteExamMessage</fullName>
        <categories>asAssessmentProcess component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentCompleteExamMessage</shortDescription>
        <value>You have completed all questions and are ready to submit the exam. Please confirm</value>
    </labels>
    <labels>
        <fullName>as_AssessmentNextButton</fullName>
        <categories>asAssessmentProcess component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentNextButton</shortDescription>
        <value>Next</value>
    </labels>
    <labels>
        <fullName>as_AssessmentPreviousButton</fullName>
        <categories>asAssessmentProcess component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentPreviousButton</shortDescription>
        <value>Previous</value>
    </labels>
    <labels>
        <fullName>as_AssessmentSubmitButton</fullName>
        <categories>asAssessmentProcess component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentSubmitButton</shortDescription>
        <value>Submit</value>
    </labels>
    <labels>
        <fullName>as_AssessmentSubmitText</fullName>
        <categories>asAssessmentProcess component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentSubmitText</shortDescription>
        <value>To submit the exam, please complete all questions.</value>
    </labels>
    <labels>
        <fullName>as_AssessmentTimeIsUpText</fullName>
        <categories>asAssessmentProcess component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentTimeIsUpText</shortDescription>
        <value>Exam time is up</value>
    </labels>
    <labels>
        <fullName>as_AssessmentTimeRemainingLabel</fullName>
        <categories>asAssessmentProcess component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentTimeRemainingLabel</shortDescription>
        <value>Time remaining:</value>
    </labels>
    <labels>
        <fullName>as_AssessmentCompletedExamMessage</fullName>
        <categories>asAssessmentResult component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentCompletedExamMessage</shortDescription>
        <value>You have completed this assessment.</value>
    </labels>
    <labels>
        <fullName>as_AssessmentExamStatusTitle</fullName>
        <categories>asAssessmentResult component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentExamStatusTitle</shortDescription>
        <value>Exam Status</value>
    </labels>
    <labels>
        <fullName>as_IncorrectPassCodeMessage</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asIncorrectPassCodeMessage</shortDescription>
        <value>Incorrect passcode. Please check and try again.</value>
    </labels>
    <labels>
        <fullName>as_AssessmentResultResetConfirmation</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentResultResetConfirmation</shortDescription>
        <value
        >After the reset, all resulting answers will be deleted and all fields completed during the assessment will be cleared. Are you sure you want to continue?</value>
    </labels>
    <labels>
        <fullName>as_EnableRandomAssessmentQuestionsCompleteMessage</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asEnableRandomAssessmentQuestionsCompleteMessage</shortDescription>
        <value>Completed successfully</value>
    </labels>
    <labels>
        <fullName>as_NoAssessmentsMessage</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asNoAssessmentsMessage</shortDescription>
        <value>There are currently no assessments for you</value>
    </labels>
    <labels>
        <fullName>as_AssessmentNotAvailableMessage</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentNotAvailableMessage</shortDescription>
        <value>Assessment is not available</value>
    </labels>
    <labels>
        <fullName>as_AssessmentListNameColumnLabel</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentListNameColumnLabel</shortDescription>
        <value>Name</value>
    </labels>
    <labels>
        <fullName>as_AssessmentListOpenDatesColumnLabel</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentListOpenDatesColumnLabel</shortDescription>
        <value>Open Dates</value>
    </labels>
    <labels>
        <fullName>as_AssessmentListResultColumnLabel</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentListResultColumnLabel</shortDescription>
        <value>Result</value>
    </labels>
    <labels>
        <fullName>as_AssessmentAvailableOnDateMessage</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentAvailableOnDateMessage</shortDescription>
        <value>Will be available on {0}</value>
    </labels>

    <labels>
        <fullName>as_AssessmentDistrictOpenDatesDuplicateErrorMessage</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asAssessmentDistrictOpenDatesDuplicateErrorMessage</shortDescription>
        <value>Relation between Assessment and the District already exists</value>
    </labels>
    <labels>
        <fullName>as_ResultDuplicateErrorMessage</fullName>
        <categories>asStartAssessment component</categories>
        <language>en_US</language>
        <protected>true</protected>
        <shortDescription>asResultDuplicateErrorMessage</shortDescription>
        <value>Selected Assessment already assigned to the selected participant</value>
    </labels>
</CustomLabels>
