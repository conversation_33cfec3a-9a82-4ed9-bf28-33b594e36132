@IsTest
public class bm_TestUtils {
    private static final String ADMIN_EMAIL = '<EMAIL>';
    private static final String ADMIN_PERMISSION_SET = 'bm_AdminPermissions';

    public static void insertSettings() {
        insert new CoreSetting__c(
            PortalUserDefaultProfileName__c = 'Portal User',
            AdvisorPermissionSets__c = 'sm_AdvisorPermissions',
            LeadAdvisorPermissionSets__c = 'sm_LeadAdvisorPermissions',
            PartnerLeadPermissionSets__c = 'bm_PartnerLeadPermissions'
        );

        insert new bm_Setting__c(
                UpdateCompanyInfoNotificationDate__c = getTodayDateDDMM()
        );
    }

    public static String getTodayDateDDMM() {
        Date today = Date.today();
        // Get day and month with leading zeros if necessary
        String day = today.day() < 10 ? '0' + String.valueOf(today.day()) : String.valueOf(today.day());
        String month = today.month() < 10 ? '0' + String.valueOf(today.month()) : String.valueOf(today.month());
        return day + '/' + month;
    }


    public static User createAdminUser() {
        Profile p = [SELECT Id FROM Profile WHERE Name = 'System Administrator'];

        UserRole role = new UserRole(DeveloperName = 'CEO_TEST', Name = 'CEO_TEST');
        insert role;

        User admin = new User(
            ProfileId = p.Id,
            FirstName = 'John',
            LastName = 'Doe',
            Email = ADMIN_EMAIL,
            Username = ADMIN_EMAIL,
            Alias = 'jdoe123',
            TimeZoneSidKey = 'America/Chicago',
            EmailEncodingKey = 'UTF-8',
            LocaleSidKey = 'en_US',
            LanguageLocaleKey = 'en_US',
            UserRoleId = role.Id
        );
        insert admin;

        PermissionSet PS = [SELECT Id FROM PermissionSet WHERE Name = :ADMIN_PERMISSION_SET];
        insert new PermissionSetAssignment(AssigneeId = admin.Id, PermissionSetId = PS.Id);

        return admin;
    }

    public static User getAdmin() {
        return [SELECT Id FROM User WHERE Email = :ADMIN_EMAIL];
    }

    public static User getUserByRole(String role) {
        return [SELECT Id FROM User WHERE Contact.PortalRole__c = :role LIMIT 1];
    }

    public static Account createAccount(String name) {
        Account theAccount = new Account(Name = name);
        insert theAccount;
        return theAccount;
    }

    public static Contact createContact(Id accountId, String role) {
        Contact theContact = new Contact(
            FirstName = 'Jane',
            LastName = 'Smith',
            AccountId = accountId,
            Email = UUID.randomUUID() + '@example.com',
            PortalRole__c = role
        );
        insert theContact;
        return theContact;
    }

    public static Id getPartnerLeadRecordTypeId() {
        return Schema.SObjectType.Lead.getRecordTypeInfosByName().get('Business').getRecordTypeId();
    }

    //    public static User createPartner(Id contactId) {
    //        User partner = makeUser('John', 'Doe', INTERNAL_USER_EMAIL, COMMUNITY_USER_PROFILE_NAME, contactId);
    //        insert partner;
    //
    //        Id permissionSetId = [SELECT Id FROM PermissionSet WHERE Name = :PARTNER_PERMISSION_SET].Id;
    //        insert new PermissionSetAssignment(PermissionSetId = permissionSetId, AssigneeId = partner.Id);
    //
    //        return partner;
    //    }
    //
    //    public static User getPartner() {
    //        return [SELECT Id FROM User WHERE Username = :INTERNAL_USER_EMAIL LIMIT 1];
    //    }
    //
    //
    //    private static User makeUser(String firstName, String lastName, String username, String profileName, Id contactId) {
    //        Profile profile = [SELECT Id FROM Profile WHERE Name = :profileName];
    //        return new User(
    //                Alias = ('a' + Math.random() * 100).substring(1, 6),
    //                Email = username,
    //                Username = username,
    //                EmailEncodingKey = 'UTF-8',
    //                FirstName = firstName,
    //                LastName = lastName,
    //                LanguageLocaleKey = 'en_US',
    //                LocaleSidKey = 'en_US',
    //                ProfileId = profile.Id,
    //                TimeZoneSidKey = 'America/Los_Angeles',
    //                ContactId = contactId
    //        );
    //    }
    //
    //    public static Contact createContact(String accountName, String firstName, String lastName) {
    //        Account theAccount = new Account(Name = accountName);
    //        insert theAccount;
    //
    //        Contact theContact = new Contact(
    //                FirstName = firstName,
    //                LastName = lastName,
    //                AccountId = theAccount.Id
    //        );
    //        insert  theContact;
    //        return theContact;
    //    }
}