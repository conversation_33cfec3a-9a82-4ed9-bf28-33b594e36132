public with sharing class bm_LeadRecordTypesDynamicPickList extends VisualEditor.DynamicPickList {
    public override VisualEditor.DataRow getDefaultValue() {
        return new VisualEditor.DataRow('--None--', '');
    }

    public virtual override VisualEditor.DynamicPickListRows getValues() {
        VisualEditor.DynamicPickListRows values = new VisualEditor.DynamicPickListRows();
        Schema.DescribeSObjectResult leadDescribe = Lead.SObjectType.getDescribe();
        List<Schema.RecordTypeInfo> recordTypes = leadDescribe.getRecordTypeInfos();

        for (Schema.RecordTypeInfo recordType : recordTypes) {
            values.addRow(new VisualEditor.DataRow(recordType.getName(), String.valueOf(recordType.getRecordTypeId())));
        }

        return values;
    }
}