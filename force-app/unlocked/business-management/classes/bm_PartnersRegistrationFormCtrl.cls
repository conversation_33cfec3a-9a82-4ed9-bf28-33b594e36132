public with sharing class bm_PartnersRegistrationFormCtrl {
    private static final String FORM_LAYOUT_NAME = 'Lead-[BM] Lead Self Registration';

    @AuraEnabled
    public static aclab.FormLayout getForm() {
        try {
            return aclab.Form.retrieve(FORM_LAYOUT_NAME);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void saveRecord(Lead record, String recordTypeId, String captchaToken, Boolean isJudge) {
        try {
            if (!aclab.GoogleReCaptchaService.isClientTokenValid(captchaToken)) {
                throw Error.toLWC(System.Label.bm_PartnersFormInvalidCaptchaMessage);
            }
            Lead theLead = (Lead) aclab.Form.cleanInjectedFields(record, FORM_LAYOUT_NAME);
            theLead.IsFormCreated__c = true;
            theLead.FirstName = record.FirstName;
            theLead.LastName = record.LastName;
            theLead.PortalRole__c = 'Partner Lead' + (isJudge ? ';Judge' : '');

            Database.DMLOptions dmlOptions = new Database.DMLOptions();
            dmlOptions.assignmentRuleHeader.useDefaultRule = true;
            dmlOptions.emailHeader.triggerOtherEmail = true;
            dmlOptions.emailHeader.triggerUserEmail = true;
            dmlOptions.emailHeader.triggerAutoResponseEmail = true;
            theLead.setOptions(dmlOptions);

            WS.insertRecord(theLead);

            theLead.RecordTypeId = recordTypeId;
            WS.updateRecord(theLead); // Update after insert to avoid null RecordTypeId field while use useDefaultRule dml options
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

}