import { LightningElement, api, wire } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { showErrors } from 'c/errorHandler';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { CurrentPageReference } from 'lightning/navigation';
import getForm from '@salesforce/apex/bm_PartnersRegistrationFormCtrl.getForm';
import saveRecord from '@salesforce/apex/bm_PartnersRegistrationFormCtrl.saveRecord';
import labels from './labels';

export default class extends NavigationMixin(LightningElement) {
    @api recordTypeId;
    @api postSavePageName;
    labels = labels;
    isLoading = true;
    isJudge;
    style = 'slds-hide';
    overrides = [
        { name: 'Company', label: this.labels.bmPartnersFormBusinessNameLabel },
        { name: 'Phone', label: this.labels.bmPartnersFormMainPhoneLabel },
        { name: 'Email', label: this.labels.bmPartnersFormContactEmailLabel },
        { name: 'Address', required: true, country: 'US', countryDisabled: true }
    ];

    @wire(CurrentPageReference)
    getStateParameters(currentPageReference) {
        if (currentPageReference) {
            this.isJudge = currentPageReference.state?.r;
        }
    }

    connectedCallback() {
        this.getFormMethod = () => {
            return new Promise((resolve, reject) => {
                getForm()
                    .then(form => {
                        this.injectNameFields(form);
                        resolve(form);
                    })
                    .catch(error => reject(error));
            });
        };
    }

    injectNameFields(form) {
        ['Last Name', 'First Name'].forEach(field => {
            form.sections[0]?.columns[0]?.fields.unshift({
                label: field,
                name: field.replace(' ', ''),
                type: 'string',
                isUiRequired: true
            });
        });
    }

    handleSubmit(event) {
        const record = event.detail.record;
        const captchaToken = event.detail.captchaToken;
        const recordTypeId = this.recordTypeId;
        const isJudge = this.isJudge === 'j';
        this.isLoading = true;

        saveRecord({ record, recordTypeId, captchaToken, isJudge })
            .then(() => {
                const ev = new ShowToastEvent({
                    title: 'Success!',
                    variant: 'success'
                });
                this.dispatchEvent(ev);
                this.redirectToPostSavePage();
            })
            .catch((error) => {
                showErrors(this, error);
            })
            .finally(() => (this.isLoading = false));
    }

    formReady() {
        this.style = '';
        this.isLoading = false;
    }

    redirectToPostSavePage() {
        if (!this.postSavePageName) {
            return;
        }
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: this.postSavePageName
            }
        });
    }

}
