<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Partner_Notification</name>
        <label>Partner Notification</label>
        <locationX>138</locationX>
        <locationY>792</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Partners</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>Partners.Email</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <elementReference>GetOrgWideEmail.Address</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>Partner Notification</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>PartnerNotificationEmailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>59.0</apiVersion>
    <decisions>
        <name>First_September</name>
        <label>First September</label>
        <locationX>380</locationX>
        <locationY>252</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>IsFirstSeptember</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>OrgWideExist</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>OrgWideExist</name>
        <label>OrgWideExist</label>
        <locationX>182</locationX>
        <locationY>360</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Exist</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Setup.CoreSetting__c.OrgWideEmailId__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>GetOrgWideEmail</targetReference>
            </connector>
            <label>Exist</label>
        </rules>
    </decisions>
    <description>To notify partners on 1 September</description>
    <environments>Default</environments>
    <formulas>
        <name>IsFirstSeptember</name>
        <dataType>Boolean</dataType>
        <expression>DAY(TODAY()) = 21 &amp;&amp; MONTH(TODAY()) = 12</expression>
    </formulas>
    <interviewLabel>[BM] Partners notification {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[BM] Partners notification</label>
    <loops>
        <name>Partners</name>
        <label>Partners</label>
        <locationX>50</locationX>
        <locationY>684</locationY>
        <collectionReference>GetPartnerLeads</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Partner_Notification</targetReference>
        </nextValueConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetOrgWideEmail</name>
        <label>GetOrgWideEmail</label>
        <locationX>50</locationX>
        <locationY>468</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetPartnerLeads</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Setup.CoreSetting__c.OrgWideEmailId__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OrgWideEmailAddress</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Retrieve list of all Partner Leads in the system.</description>
        <name>GetPartnerLeads</name>
        <label>Get Partner Leads</label>
        <locationX>50</locationX>
        <locationY>576</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Partners</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>PortalRole__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Partner Lead</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Contact</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Email</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>254</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>First_September</targetReference>
        </connector>
        <schedule>
            <frequency>Daily</frequency>
            <startDate>2023-12-21</startDate>
            <startTime>11:00:00.000Z</startTime>
        </schedule>
        <triggerType>Scheduled</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>PartnerNotificationEmailBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>&lt;p&gt;Kindly take a moment to assess your company&apos;s details information and make any necessary updates as required.&lt;/p&gt;&lt;p&gt;Please visit the &lt;a href=&quot;{!$Setup.bm_Setting__c.PortalPageLink__c}&quot;  rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot;&gt;Portal&lt;/a&gt;&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>PartnerIds</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
