<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>MembershipExpirationDate__c</fullName>
    <deprecated>false</deprecated>
    <description>Date in DD/MM format. For example 31/08, means August 31</description>
    <externalId>false</externalId>
    <inlineHelpText>Date in DD/MM format. For example 31/08, means August 31</inlineHelpText>
    <label>Membership Expiration Date</label>
    <length>10</length>
    <required>true</required>
    <trackTrending>false</trackTrending>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
