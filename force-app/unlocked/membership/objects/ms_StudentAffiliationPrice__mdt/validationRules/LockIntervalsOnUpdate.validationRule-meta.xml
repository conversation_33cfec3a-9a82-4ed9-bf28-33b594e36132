<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>LockIntervalsOnUpdate</fullName>
    <active>true</active>
    <description>Do not allow to change ranges</description>
    <errorConditionFormula>AND(NOT(ISNEW()), OR(ISCHANGED(MinStudents__c), ISCHANGED(MaxStudents__c)))</errorConditionFormula>
    <errorMessage>Intervals are locked.</errorMessage>
</ValidationRule>
