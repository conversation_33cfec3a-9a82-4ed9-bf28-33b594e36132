public with sharing class ms_TPPRequestTriggerHandler {

    public void sendEmailToAdmins(List<ms_TPPRequest__c> requests) {
        Id orgWideEmailId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;
        String emailTemplateName = ms_Settings__c.getInstance().TPPRequestCreationEmailTemplate__c;

        if (String.isBlank(orgWideEmailId) || String.isBlank(emailTemplateName)) {
            return;
        }

        EmailTemplate template = [SELECT Id, Body, Subject FROM EmailTemplate WHERE IsActive = TRUE AND DeveloperName = :emailTemplateName];

        List<User> admins = new ms_Service.WS().getTPPAdmins();
        List<Messaging.SingleEmailMessage> emailMessages = new List<Messaging.SingleEmailMessage>();

        for (ms_TPPRequest__c request: requests) {
            for (User admin: admins) {
                emailMessages.add(makeEmailMessage(orgWideEmailId, template.Subject, template.Body, admin.Id, request.Id));
            }
        }

        new SendEmailWithoutSharing().sendEmails(emailMessages);
    }

    @TestVisible
    private Messaging.SingleEmailMessage makeEmailMessage(String orgWideEmailId, String subject, String templateBody, String adminId, String whatId) {
        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
        email.setOrgWideEmailAddressId(orgWideEmailId);
//        email.setTemplateId(templateId);
        email.setSubject(subject);
        email.setPlainTextBody(templateBody.replaceAll('\\{\\!ms_TPPRequest__c\\.Id\\}', whatId));
        email.setTargetObjectId(adminId);
        email.setSaveAsActivity(false);
        return email;
    }

    private without sharing class SendEmailWithoutSharing {
        public void sendEmails(List<Messaging.SingleEmailMessage> emailMessages) {
            Messaging.sendEmail(emailMessages);
        }
    }
}