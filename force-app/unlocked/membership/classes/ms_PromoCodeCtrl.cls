public with sharing class ms_PromoCodeCtrl {

    @AuraEnabled
    public static List<ms_PromoCode__c> getPromoCodes() {
        return [
                SELECT Name, EndDate__c
                FROM ms_PromoCode__c
                WHERE StartDate__c != NULL AND EndDate__c != NULL
                AND StartDate__c < :System.today() AND EndDate__c > :System.today()
                ORDER BY EndDate__c ASC
        ];
    }

}