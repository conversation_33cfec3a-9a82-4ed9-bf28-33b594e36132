@IsTest
private class ms_ContactTriggerHandlerTest {
    @TestSetup
    static void setup() {
        ms_TestUtils.createAdminUser();
        insertSettings();
    }

    @Future
    static void insertSettings() {
        ms_TestUtils.insertSettings();
    }

    @IsTest
    static void testBehavior() {

        System.runAs(ms_TestUtils.getAdmin()) {
            Account theAccount = new Account(Name = 'Acme');
            insert theAccount;

            theAccount.MembershipType__c = 'TPP';
            update theAccount;

            Contact theContact = ms_TestUtils.createContact(theAccount.Id, 'Lead Advisor');

            theContact.PortalRole__c = 'Student';
            update theContact;
        }

    }

    @IsTest
    static void testMakeEmail() {

        System.runAs(ms_TestUtils.getAdmin()) {
            Account theAccount = new Account(Name = 'Acme');
            insert theAccount;

            theAccount.MembershipType__c = 'TPP';
            update theAccount;

            Contact theContact = ms_TestUtils.createContact(theAccount.Id, 'Lead Advisor');

//            List<String> adminEmails = new ms_ContactTriggerHandler().getAdminEmails();
            new ms_ContactTriggerHandler().makeMail(theAccount, new List<String>(), '00x000000000000');
        }

    }

    @IsTest
    static void testCancelSubscription() {

        System.runAs(ms_TestUtils.getAdmin()) {
            Account theAccount = new Account(Name = 'Acme');
            insert theAccount;

            theAccount.MembershipType__c = 'TPP';
            update theAccount;

            Contact theContact = ms_TestUtils.createContact(theAccount.Id, 'Lead Advisor');


            try {
                delete theContact;
            } catch (Exception e) {

            }

        }

    }
}