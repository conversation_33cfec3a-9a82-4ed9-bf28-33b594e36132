global with sharing class ms_MembershipNotificationsScheduler implements Schedulable {

    public static Date today = System.today();

    global void execute(SchedulableContext SC) {
        // Send notification to admin on March 1
        if (isDayToSendNotificationToAdmins()) {
            sendEmailToAdmin();
        }

        // Send notification to lead advisor that they need to agree/disagree to pay next year
        if(isDayToSendNotificationToAskAgree()) {
            sendEmailToLABeforeAgreeDisagree();
        }

        // Send notification to lead advisors if they didn't answered id they agree/disagree to pay next year
        if (isDayToSendRepeatedReminder()) {
            sendRepeatedReminder();
        }

    }

    @TestVisible
    private Boolean isDayToSendNotificationToAdmins() {
        String dateToNotify = ms_Settings__c.getInstance().DateToNotifyAdminsUnconfirmedMember__c;

        // Split the string '01/03' into month and day parts
        List<String> dateParts = dateToNotify.split('/');
        Integer month = Integer.valueOf(dateParts[1]);
        Integer day = Integer.valueOf(dateParts[0]);

        // Create a date instance using the current year, month, and day
        Date targetDate = Date.newInstance(today.year(), month, day);

        return today == targetDate;
    }

    @TestVisible
    private void sendEmailToAdmin() {
        String orgWideEmail = CoreSetting__c.getInstance().OrgWideEmailId__c;

        if (orgWideEmail == null) {
            return;
        }

        List<User> admins = new ms_Service.WS().getTPPAdmins();
        List<String> adminEmailAddresses = new List<String>();

        for (User admin: admins) {
            adminEmailAddresses.add(admin.Email);
        }

        if (admins.isEmpty()) {
            return;
        }

        sendEmails(orgWideEmail, adminEmailAddresses);
    }

    @TestVisible
    private void sendEmails(String orgWideEmail, List<String> emails) {
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setToAddresses(emails);
        mail.setOrgWideEmailAddressId(orgWideEmail);
        mail.setSubject('Please review the list of school accounts');
        mail.setPlainTextBody('Please review the list of school accounts that have not confirmed/canceled their membership for the current academic year.');

        if (!Test.isRunningTest()) {
            Messaging.sendEmail(new List<Messaging.Email>{ mail });
        }
    }

    @TestVisible
    private Boolean isDayToSendNotificationToAskAgree() {
        Integer showBeforeNDays = (Integer) ms_Settings__c.getInstance().DisplayAgreementToPayBeforeNDays__c;
        if (showBeforeNDays == null) {
            return false;
        }

        Datetime nextExpirationDate = ms_Service.getNextSubscriptionExpirationDate();
        return nextExpirationDate.addDays(-1 * showBeforeNDays) == today;
    }

    @TestVisible
    private void sendEmailToLABeforeAgreeDisagree() {
        Id orgWideEmailId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;
        String emailTemplateName = ms_Settings__c.getInstance().NeedConfirmationMembershipEmailTmpl__c;

        if (String.isBlank(orgWideEmailId) || String.isBlank(emailTemplateName)) {
            return;
        }

        List<Contact> advisors = [
                SELECT Id, Email FROM Contact
                WHERE PortalRole__c INCLUDES ('Lead Advisor') AND ms_IsActiveUser__c = TRUE
        ];

        ms_Service.sendEmailToContacts(advisors, emailTemplateName);
    }

    @TestVisible
    private Boolean isDayToSendRepeatedReminder() {
        List<Integer> daysAfterExpirationDate = new List<Integer> { 14, 30, 90 };
        Datetime nextDate = ms_Service.getNextSubscriptionExpirationDate();

        System.debug('TODAY:      ' + today);
        System.debug('NEXT ED:    ' + nextDate);

        for (Integer days: daysAfterExpirationDate) {
            System.debug('FOR(' +days+ '):     ' + nextDate.addDays(days) + ' ' + (nextDate.addDays(days) == today ? 'YES' : 'NO'));
            if (nextDate.addDays(days) == today) {
                return true;
            }
        }

        return false;
    }

    @TestVisible
    private void sendRepeatedReminder() {
        Id orgWideEmailId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;
        String emailTemplateName = ms_Settings__c.getInstance().RepeatedPaymentReminderEmailTemplate__c;

        if (String.isBlank(orgWideEmailId) || String.isBlank(emailTemplateName)) {
            return;
        }

        List<Account> accounts = ms_AgreementToPayService.getNotAnsweredAccountsForCurrentYear();

        List<Contact> advisors = [
                SELECT Id, Email FROM Contact
                WHERE PortalRole__c INCLUDES ('Lead Advisor') AND ms_IsActiveUser__c = FALSE
                AND AccountId IN :accounts
        ];

        ms_Service.sendEmailToContacts(advisors, emailTemplateName);
    }

}