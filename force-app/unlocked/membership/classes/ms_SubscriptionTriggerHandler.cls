public with sharing class ms_SubscriptionTriggerHandler {

    public List<ms_Subscription__c> filterSubscriptionsByStatus(Map<Id, ms_Subscription__c> newMap, Map<Id, ms_Subscription__c> oldMap, String status) {
        return this.filterSubscriptionsByStatus(newMap, oldMap, new Set<String>{ status });
    }

    public List<ms_Subscription__c> filterSubscriptionsByStatus(Map<Id, ms_Subscription__c> newMap, Map<Id, ms_Subscription__c> oldMap, Set<String> statuses) {
        List<ms_Subscription__c> result = new List<ms_Subscription__c>();
        for (ms_Subscription__c subscription: newMap.values()) {
            Boolean hasOldValue = oldMap != null;
            Boolean containsStatus = statuses.contains(subscription.Status__c);
            if (containsStatus) {
                if (hasOldValue && newMap.get(subscription.Id).Status__c != oldMap.get(subscription.Id).Status__c) {
                    result.add(subscription);
                }
                else if (!hasOldValue) {
                    result.add(subscription);
                }
            }
        }
        return result;
    }

    public void createInvoiceLines(List<ms_Subscription__c> subscriptions) {
        List<InvoiceLine__c> lines = new List<InvoiceLine__c>();
        for (ms_Subscription__c subscription: subscriptions) {
            lines.add(new InvoiceLine__c(
                    Name = subscription.Name,
                    Account__c = subscription.School__c,
                    Quantity__c = 1,
                    UnitPrice__c = subscription.CalculatedPrice__c,
                    ms_Subscription__c = subscription.Id
            ));
        }

        insert lines;

        // update subscriptions
        List<ms_Subscription__c> subscriptionsToUpdate = new List<ms_Subscription__c>();
        for (InvoiceLine__c line: lines) {
            subscriptionsToUpdate.add(new ms_Subscription__c(
                    Id = line.ms_Subscription__c,
                    InvoiceLine__c = line.Id
            ));
        }
        update subscriptionsToUpdate;
    }

    public List<InvoiceLine__c> expireInvoiceLinesForSubscriptions(List<ms_Subscription__c> subscriptions) {
        WS.deleteRecords([
                SELECT Id
                FROM InvoiceLine__c
                WHERE ms_Subscription__c IN :subscriptions
                AND InvoiceStatus__c NOT IN ('Paid', 'Paid By Credit')
        ]);
        return null;
    }

}