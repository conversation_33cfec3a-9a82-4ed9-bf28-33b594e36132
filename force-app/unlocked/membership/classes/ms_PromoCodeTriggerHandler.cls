public with sharing class ms_PromoCodeTriggerHandler {

    public void sendPromoCodeToLeadAdvisors(List<ms_PromoCode__c> promoCodes) {
        Map<Id, Id> promoCodeIdToAccountId = new Map<Id, Id>();
        for (ms_PromoCode__c promoCode: promoCodes) {
            promoCodeIdToAccountId.put(promoCode.Id, promoCode.Account__c);
        }

        Map<Id, Account> accountsMap = new Map<Id, Account>([
                SELECT Id, (
                        SELECT Id
                        FROM Contacts
                        WHERE ms_IsActiveUser__c = TRUE
                        AND PortalRole__c INCLUDES ('Lead Advisor')
                )
                FROM Account
                WHERE Id IN :promoCodeIdToAccountId.values()
        ]);

        Id orgWideEmailId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;
        String emailTemplateName = ms_Settings__c.getInstance().PromoCodeAddedEmailTemplate__c;

        if (String.isBlank(orgWideEmailId) || String.isBlank(emailTemplateName)) {
            return;
        }

        EmailTemplate template = [SELECT Id FROM EmailTemplate WHERE IsActive = TRUE AND DeveloperName = :emailTemplateName];
        List<Messaging.SingleEmailMessage> emailMessages = new List<Messaging.SingleEmailMessage>();
        for (Id promoCodeId: promoCodeIdToAccountId.keySet()) {
            Id accountId = promoCodeIdToAccountId.get(promoCodeId);
            for (Contact advisor: accountsMap.get(accountId).Contacts) {
                emailMessages.add(makeEmailMessage(orgWideEmailId, template.Id, advisor.Id, promoCodeId));
            }
        }

        Messaging.sendEmail(emailMessages);
    }

    @TestVisible
    private Messaging.SingleEmailMessage makeEmailMessage(Id orgWideEmailId, Id templateId, Id advisorId, Id whatId) {
        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
        email.setOrgWideEmailAddressId(orgWideEmailId);
        email.setTemplateId(templateId);
        email.setTargetObjectId(advisorId);
        email.setWhatId(whatId);
        return email;
    }

}