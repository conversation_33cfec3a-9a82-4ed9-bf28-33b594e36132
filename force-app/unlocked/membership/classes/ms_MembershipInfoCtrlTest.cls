@IsTest
private class ms_MembershipInfoCtrlTest {
    @TestSetup
    static void setup() {
        ms_TestUtils.createAdminUser();
        insertSettings();
    }

    @Future
    static void insertSettings() {
        ms_TestUtils.insertSettings();
    }


    @IsTest
    static void testBehavior() {
        System.runAs(ms_TestUtils.getAdmin()) {
            Account theAccount = new Account(Name = 'Acme');
            insert theAccount;

            theAccount.MembershipType__c = 'TPP';
            update theAccount;

            Contact theContact = ms_TestUtils.createContact(theAccount.Id, 'Lead Advisor');

            ms_MembershipInfoCtrl.init();
            try {
            } catch (Exception e) {
                // Layout does not exist
            }

        }

        System.runAs(ms_TestUtils.getUserByRole('Lead Advisor')) {
            try {
                ms_MembershipInfoCtrl.saveRecord();
            } catch (Exception e) {
                // TODO
            }

        }

    }
}