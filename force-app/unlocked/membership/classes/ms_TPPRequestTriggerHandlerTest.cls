@IsTest
private class ms_TPPRequestTriggerHandlerTest {
    @TestSetup
    static void setup() {
        ms_TestUtils.createAdminUser();
        insertSettings();
    }

    @Future
    static void insertSettings() {
        ms_TestUtils.insertSettings();
    }


    @IsTest
    static void testBehavior() {
        Account theAccount = new Account(Name = 'Acme');

        System.runAs(ms_TestUtils.getAdmin()) {
            insert theAccount;

            theAccount.MembershipType__c = 'Direct';
            update theAccount;

            ms_TestUtils.createContact(theAccount.Id, 'Lead Advisor');


            EmailTemplate validEmailTemplate = new EmailTemplate();
            validEmailTemplate.isActive = true;
            validEmailTemplate.Name = 'TPPRequestCreationEmailTemplate';
            validEmailTemplate.DeveloperName = 'TPPRequestCreationEmailTemplate';
            validEmailTemplate.TemplateType = 'text';
            validEmailTemplate.FolderId = UserInfo.getUserId();

            insert validEmailTemplate;
        }

        User LA = ms_TestUtils.getUserByRole('Lead Advisor');

        System.runAs(LA) {

            try {
                insert new ms_TPPRequest__c(
                        Contact__c = LA.ContactId,
                        School__c = theAccount.Id,
                        EstimatedNumberOfStudents__c = 10,
                        EstimatedNumberOfAdvisors__c = 1
                );
            } catch (Exception e) {

            }

        }

    }

    @IsTest
    static void testEmail() {
        new ms_TPPRequestTriggerHandler().makeEmailMessage('0D2VC0000000000', '',  '', UserInfo.getUserId(), '');
    }
}