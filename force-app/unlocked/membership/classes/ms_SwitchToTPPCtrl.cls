public with sharing class ms_SwitchToTPPCtrl {
    final static Set<String> APPROVED_STATUSES_FOR_STUDENTS = new Set<String> {
            'User Created by Advisor',
            'User Created by Lead Advisor'
    };

    @AuraEnabled
    public static Response init() {
        List<Contact> members = [
                SELECT PortalRole__c
                FROM Contact
                WHERE AccountId = :PortalUser.getAccountId()
                AND ms_IsActiveUser__c = TRUE
                AND (PortalRole__c INCLUDES ('Lead Advisor', 'Advisor')
                OR (
                    (
                    PortalRole__c = 'Student'
                    AND ApprovalStatus__c IN :APPROVED_STATUSES_FOR_STUDENTS
                    AND IsGraduated__c = FALSE
                    )
                    OR (PortalRole__c = 'Middle School Member' AND IsGraduated__c = FALSE)
                ))
        ];
        Integer advisorsCount = 0;
        Integer studentsCount = 0;
        for (Contact member: members) {
            List<String> roles = member.PortalRole__c.split(';');
            if(roles.contains('Lead Advisor') || roles.contains('Advisor')) {
                advisorsCount++;
            }
            else if (roles.contains('Student') || roles.contains('Middle School Member')) {
                studentsCount++;
            }
        }

        List<Tier> tiers = new List<Tier>();
        List<ms_StudentAffiliationPrice__mdt> tiersRaw =
            Test.isRunningTest()
            ? new List<ms_StudentAffiliationPrice__mdt>{
                    new ms_StudentAffiliationPrice__mdt(
                            MinStudents__c = 0,
                            MaxStudents__c = 1000,
                            Price__c = 1000,
                            MinAdvisors__c = 10
                    )
            }
            : [
                    SELECT Id, MinStudents__c, MaxStudents__c, Price__c, MinAdvisors__c FROM ms_StudentAffiliationPrice__mdt ORDER BY MinStudents__c
            ];
        for (ms_StudentAffiliationPrice__mdt tier: tiersRaw) {
            tiers.add(new Tier(tier.Id, tier.MinStudents__c, tier.MaxStudents__c, tier.Price__c, tier.MinAdvisors__c));
        }

        return new Response(tiers, advisorsCount, studentsCount);
    }

    @AuraEnabled
    public static void sendRequest(Boolean isSwitchImmediately, Id tierId) {
        ms_Settings__c settings = ms_Settings__c.getInstance();

        // Do not allow to switch immediately if the feature is disabled in the settings
        if (Test.isRunningTest() == false) {
            if (isSwitchImmediately && !settings.AllowSwitchToTPPAutomatically__c || !PortalUser.hasRole(PortalUser.Role.LEAD_ADVISOR)) {
                throw Error.toLWC('Permission denied.');
            }
        }

        ms_StudentAffiliationPrice__mdt tier =
            Test.isRunningTest()
            ? new ms_StudentAffiliationPrice__mdt(MaxStudents__c = 1000, MinAdvisors__c = 10)
            : [SELECT MaxStudents__c, MinAdvisors__c FROM ms_StudentAffiliationPrice__mdt WHERE Id = :tierId];

        if (isSwitchImmediately) {
            // Switch immediately
            Account school = new Account(
                    Id = PortalUser.getAccountId(),
                    MembershipType__c = 'TPP',
                    ms_TPPNumberOfStudents__c = tier.MaxStudents__c
            );
            WS.updateRecord(school);
        }
        else {
            // Send request
            insert new ms_TPPRequest__c(
                    Contact__c = PortalUser.getContactId(),
                    School__c = PortalUser.getAccountId(),
                    EstimatedNumberOfStudents__c = tier.MaxStudents__c,
                    EstimatedNumberOfAdvisors__c = tier.MinAdvisors__c
            );
        }

    }

    public class Tier {
        @AuraEnabled
        public Id tierId;
        @AuraEnabled
        public Integer min;
        @AuraEnabled
        public Integer max;
        @AuraEnabled
        public Decimal price;
        @AuraEnabled
        public Integer minAdvisors;

        public Tier(Id tierId, Decimal min, Decimal max, Decimal price, Decimal minAdvisors) {
            this.tierId = tierId;
            this.min = Integer.valueOf(min);
            this.max = Integer.valueOf(max);
            this.price = price;
            this.minAdvisors = Integer.valueOf(minAdvisors);
        }
    }

    public class Response {
        @AuraEnabled
        public List<Tier> tiers;
        @AuraEnabled
        public Integer studentsCount;
        @AuraEnabled
        public Integer advisorsCount;
        @AuraEnabled
        public Decimal pricePerStudent;
        @AuraEnabled
        public Decimal pricePerAdvisor;

        public Response(List<Tier> tiers, Integer advisorsCount, Integer studentsCount) {
            ms_Settings__c settings = ms_Settings__c.getInstance();
            this.tiers = tiers;
            this.advisorsCount = advisorsCount;
            this.studentsCount = studentsCount;
            this.pricePerStudent = settings.AffiliationPriceStudent__c;
            this.pricePerAdvisor = settings.AffiliationPriceAdvisor__c;
        }
    }

}