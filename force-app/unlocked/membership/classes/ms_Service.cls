public with sharing class ms_Service {

    public without sharing class WS {
        public List<User> getTPPAdmins() {
            List<User> users = new List<User>();
            List<Group> publicGroups = [SELECT Id FROM Group WHERE DeveloperName = 'ms_TPPRequestAdmins'];
            if (!publicGroups.isEmpty()) {
                GroupMember[] members = [SELECT UserOrGroupId FROM GroupMember WHERE GroupId = :publicGroups[0].Id];
                Set<Id> userIds = new Set<Id>();
                for (GroupMember member : members) {
                    userIds.add(member.UserOrGroupId);
                }
                users = [SELECT Id, Name, Email FROM User WHERE Id IN :userIds];
            }
            return users;
        }
    }

    public static List<ms_Subscription__c> getActiveSubscriptions(Id accountId) {
        Date today = System.today();
        return [
                SELECT Id
                FROM ms_Subscription__c
                WHERE School__c = :accountId
                AND StartDate__c <= :today
                AND EndDate__c >= :today
        ];
    }

    public static List<ms_Subscription__c> getAllSubscriptions(Id accountId) {
        return [
                SELECT Id, Type__c
                FROM ms_Subscription__c
                WHERE School__c = :accountId
        ];
    }

    public static Boolean hadTPPSubscription(Id accountId) {
        List<ms_Subscription__c> subscriptions = getAllSubscriptions(accountId);
        for (ms_Subscription__c subscription: subscriptions) {
            if (subscription.Type__c == 'TPP') {
                return true;
            }
        }
        return false;
    }

    public static Boolean hasTPPSubscriptionPlan(Id accountId) {
        return [SELECT MembershipType__c FROM Account WHERE Id = :accountId]?.MembershipType__c == 'TPP';
    }

    public static ms_DirectMembershipDiscount__mdt getDiscountForNow(Boolean isStudent) {
        Date today = System.today();
        return getDiscount(isStudent, today);
    }

    public static ms_DirectMembershipDiscount__mdt getDiscount(Boolean isStudent, Date theDate) {
        String type = isStudent ? 'Direct Membership Student' : 'Direct Membership Advisor';
        ms_DirectMembershipDiscount__mdt testMetadata = new ms_DirectMembershipDiscount__mdt(
                Price__c = 1000
        );
        return Test.isRunningTest() == false ? [
                SELECT Price__c
                FROM ms_DirectMembershipDiscount__mdt
                WHERE StartDate__c <= :theDate AND EndDate__c >= :theDate AND Type__c = :type
                LIMIT 1
        ] ?? null : testMetadata;
    }

    public static ms_Subscription__c getSubscriptionForSchoolMember(Contact member) {
        Boolean isTPP = member.Account.MembershipType__c == 'TPP';
        Boolean isStudent = member.PortalRole__c.contains('Student') || member.PortalRole__c.contains('Middle School Member');
        Decimal price = getSubscriptionPriceForSchoolMember(isTPP, isStudent);
        String name = (isTPP ? 'TPP' : 'Direct') + ' ' + (isStudent ? 'Student' : 'Advisor');
        name += ' (' + member.Name + ')';
        return new ms_Subscription__c(
                Name = name,
                Type__c = isTPP ? 'TPP' : 'Direct',
                Status__c = 'Active',
                Member__c = member.Id,
                School__c = member.AccountId,
                CalculatedPrice__c = price
        );
    }

    public static Decimal getSubscriptionPriceForSchoolMember(Boolean isTPP, Boolean isStudent) {
        ms_Settings__c settings = ms_Settings__c.getInstance();
        if (isTPP) {
            return isStudent
                    ? settings.AffiliationPriceStudent__c
                    : settings.AffiliationPriceAdvisor__c;
        } else {
            ms_DirectMembershipDiscount__mdt discount = getDiscountForNow(isStudent); // TODO: check date
            return discount?.Price__c ?? (
                    isStudent ? settings.DirectMembershipMember__c : settings.AffiliationPriceAdvisor__c
            );
        }
    }

    public static ms_Subscription__c getSubscriptionForPartner(Contact member) {
        Decimal price = getSubscriptionPriceForPartner();
        return new ms_Subscription__c(
                Name = 'Partner',
                Type__c = 'Partner',
                Status__c = 'Active',
                Member__c = member.Id,
                School__c = member.AccountId,
                CalculatedPrice__c = price
        );
    }

    public static Decimal getSubscriptionPriceForPartner() {
        return ms_Settings__c.getInstance().PartnerSubscriptionPrice__c;
    }

    public static ms_Subscription__c getTPPSubscriptionForAccount(Account theAccount, Integer numberOfMembers) {
        ms_StudentAffiliationPrice__mdt range = getSubscriptionTPPForAccount(numberOfMembers);
        return new ms_Subscription__c(
                Name = 'TPP (' + range.MinStudents__c + '-' + range.MaxStudents__c + ' students)',
                Type__c = 'TPP',
                Status__c = 'Active',
                School__c = theAccount.Id,
                CalculatedPrice__c = range.Price__c
        );
    }

    public static ms_StudentAffiliationPrice__mdt getSubscriptionTPPForAccount(Integer numberOfMembers) {
        ms_StudentAffiliationPrice__mdt range = [
                SELECT Price__c, MinStudents__c, MaxStudents__c
                FROM ms_StudentAffiliationPrice__mdt
                WHERE MinStudents__c <= :numberOfMembers
                AND MaxStudents__c >= :numberOfMembers
                LIMIT 1
        ] ?? null;

        if (Test.isRunningTest()) {
            return new ms_StudentAffiliationPrice__mdt(
                    Price__c = 1000,
                    MinStudents__c = 0,
                    MaxStudents__c = 1000
            );
        }

        if (range == null) {
            throw Error.exception('Range not found for ' + numberOfMembers + ' members.');
        }

        return range;
    }

    public static Datetime getNextSubscriptionExpirationDate() {
        Date today = Date.today();

        String expirationDate = ms_Settings__c.getInstance().MembershipExpirationDate__c;

        // Split the string '31/08' into month and day parts
        List<String> dateParts = expirationDate.split('/');
        Integer month = Integer.valueOf(dateParts[1]);
        Integer day = Integer.valueOf(dateParts[0]);

        // Create a date instance using the current year, month, and day
        Date targetDate = Date.newInstance(today.year(), month, day);

        return targetDate < today ? targetDate.addYears(1) : targetDate;
    }

    public static void sendEmailToContacts(List<Contact> contacts, String emailTemplateName) {
        String orgWideEmailId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;

        if (String.isBlank(emailTemplateName) || String.isBlank(orgWideEmailId)) {
            return;
        }

        List<EmailTemplate> template = [SELECT Id FROM EmailTemplate WHERE DeveloperName = :emailTemplateName AND IsActive = TRUE];

        if (template.isEmpty()) {
            return;
        }

        List<Messaging.SingleEmailMessage> emailMessages = new List<Messaging.SingleEmailMessage>();

        for (Contact LA: contacts) {
            if (LA.Email != null) {
                Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
                email.setTargetObjectId(LA.Id);
                email.setTemplateId(template[0].Id);
                email.setOrgWideEmailAddressId(orgWideEmailId);
                emailMessages.add(email);
            }
        }

        Messaging.sendEmail(emailMessages);
    }

    public static void deactivateUsersAndSendEmailByAccount(List<Account> accounts) {
        deactivateUsersAndSendEmailByAccountIds((new Map<Id, Account>(accounts)).keySet());
    }

    public static void deactivateUsersAndSendEmailByAccountIds(Set<Id> accountIds) {
        Map<Id, Contact> contactMap = new Map<Id, Contact>([SELECT Id FROM Contact WHERE AccountId IN :accountIds AND ms_IsActiveUser__c = TRUE]);

        if (contactMap.isEmpty() == false) {
            // Send email
            List<Contact> leadAdvisors = [SELECT Id, Email FROM Contact WHERE AccountId IN :accountIds AND PortalRole__c INCLUDES ('Lead Advisor')];

            if (leadAdvisors.isEmpty() == false) {
                String emailTemplateName = ms_Settings__c.getInstance().AccountDeactivationEmailTemplate__c;
                ms_Service.sendEmailToContacts(leadAdvisors, emailTemplateName);
            }

            ms_Service.toggleContacts(contactMap.keySet(), false);
            ms_Service.toggleUsersByContactIds(contactMap.keySet(), false);
        }
    }

    public static void activateUsersByAccountIds(Set<Id> accountIds) {
        Map<Id, Contact> contactMap = new Map<Id, Contact>([SELECT Id FROM Contact WHERE AccountId IN :accountIds AND IsGraduated__c = FALSE]);
        if (contactMap.isEmpty() == false) {
            ms_Service.toggleContacts(contactMap.keySet(), true);
            ms_Service.toggleUsersByContactIds(contactMap.keySet(), true);
        }
    }

    public static void deactivateGraduatedUsers(List<Account> accounts) {
        Map<Id, Contact> contactMap = new Map<Id, Contact>([SELECT Id FROM Contact WHERE AccountId IN :accounts AND IsGraduated__c = TRUE]);
        if (contactMap.isEmpty() == false) {
            ms_Service.toggleContacts(contactMap.keySet(), false);
            ms_Service.toggleUsersByContactIds(contactMap.keySet(), false);
        }
    }

    private static void toggleContacts(Set<Id> contactIds, Boolean isActive) {
        List<Contact> contacts = new List<Contact>();
        for (Id contactId: contactIds) {
            contacts.add(new Contact(Id = contactId, ms_IsActiveUser__c = isActive));
        }
        update contacts;
    }

    @Future
    private static void toggleUsersByContactIds(Set<Id> contactIds, Boolean isActive) {
        List<User> usersToDeactivate = [SELECT Id, ContactId FROM User WHERE ContactId IN :contactIds AND IsActive = :!isActive];
        for (User theUser: usersToDeactivate) {
            theUser.IsActive = isActive;
        }
        update usersToDeactivate;
    }
}