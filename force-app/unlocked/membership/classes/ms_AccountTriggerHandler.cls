public with sharing class ms_AccountTriggerHandler {

    static public Boolean runTriggers = true;

    final static Set<String> APPROVED_STATUSES_FOR_STUDENTS = new Set<String> {
            'User Created by Advisor',
            'User Created by Lead Advisor'
    };

    public List<Account> filterAccountsByChangedMembership(List<Account> accounts, Map<Id, Account> oldMap) {
        List<Account> result = new List<Account>();
        for (Account theAccount: accounts) {
            String oldType = oldMap.get(theAccount.Id).MembershipType__c;
            String newType = theAccount.MembershipType__c;
            if (newType != oldType) {
                result.add(theAccount);
            }
        }
        return result;
    }

    public void updateSubscriptionsForAccounts(List<Account> accounts) {
        List<ms_Subscription__c> expiredSubscriptions = this.expireOldSubscription(accounts);
        this.updateCredits(expiredSubscriptions);
        this.createNewSubscriptions(accounts);
    }

    public List<ms_Subscription__c> expireOldSubscription(List<Account> accounts) {
        List<ms_Subscription__c> activeSubscriptions = [
                SELECT Id, InvoiceStatus__c, InvoiceLine__r.TotalPrice__c, School__c, CalculatedPrice__c
                FROM ms_Subscription__c
                WHERE School__c IN :accounts
                AND Type__c IN ('Direct', 'TPP')
                AND Status__c NOT IN ('Expired', 'Canceled')
        ];
        for (ms_Subscription__c subscription: activeSubscriptions) {
            subscription.Status__c = 'Expired';
            subscription.EndDate__c = System.today();
        }
        update activeSubscriptions;

        // Delete pending related invoice lines
        if (!activeSubscriptions.isEmpty()) {
            delete [
                    SELECT Id
                    FROM InvoiceLine__c
                    WHERE Account__c IN :accounts
                    AND InvoiceStatus__c = NULL
                    AND ms_Subscription__c IN :activeSubscriptions
            ];
        }

        return activeSubscriptions;
    }

    public void updateCredits(List<ms_Subscription__c> expiredSubscriptions) {
        // Divide subscriptions by account
        Map<Id, List<ms_Subscription__c>> accountToSubscriptions = new Map<Id, List<ms_Subscription__c>>();
        for (ms_Subscription__c subscription: expiredSubscriptions) {
            List<ms_Subscription__c> subscriptions = accountToSubscriptions.get(subscription.School__c);
            if (subscriptions == null) {
                accountToSubscriptions.put(subscription.School__c, new List<ms_Subscription__c> { subscription });
            }
            else {
                subscriptions.add(subscription);
                accountToSubscriptions.put(subscription.School__c, subscriptions);
            }
        }

        // Find existing credits for each account
        Map<Id, InvoiceLine__c> accountToInvoiceLine = new Map<Id, InvoiceLine__c>();
        List<InvoiceLine__c> credits = [
                SELECT Id, Account__c
                FROM InvoiceLine__c
                WHERE Account__c IN :accountToSubscriptions.keySet()
                AND ms_IsCredit__c = TRUE
        ];
        for (InvoiceLine__c line: credits) {
            accountToInvoiceLine.put(line.Account__c, line);
        }

        List<InvoiceLine__c> invoiceLineCredits = new List<InvoiceLine__c>();
        for (Id accountId: accountToSubscriptions.keySet()) {
            List<ms_Subscription__c> subscriptions = accountToSubscriptions.get(accountId);
            Decimal creditSum = 0;
            for (ms_Subscription__c subscription: subscriptions) {
                if (subscription.InvoiceStatus__c == 'Paid') {
                    creditSum += subscription.CalculatedPrice__c;
                }
            }
            if (creditSum > 0) {
                Boolean hasExistingCredit = accountToInvoiceLine.get(accountId) != null;
                invoiceLineCredits.add(new InvoiceLine__c(
                        Id = hasExistingCredit ? accountToInvoiceLine.get(accountId).Id : null,
                        Name = 'Credit',
                        ms_IsCredit__c = true,
                        Account__c = accountId,
                        Quantity__c = 1,
                        Invoice__c = null,
                        UnitPrice__c = -creditSum
                ));
            }
        }
        upsert invoiceLineCredits;

    }

    public void createNewSubscriptions(List<Account> accounts) {
        List<ms_Subscription__c> newSubscriptions = new List<ms_Subscription__c>();
        List<Account> accountsWithContacts = [
                SELECT Id, MembershipType__c, ms_TPPNumberOfStudents__c, (
                        SELECT Id, Name, AccountId, Account.MembershipType__c, PortalRole__c, ApprovalStatus__c, GraduationYear__c
                        FROM Contacts
                        WHERE ms_IsActiveUser__c = TRUE
                        AND (
                            PortalRole__c INCLUDES ('Lead Advisor', 'Advisor') OR (
                                    (
                                            PortalRole__c = 'Student'
                                            AND ApprovalStatus__c IN :APPROVED_STATUSES_FOR_STUDENTS
                                            AND IsGraduated__c = FALSE
                                    )
                                    OR
                                    (PortalRole__c = 'Middle School Member' AND IsGraduated__c = FALSE)
                            )
                        )
                )
                FROM Account
                WHERE Id IN :accounts
        ];
        for (Account theAccount: accountsWithContacts) {
            Integer numberOfStudents = 0;
            for (Contact theContact: theAccount.Contacts) {
                if (theContact.PortalRole__c.contains('Student') || theContact.PortalRole__c.contains('Middle School Member')) {
                    numberOfStudents++;
                }
                newSubscriptions.add(ms_Service.getSubscriptionForSchoolMember(theContact));
            }
            if (theAccount.MembershipType__c == 'TPP') { // Create subscription under account
                Integer numberOfStudentsForTPP = Integer.valueOf(theAccount.ms_TPPNumberOfStudents__c ?? numberOfStudents);
                newSubscriptions.add(ms_Service.getTPPSubscriptionForAccount(theAccount, numberOfStudentsForTPP));
            }
        }
        insert newSubscriptions;
    }

}