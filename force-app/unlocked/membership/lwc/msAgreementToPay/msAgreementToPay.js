import { LightningElement } from "lwc";
import init from "@salesforce/apex/ms_AgreementToPayCtrl.init";
import saveAgreementAnswer from "@salesforce/apex/ms_AgreementToPayCtrl.saveAgreementAnswer";
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { showErrors } from 'c/errorHandler';
import LightningConfirm from 'lightning/confirm';

export default class extends LightningElement {
    showComponent = false;
    expirationDateRaw = '';
    hideLabel = 'Hide';

    get expirationDate() {
        const date = new Date(this.expirationDateRaw);

        return  date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    connectedCallback() {
        init()
            .then(response => {
                this.showComponent = response.displayAlert;
                this.expirationDateRaw = response.expirationDate;
                console.log(111, JSON.stringify(response));
            })
            .catch(error => {
                console.error('error', error);
            })
    }

    async agree() {
        saveAgreementAnswer({isAgree: true})
            .then(() => {
                this.handleResponse();
            })
            .catch(error => showErrors(this, error))
    }

    async disagree() {
        const result = await LightningConfirm.open({
            message: 'Confirm Membership Cancellation',
            variant: 'header',
            theme: 'error'
        });

        if (result) {
            saveAgreementAnswer({isAgree: false})
                .then(() => {
                    this.handleResponse();
                })
                .catch(error => showErrors(this, error))
        }
    }

    handleResponse() {
        this.showComponent = false;
        const event = new ShowToastEvent({
            title: 'Answer saved!',
            message:
                'Thank you for your answer.',
        });
        this.dispatchEvent(event);
    }

    hide() {
        this.hideLabel = this.hideLabel === 'Hide' ? 'Show' : 'Hide';
        this.refs.content.className = this.refs.content.className === 'slds-hide' ? 'slds-p-horizontal_small' : 'slds-hide';
    }

}