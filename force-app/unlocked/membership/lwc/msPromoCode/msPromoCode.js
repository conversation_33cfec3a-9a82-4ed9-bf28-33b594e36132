import { LightningElement, track } from "lwc";
import getPromoCodes from "@salesforce/apex/ms_PromoCodeCtrl.getPromoCodes";
import { showErrors } from 'c/errorHandler';

export default class extends LightningElement {
    @track promoCodes = [];
    showPromoCodes = false;

    connectedCallback() {
        getPromoCodes()
            .then((promoCodes) => {
                this.promoCodes = promoCodes;
                this.showPromoCodes = this.promoCodes.length > 0;
                console.log('>' , promoCodes);
            })
            .catch(errors => {
                showErrors(this, errors);
            });
    }

}