<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>NotifyLAAboutSwitchingToTPP</name>
        <label>Notify LA About Switching To TPP</label>
        <locationX>314</locationX>
        <locationY>566</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>SuccessScreen</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>RollbackEverything</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>recordId.Contact__r.Email</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>Notification of School&apos;s Transition to a New Tier in Total Participation Plan</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>TextBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <versionSegment>1</versionSegment>
    </actionCalls>
    <apiVersion>60.0</apiVersion>
    <decisions>
        <name>CheckStatusAccountMembership</name>
        <label>Check Status &amp; Account Membership</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>ConfirmationScreen</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IsStatusApprovedOrMembershipAlreadyTPP</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>recordId.Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Approved</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>recordId.School__r.MembershipType__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>TPP</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>SchoolIsAlreadyTPP</targetReference>
            </connector>
            <label>Is Status Approved Or Membership Already TPP</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Update Membership Type Of Related School {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[MS] Enable Membership Of School As TPP</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordRollbacks>
        <name>RollbackEverything</name>
        <label>Rollback Everything</label>
        <locationX>1106</locationX>
        <locationY>458</locationY>
        <connector>
            <targetReference>FaultScreen</targetReference>
        </connector>
    </recordRollbacks>
    <recordUpdates>
        <name>UpdateAccountRecord</name>
        <label>Update Account Record</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <connector>
            <targetReference>UpdateTPPRecordStatus</targetReference>
        </connector>
        <faultConnector>
            <targetReference>RollbackEverything</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.School__r.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>MembershipType__c</field>
            <value>
                <stringValue>TPP</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ms_TPPNumberOfStudents__c</field>
            <value>
                <elementReference>recordId.EstimatedNumberOfStudents__c</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateTPPRecordStatus</name>
        <label>Update TPP Record Status</label>
        <locationX>314</locationX>
        <locationY>458</locationY>
        <connector>
            <targetReference>NotifyLAAboutSwitchingToTPP</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>RollbackEverything</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status__c</field>
            <value>
                <stringValue>Approved</stringValue>
            </value>
        </inputAssignments>
        <object>ms_TPPRequest__c</object>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>ConfirmationScreen</name>
        <label>Confirmation Screen</label>
        <locationX>314</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>UpdateAccountRecord</targetReference>
        </connector>
        <fields>
            <name>AreYouSureText</name>
            <fieldText>&lt;p&gt;Are you sure you want to enable TPP for the &lt;strong&gt;{!recordId.School__r.Name}&lt;/strong&gt; school?&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Confirm</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>FaultScreen</name>
        <label>FaultScreen</label>
        <locationX>1106</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>FaultMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;color: rgb(208, 0, 0);&quot;&gt;Oops! Something went wrong.&lt;/span&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;color: rgb(208, 0, 0);&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>SchoolIsAlreadyTPP</name>
        <label>SchoolIsAlreadyTPP</label>
        <locationX>50</locationX>
        <locationY>242</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>AlreadyOnTPPText</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;The &lt;/span&gt;&lt;strong style=&quot;font-size: 12px;&quot;&gt;{!recordId.School__r.Name} &lt;/strong&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;school is already using &lt;/span&gt;&lt;strong style=&quot;font-size: 12px;&quot;&gt;TPP &lt;/strong&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;or TPP Record has Approved status field value.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Close</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>SuccessScreen</name>
        <label>SuccessScreen</label>
        <locationX>314</locationX>
        <locationY>674</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>CompeletedText</name>
            <fieldText>&lt;p&gt;&lt;strong style=&quot;color: rgb(0, 186, 59);&quot;&gt;Done! Action completed.&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>CheckStatusAccountMembership</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>TextBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;We are pleased to inform you that your school has transitioned to a new tier in the Total Participation Plan. Please contact us if you have any questions.&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>ms_TPPRequest__c</objectType>
    </variables>
</Flow>
