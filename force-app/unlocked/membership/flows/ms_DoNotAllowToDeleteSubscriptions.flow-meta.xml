<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <customErrors>
        <description>You can&apos;t delete subscriptions. Please set status of the subscription to Expired or Cancelled instead.</description>
        <name>YouCantDeleteSubscriptions</name>
        <label>You can&apos;t delete subscriptions</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <customErrorMessages>
            <errorMessage>&quot;You can&apos;t delete subscriptions. Please set the status of the subscription to Expired or Cancelled instead.&quot;</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <description>Prevent users from deleting subscription records.</description>
    <environments>Default</environments>
    <interviewLabel>[MS] Do Not Allow To Delete Subscriptions {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[MS] Do Not Allow To Delete Subscriptions</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>YouCantDeleteSubscriptions</targetReference>
        </connector>
        <object>ms_Subscription__c</object>
        <recordTriggerType>Delete</recordTriggerType>
        <triggerType>RecordBeforeDelete</triggerType>
    </start>
    <status>Active</status>
    <triggerOrder>1</triggerOrder>
</Flow>
