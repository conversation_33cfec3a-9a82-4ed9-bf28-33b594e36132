<?xml version="1.0" encoding="UTF-8" ?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>UpgradeRoomTypeName__c</fullName>
    <description
    >Room Type Name value of another Room Type Setting. If this field is specified room with current type can be upgraded to another type specified in this field</description>
    <externalId>false</externalId>
    <fieldManageability>DeveloperControlled</fieldManageability>
    <label>Upgrade Room Type</label>
    <length>255</length>
    <required>false</required>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
