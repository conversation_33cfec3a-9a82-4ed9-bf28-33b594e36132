public with sharing class hm_PreferredHotelsPickerCtrl {
    @AuraEnabled
    public static List<hm_DTO.HotelRequestPreferredHotel> getAvailableHotels() {
        try {
            return hm_DTO.hotelsToPreferredHotelsToDtos(hm_Selector.getAvailableHotels());
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static List<hm_DTO.HotelRequestPreferredHotel> getPreferredHotels(Id hotelRequestId) {
        try {
            return hm_DTO.hotelRequestPreferredHotelsToDtos(hm_Selector.getPreferredHotels(hotelRequestId));
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void savePreferredHotels(List<hm_DTO.HotelRequestPreferredHotel> prefHotels) {
        try {
            List<hm_HotelRequestPreferredHotel__c> prefHotelRecords = hm_DTO.hotelRequestPreferredHotelsToSObjects(
                prefHotels
            );
            Database.upsert(prefHotelRecords, AccessLevel.USER_MODE);
            Set<Id> prefHotelIds = new Map<Id, hm_HotelRequestPreferredHotel__c>(prefHotelRecords).keySet();
            List<hm_HotelRequestPreferredHotel__c> prefHotelsForDelete = hm_Selector.getRemainingPreferredHotels(
                prefHotelRecords.get(0).HotelRequest__c,
                prefHotelIds
            );
            Database.delete(prefHotelsForDelete, AccessLevel.USER_MODE);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}