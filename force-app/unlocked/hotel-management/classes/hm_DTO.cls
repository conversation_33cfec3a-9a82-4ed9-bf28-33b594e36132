public with sharing class hm_DTO {
    public class HotelRequest {
        @AuraEnabled
        public Id id { get; set; }
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public Date checkInDate { get; set; }
        @AuraEnabled
        public Date checkOutDate { get; set; }
        @AuraEnabled
        public List<HotelRequestRoom> rooms { get; set; }
        @AuraEnabled
        public List<HotelRequestRoom> removedRooms { get; set; }
        @AuraEnabled
        public List<HotelRequestVisitor> visitors { get; set; }
        @AuraEnabled
        public Boolean readOnly { get; set; }
        @AuraEnabled
        public Boolean onSiteTransportation { get; set; }
        @AuraEnabled
        public Map<String, Integer> typeToRoomCount { get; set; }
        @AuraEnabled
        public String selectedHotelId { get; set; }
        @AuraEnabled
        public String enforcedHotelId { get; set; }
        @AuraEnabled
        public String enforcedHotelName { get; set; }
        @AuraEnabled
        public String accountName { get; set; }
        @AuraEnabled
        public List<HotelRequestPreferredHotel> hotelPrefs { get; set; }
        //stores diff between requested room count and real room count needed for request
        @AuraEnabled
        public Map<String, Integer> typeToReducedRoomCount { get; set; }

        public HotelRequest() {
            return;
        }

        public HotelRequest(hm_HotelRequest__c hotelRequest) {
            Map<String, Object> recordFieldsMap = hotelRequest.getPopulatedFieldsAsMap();

            this.id = hotelRequest.Id;
            this.checkInDate = (Date) recordFieldsMap.get('CheckInDate__c');
            this.checkOutDate = (Date) recordFieldsMap.get('CheckOutDate__c');
            this.readOnly = (Boolean) recordFieldsMap.get('ReadOnly__c');
            this.onSiteTransportation = (Boolean) recordFieldsMap.get('OnSiteTransportation__c');
            this.enforcedHotelId = (Id) recordFieldsMap.get('EnforcedHotel__c');
            this.rooms = new List<HotelRequestRoom>();
            this.visitors = new List<HotelRequestVisitor>();
        }
    }

    public class Hotel {
        @AuraEnabled
        public Id id { get; set; }
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public Boolean isInWalkingDistance { get; set; }
        @AuraEnabled
        public Map<String, Integer> typeToRoomCount { get; set; }
        @AuraEnabled
        public Map<String, Integer> typeToCurrentRoomCount { get; set; }
        @AuraEnabled
        public List<HotelRequest> allocatedRequests { get; set; }

        public Hotel() {
            return;
        }

        public Hotel(hm_Hotel__c hotel, Map<String, Integer> typeToRoomCount) {
            Map<String, Object> recordFieldsMap = hotel.getPopulatedFieldsAsMap();

            this.id = hotel.Id;
            this.name = (String) recordFieldsMap.get('Name');
            this.isInWalkingDistance = (Boolean) recordFieldsMap.get('IsInWalkingDistanceFromEvent__c');
            this.typeToRoomCount = typeToRoomCount;
            this.typeToCurrentRoomCount = new Map<String, Integer>(this.typeToRoomCount);
            this.allocatedRequests = new List<HotelRequest>();
        }
    }

    public class HotelRequestPreferredHotel {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String hotelRequestId { get; set; }
        @AuraEnabled
        public String hotelId { get; set; }
        @AuraEnabled
        public String hotelName { get; set; }
        @AuraEnabled
        public Decimal order { get; set; }
        @AuraEnabled
        public Boolean isInWalkingDistance { get; set; }
        @AuraEnabled
        public String site { get; set; }
        @AuraEnabled
        public String address { get; set; }
        @AuraEnabled
        public String confHallDistance { get; set; }
        @AuraEnabled
        public String mapLink { get; set; }

        public HotelRequestPreferredHotel() {
            return;
        }

        public HotelRequestPreferredHotel(hm_HotelRequestPreferredHotel__c prefHotel) {
            Map<String, Object> recordFieldsMap = prefHotel.getPopulatedFieldsAsMap();

            this.id = prefHotel.Id;
            this.hotelRequestId = (Id) recordFieldsMap.get('HotelRequest__c');
            this.hotelId = (Id) recordFieldsMap.get('Hotel__c');
            this.order = Integer.valueOf(recordFieldsMap.get('PriorityOrder__c'));

            if (recordFieldsMap.containsKey('Hotel__r')) {
                Map<String, Object> hotelFieldsMap = prefHotel.Hotel__r.getPopulatedFieldsAsMap();

                this.hotelName = (String) hotelFieldsMap.get('Name');
                this.isInWalkingDistance = (Boolean) hotelFieldsMap.get('IsInWalkingDistanceFromEvent__c');
                this.site = (String) hotelFieldsMap.get('Site__c');
                this.address = (String) hotelFieldsMap.get('Address__c');
                this.confHallDistance = (String) hotelFieldsMap.get('DistanceToConferenceHall__c');
                this.mapLink = (String) hotelFieldsMap.get('MapLink__c');
            }
        }

        public HotelRequestPreferredHotel(hm_Hotel__c hotel) {
            this.hotelId = hotel.Id;
            this.hotelName = hotel.Name;
            this.isInWalkingDistance = hotel.IsInWalkingDistanceFromEvent__c;
            this.site = hotel.Site__c;
            this.address = hotel.Address__c;
            this.confHallDistance = hotel.DistanceToConferenceHall__c;
            this.mapLink = hotel.MapLink__c;
        }

        public hm_HotelRequestPreferredHotel__c toSObject() {
            return new hm_HotelRequestPreferredHotel__c(
                Id = this.id,
                HotelRequest__c = this.hotelRequestId,
                Hotel__c = this.hotelId,
                PriorityOrder__c = this.order + 1
            );
        }
    }

    public class HotelRequestFormData {
        @AuraEnabled
        public aclab.FormLayout layout { get; set; }
        @AuraEnabled
        public hm_HotelRequest__c record { get; set; }
        @AuraEnabled
        public Event event { get; set; }
    }

    public class HotelRequestRoom {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public Date checkInDate { get; set; }
        @AuraEnabled
        public Date checkOutDate { get; set; }
        @AuraEnabled
        public String type { get; set; }
        @AuraEnabled
        public string hotelRequestId { get; set; }
        @AuraEnabled
        public Integer visitorCount { get; set; }
        @AuraEnabled
        public List<HotelRequestVisitor> visitors { get; set; }

        public HotelRequestRoom() {
            return;
        }

        public HotelRequestRoom(hm_HotelRequestRoom__c hotelRequestRoom) {
            Map<String, Object> recordFieldsMap = hotelRequestRoom.getPopulatedFieldsAsMap();

            this.id = hotelRequestRoom.Id;
            this.hotelRequestId = (Id) recordFieldsMap.get('HotelRequest__c');
            this.checkInDate = (Date) recordFieldsMap.get('CheckInDate__c');
            this.checkOutDate = (Date) recordFieldsMap.get('CheckOutDate__c');
            this.type = (String) recordFieldsMap.get('Type__c');
            this.visitors = new List<HotelRequestVisitor>();
        }

        public hm_HotelRequestRoom__c toSObject() {
            return new hm_HotelRequestRoom__c(
                Id = this.id,
                CheckInDate__c = this.checkInDate,
                CheckOutDate__c = this.checkOutDate,
                HotelRequest__c = this.hotelRequestId,
                Type__c = this.type
            );
        }
    }

    public class HotelRequestVisitor {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String firstName { get; set; }
        @AuraEnabled
        public String lastName { get; set; }
        @AuraEnabled
        public String roomId { get; set; }
        @AuraEnabled
        public String portalRole { get; set; }
        @AuraEnabled
        public String gender { get; set; }
        @AuraEnabled
        public Integer roomPlace { get; set; }

        public HotelRequestVisitor() {
            return;
        }

        public HotelRequestVisitor(hm_HotelRequestVisitor__c hotelRequestVisitor) {
            this.id = hotelRequestVisitor.id;
            this.firstName = hotelRequestVisitor.FirstName__c;
            this.lastName = hotelRequestVisitor.LastName__c;
            this.roomId = hotelRequestVisitor.HotelRequestRoom__c;
            this.roomPlace = (Integer) hotelRequestVisitor.RoomPlaceNumber__c;
            this.gender = hotelRequestVisitor.Gender__c;
            this.portalRole = String.isNotBlank(hotelRequestVisitor.Contact__r.PortalRole__c)
                ? hotelRequestVisitor.Contact__r.PortalRole__c.split(';').get(0)
                : System.Label.hmGuestVisitorGroup;
        }

        public hm_HotelRequestVisitor__c toSObject() {
            return new hm_HotelRequestVisitor__c(
                Id = this.id,
                HotelRequestRoom__c = this.roomId,
                RoomPlaceNumber__c = this.roomPlace
            );
        }
    }

    public class EventHotelRequestsSummary {
        @AuraEnabled
        public String eventId { get; set; }
        @AuraEnabled
        public String eventName { get; set; }
        @AuraEnabled
        public Integer schoolCount { get; set; }
        @AuraEnabled
        public Integer guestCount { get; set; }
        @AuraEnabled
        public Integer hotelCount { get; set; }
        @AuraEnabled
        public Map<String, RoomTypeUsage> nameToRoomType { get; set; }
    }

    public class HotelTetrisResultSummary {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String eventName { get; set; }
        @AuraEnabled
        public Integer hotelCount { get; set; }
        @AuraEnabled
        public Integer orgCount { get; set; }
        @AuraEnabled
        public Datetime lastSentToAllHotelManagerDate { get; set; }
        @AuraEnabled
        public List<HotelTetrisResultGroupSummary> groups { get; set; }
    }

    public class HotelTetrisResultGroupSummary {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String hotelName { get; set; }
        @AuraEnabled
        public String hotelEmail { get; set; }
        @AuraEnabled
        public Datetime lastSentToHotelManagerAt { get; set; }
        @AuraEnabled
        public Datetime lastAnswerFromHotelManagerAt { get; set; }
        @AuraEnabled
        public Integer totalReservationLineCount { get; set; }
        @AuraEnabled
        public Integer reservationLinesForApprovalCount { get; set; }
        @AuraEnabled
        public Integer approvedReservationLineCount { get; set; }
        @AuraEnabled
        public Integer rejectedReservationLineCount { get; set; }
        @AuraEnabled
        public Boolean isApprovalNeeded { get; set; }
    }

    public class HotelManagementApprovalSummary {
        @AuraEnabled
        public Map<String, RoomTypeUsage> nameToRoomType { get; set; }
        @AuraEnabled
        public Integer companyCount { get; set; }
        @AuraEnabled
        public List<Record> reservationLines { get; set; }
    }

    public virtual class RoomType {
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public String label { get; set; }
        @AuraEnabled
        public Integer capacity { get; set; }
        @AuraEnabled
        public String upgradeTypeName { get; set; }
        @AuraEnabled
        public Integer minVisitorsForBooking { get; set; }
        @AuraEnabled
        public Integer minVisitorsForUpgrade { get; set; }

        public RoomType() {
        }

        public RoomType(String name, String label, Integer capacity) {
            this.name = name;
            this.label = label;
            this.capacity = capacity;
        }
    }

    public class HotelTetrisData {
        @AuraEnabled
        public String eventId { get; set; }
        @AuraEnabled
        public List<Hotel> hotels { get; set; }
        @AuraEnabled
        public List<HotelRequest> requests { get; set; }
    }

    public class HotelTetrisResult {
        @AuraEnabled
        public String eventId { get; set; }
        @AuraEnabled
        public List<Hotel> hotels { get; set; }
        @AuraEnabled
        public List<HotelRequest> requests { get; set; }
        public Map<Id, Hotel> idToHotel {
            get {
                return this.getIdToHotel();
            }
            private set;
        }

        private Map<Id, Hotel> getIdToHotel() {
            Map<Id, Hotel> idToHotel = new Map<Id, Hotel>();
            for (Hotel hotel : this.hotels) {
                idToHotel.put(hotel.id, hotel);
            }
            return idToHotel;
        }
    }

    public class RoomTypeUsage extends RoomType {
        @AuraEnabled
        public Integer count { get; set; }

        public RoomTypeUsage(RoomType roomType) {
            this.name = roomType.name;
            this.label = roomType.label;
            this.capacity = roomType.capacity;
            this.count = 0;
        }
    }

    public class Record {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public Boolean editable { get; set; }
        @AuraEnabled
        public List<RecordField> fields { get; set; }
    }

    public class RecordField {
        @AuraEnabled
        public FieldAttributes attributes { get; set; }
        @AuraEnabled
        public Object value { get; set; }
        @AuraEnabled
        public Object oldValue { get; set; }

        public RecordField() {
        }
        public RecordField(String name, Object value) {
            this.attributes = new FieldAttributes();
            this.attributes.name = name;
            this.value = value;
        }
    }

    public class FieldAttributes {
        @AuraEnabled
        public String type { get; set; }
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public String label { get; set; }
        @AuraEnabled
        public Boolean editable { get; set; }
        @AuraEnabled
        public Boolean required { get; set; }
        @AuraEnabled
        public List<FieldOption> options { get; set; }
    }

    public class FieldOption {
        @AuraEnabled
        public String label { get; set; }
        @AuraEnabled
        public String value { get; set; }
    }

    public class Event {
        @AuraEnabled
        public String id { get; set; }
        @AuraEnabled
        public String name { get; set; }
        @AuraEnabled
        public Date startDate { get; set; }
        @AuraEnabled
        public Date endDate { get; set; }
        @AuraEnabled
        public Date regStartDate { get; set; }
        @AuraEnabled
        public Date regEndDate { get; set; }
    }

    public static List<hm_HotelRequestPreferredHotel__c> hotelRequestPreferredHotelsToSObjects(
        List<HotelRequestPreferredHotel> dtoPrefHotels
    ) {
        List<hm_HotelRequestPreferredHotel__c> prefHotels = new List<hm_HotelRequestPreferredHotel__c>();

        for (HotelRequestPreferredHotel dtoPrefHotel : dtoPrefHotels) {
            prefHotels.add(dtoPrefHotel.toSObject());
        }

        return prefHotels;
    }

    public static List<HotelRequestPreferredHotel> hotelRequestPreferredHotelsToDtos(
        List<hm_HotelRequestPreferredHotel__c> prefHotels
    ) {
        List<HotelRequestPreferredHotel> dtoPrefHotels = new List<HotelRequestPreferredHotel>();

        for (hm_HotelRequestPreferredHotel__c prefHotel : prefHotels) {
            dtoPrefHotels.add(new HotelRequestPreferredHotel(prefHotel));
        }

        return dtoPrefHotels;
    }

    public static List<HotelRequestPreferredHotel> hotelsToPreferredHotelsToDtos(List<hm_Hotel__c> hotels) {
        List<HotelRequestPreferredHotel> dtoPrefHotels = new List<HotelRequestPreferredHotel>();

        for (hm_Hotel__c hotel : hotels) {
            dtoPrefHotels.add(new HotelRequestPreferredHotel(hotel));
        }

        return dtoPrefHotels;
    }

    public static List<hm_HotelRequestRoom__c> hotelRequestRoomsToSObjects(List<HotelRequestRoom> dtoRooms) {
        List<hm_HotelRequestRoom__c> rooms = new List<hm_HotelRequestRoom__c>();

        for (HotelRequestRoom dtoRoom : dtoRooms) {
            rooms.add(dtoRoom.toSObject());
        }

        return rooms;
    }

    public static List<hm_HotelRequestVisitor__c> hotelRequestVisitorsToSObjects(
        List<HotelRequestVisitor> dtoVisitors
    ) {
        List<hm_HotelRequestVisitor__c> visitors = new List<hm_HotelRequestVisitor__c>();

        for (HotelRequestVisitor dtoVisitor : dtoVisitors) {
            visitors.add(dtoVisitor.toSObject());
        }

        return visitors;
    }

    public static List<Record> sobjectsWithFieldSetToRecords(List<SObject> records, List<FieldSetMember> fsMembers) {
        List<Record> recordDtos = new List<Record>();
        Map<String, FieldAttributes> fieldNameToAttrs = fieldSetMembersToFieldAttributesMap(fsMembers);

        for (SObject record : records) {
            Record recordDto = new Record();
            recordDto.id = record.Id;
            recordDto.fields = new List<RecordField>();
            for (FieldSetMember fsMember : fsMembers) {
                String fieldName = fsMember.getFieldPath();
                hm_DTO.RecordField recordFieldDto = new hm_DTO.RecordField();
                recordFieldDto.attributes = fieldNameToAttrs.get(fieldName);
                recordFieldDto.value = record.get(fieldName);
                recordDto.fields.add(recordFieldDto);
            }
            recordDtos.add(recordDto);
        }
        return recordDtos;
    }

    public static Map<String, FieldAttributes> fieldSetMembersToFieldAttributesMap(List<FieldSetMember> fsMembers) {
        Map<String, FieldAttributes> nameToFieldAttr = new Map<String, FieldAttributes>();

        for (FieldSetMember fsMember : fsMembers) {
            String fieldName = fsMember.getFieldPath();
            FieldAttributes fieldAttr = new FieldAttributes();
            fieldAttr.name = fieldName;
            fieldAttr.label = fsMember.getLabel();
            fieldAttr.type = String.valueOf(fsMember.getType());
            fieldAttr.required = fsMember.getRequired();

            if (fsMember.getType() == Schema.DisplayType.PICKLIST) {
                fieldAttr.options = new List<FieldOption>();
                for (Schema.PicklistEntry pickEntry : fsMember.getSObjectField().getDescribe().getPicklistValues()) {
                    FieldOption option = new FieldOption();
                    option.label = pickEntry.getLabel();
                    option.value = pickEntry.getValue();
                    fieldAttr.options.add(option);
                }
            }
            nameToFieldAttr.put(fieldAttr.name, fieldAttr);
        }

        return nameToFieldAttr;
    }

    public static List<Hotel> convertHotels(List<hm_Hotel__c> hotels) {
        Set<Id> hotelIds = new Map<Id, hm_Hotel__c>(hotels).keySet();
        Map<Id, Map<String, Integer>> hotelIdToRoomCount = hm_HotelService.getHotelRoomCountPerType(hotelIds);
        List<Hotel> hotelDtos = new List<Hotel>();

        for (hm_Hotel__c hotel : hotels) {
            hotelDtos.add(new Hotel(hotel, hotelIdToRoomCount.get(hotel.Id)));
        }
        return hotelDtos;
    }

    public static List<HotelRequest> convertRequests(List<hm_HotelRequest__c> requests) {
        List<HotelRequest> requestDtos = new List<HotelRequest>();

        for (hm_HotelRequest__c request : requests) {
            Map<String, List<hm_HotelRequestRoom__c>> typeToRooms = hm_HotelRequestService.groupRoomsByType(
                request.HotelRequestRooms__r
            );

            HotelRequest requestDto = new HotelRequest(request);
            requestDto.name = request.Name;
            requestDto.hotelPrefs = convertPreferredHotels(request.HotelRequestPreferredHotels__r);
            requestDto.enforcedHotelName = request.EnforcedHotel__r?.Name;
            requestDto.accountName = request.Account__r.Name;
            requestDto.rooms = convertHotelRequestRooms(request.HotelRequestRooms__r);

            requestDtos.add(requestDto);
        }
        return requestDtos;
    }

    public static List<HotelRequestRoom> convertHotelRequestRooms(List<hm_HotelRequestRoom__c> hotelRequestRooms) {
        List<HotelRequestRoom> roomDtos = new List<HotelRequestRoom>();

        for (hm_HotelRequestRoom__c room : hotelRequestRooms) {
            HotelRequestRoom roomDto = new HotelRequestRoom(room);
            roomDto.visitorCount = room.HotelRequestVisitors__r.size();
            roomDtos.add(roomDto);
        }
        return roomDtos;
    }

    public static List<HotelRequestPreferredHotel> convertPreferredHotels(
        List<hm_HotelRequestPreferredHotel__c> prefHotels
    ) {
        List<HotelRequestPreferredHotel> prefHotelDtos = new List<HotelRequestPreferredHotel>();
        for (hm_HotelRequestPreferredHotel__c prefHotel : prefHotels) {
            prefHotelDtos.add(new HotelRequestPreferredHotel(prefHotel));
        }
        return prefHotelDtos;
    }
}