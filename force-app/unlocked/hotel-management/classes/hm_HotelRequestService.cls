public with sharing class hm_HotelRequestService {
    private static final Set<String> SERVICE_FIELDS = new Set<String>{
        String.valueOf(hm_HotelRequest__c.ReadOnly__c),
        String.valueOf(hm_HotelRequest__c.EventId__c),
        String.valueOf(hm_HotelRequest__c.Account__c)
    };

    public static hm_DTO.HotelRequestFormData getHotelRequestFormData(Id hotelRequestId) {
        aclab.FormLayout layout = aclab.Form.retrieve(
            hm_HotelManagementSettings__c.getInstance().HotelRequestFormLayoutName__c
        );
        Set<String> queryFields = layout.getAllFieldNames();
        queryFields.addAll(SERVICE_FIELDS);
        Set<String> inaccessibleFields = new Set<String>();
        Map<String, Schema.SObjectField> nameToFieldDescribe = Schema.SObjectType.hm_HotelRequest__c.fields.getMap();

        for (String fieldName : queryFields) {
            if (!nameToFieldDescribe.get(fieldName).getDescribe().isAccessible()) {
                inaccessibleFields.add(fieldName);
            }
        }

        queryFields.removeAll(inaccessibleFields);

        hm_HotelRequest__c hotelRequest;
        hm_DTO.Event event;

        if (hotelRequestId != null) {
            hotelRequest = hm_Selector.getHotelRequestById(queryFields, hotelRequestId);
            event = hm_EventService.getEventForRegistration(hotelRequest.EventId__c);
        } else {
            Id accountId = getHotelRequestAccountId();
            Id eventId = hm_EventService.getActiveEventId();
            event = hm_EventService.getEventForRegistration(eventId);

            hotelRequest = hm_Selector.getHotelRequestByEventIdAccountId(queryFields, eventId, accountId);

            if (hotelRequest == null) {
                hotelRequest = new hm_HotelRequest__c(
                    Account__c = accountId,
                    CheckInDate__c = event.startDate,
                    CheckOutDate__c = event.endDate,
                    EventId__c = event.id
                );
            }
        }

        hm_DTO.HotelRequestFormData formData = new hm_DTO.HotelRequestFormData();
        formData.layout = layout;
        formData.record = hotelRequest;
        formData.event = event;

        return formData;
    }

    public static Id saveHotelRequest(hm_HotelRequest__c hotelRequest) {
        SObjectAccessDecision accessDecision = Security.stripInaccessible(
            AccessType.UPSERTABLE,
            new List<hm_HotelRequest__c>{ hotelRequest }
        );

        hotelRequest = (hm_HotelRequest__c) accessDecision.getRecords().get(0);

        Set<String> removedFields = accessDecision.getRemovedFields()
            .get(String.valueOf(hm_HotelRequest__c.SObjectType));

        if (
            removedFields != null &&
            (removedFields.contains(String.valueOf(hm_HotelRequest__c.CheckInDate__c)) ||
            removedFields.contains(String.valueOf(hm_HotelRequest__c.CheckOutDate__c)))
        ) {
            hm_DTO.Event event = hm_EventService.getEvent(hotelRequest.EventId__c, AccessLevel.SYSTEM_MODE);
            hotelRequest.CheckInDate__c = event.startDate;
            hotelRequest.CheckOutDate__c = event.endDate;
        }

        Database.upsert(hotelRequest, AccessLevel.SYSTEM_MODE);

        return hotelRequest.Id;
    }

    public static hm_DTO.HotelRequest getHotelRequestForRoomAssignment(Id hotelRequestId) {
        hm_HotelRequest__c hotelRequest = hm_Selector.getHotelRequestWithRoomVisitors(hotelRequestId);
        hm_DTO.HotelRequest hotelRequestDto = new hm_DTO.HotelRequest(hotelRequest);

        Map<Id, List<hm_DTO.HotelRequestVisitor>> roomIdToVisitorDtos = new Map<Id, List<hm_DTO.HotelRequestVisitor>>();
        for (hm_HotelRequestVisitor__c visitor : hotelRequest.HotelRequestVisitors__r) {
            hm_DTO.HotelRequestVisitor visitorDto = new hm_DTO.HotelRequestVisitor(visitor);
            hotelRequestDto.visitors.add(visitorDto);
        }

        for (hm_HotelRequestRoom__c room : hotelRequest.HotelRequestRooms__r) {
            hm_DTO.HotelRequestRoom roomDto = new hm_DTO.HotelRequestRoom(room);
            hotelRequestDto.rooms.add(roomDto);
        }

        return hotelRequestDto;
    }

    public static void saveRoomsWithVisitors(hm_DTO.HotelRequest hotelRequestDto) {
        List<hm_HotelRequestRoom__c> rooms = hm_DTO.hotelRequestRoomsToSObjects(hotelRequestDto.rooms);
        SObjectAccessDecision accessDecision = Security.stripInaccessible(AccessType.CREATABLE, rooms);
        rooms = (List<hm_HotelRequestRoom__c>) accessDecision.getRecords();
        Set<String> removedFields = accessDecision.getRemovedFields()
            .get(String.valueOf(hm_HotelRequestRoom__c.SObjectType));

        if (
            removedFields != null &&
            (removedFields.contains(String.valueOf(hm_HotelRequestRoom__c.CheckInDate__c)) ||
            removedFields.contains(String.valueOf(hm_HotelRequestRoom__c.CheckOutDate__c)))
        ) {
            copyHotelRequestDatesToNewRoomDates(hotelRequestDto.id, rooms);
        }

        Database.upsert(rooms, AccessLevel.SYSTEM_MODE);

        Map<Id, hm_HotelRequestVisitor__c> idToVisitor = new Map<Id, hm_HotelRequestVisitor__c>(
            hm_DTO.hotelRequestVisitorsToSObjects(hotelRequestDto.visitors)
        );

        for (Integer i = 0; i < rooms.size(); i++) {
            hm_HotelRequestRoom__c room = rooms.get(i);
            hm_DTO.HotelRequestRoom roomDto = hotelRequestDto.rooms.get(i);
            for (hm_DTO.HotelRequestVisitor visitorDto : roomDto.visitors) {
                hm_HotelRequestVisitor__c visitor = idToVisitor.get(visitorDto.id);
                visitor.RoomPlaceNumber__c = visitorDto.roomPlace;
                visitor.HotelRequestRoom__c = room.Id;
            }
        }
        Database.update(idToVisitor.values(), AccessLevel.USER_MODE);

        List<SObject> recordsForDelete = new List<SObject>();
        if (hotelRequestDto.removedRooms != null) {
            recordsForDelete.addAll(hm_DTO.hotelRequestRoomsToSObjects(hotelRequestDto.removedRooms));
        }
        Database.delete(recordsForDelete, AccessLevel.USER_MODE);
    }

    public static Map<String, List<hm_HotelRequestRoom__c>> groupRoomsByType(List<hm_HotelRequestRoom__c> rooms) {
        Map<String, List<hm_HotelRequestRoom__c>> typeToRooms = new Map<String, List<hm_HotelRequestRoom__c>>();

        for (hm_HotelRequestRoom__c room : rooms) {
            if (!typeToRooms.containsKey(room.Type__c)) {
                typeToRooms.put(room.Type__c, new List<hm_HotelRequestRoom__c>{ room });
                continue;
            }
            typeToRooms.get(room.Type__c).add(room);
        }

        return typeToRooms;
    }

    private static void copyHotelRequestDatesToNewRoomDates(Id hotelRequestId, List<hm_HotelRequestRoom__c> rooms) {
        Set<String> hotelRequestFields = new Set<String>{
            String.valueOf(hm_HotelRequest__c.CheckInDate__c),
            String.valueOf(hm_HotelRequest__c.CheckOutDate__c)
        };
        hm_HotelRequest__c hotelRequest = hm_Selector.getHotelRequestById(hotelRequestFields, hotelRequestId);

        for (hm_HotelRequestRoom__c room : rooms) {
            if (room.Id == null) {
                room.CheckInDate__c = hotelRequest.CheckInDate__c;
                room.CheckOutDate__c = hotelRequest.CheckOutDate__c;
            }
        }
    }

    private static Id getHotelRequestAccountId() {
        User currentUser = hm_Selector.getCurrentUser();

        if (String.isNotBlank(currentUser.AccountId)) {
            return currentUser.AccountId;
        }

        return hm_HotelManagementSettings__c.getInstance().DefaultAccountId__c;
    }
}