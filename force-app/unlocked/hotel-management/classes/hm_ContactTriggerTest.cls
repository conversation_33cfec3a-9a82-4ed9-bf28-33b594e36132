@IsTest
class hm_ContactTriggerTest {
    static final String DEFAULT_ACCOUNT_NAME = 'Default Account';
    static final Integer SETUP_VOLUNTEER_COUNT = 5;
    static final String VOLUNTEER_ROLE = PortalUser.ROLES.get(PortalUser.Role.VOLUNTEER);
    static final String STATE_OFFICER_ROLE = PortalUser.ROLES.get(PortalUser.Role.STATE_OFFICER);

    @TestSetup
    static void setup() {
        User internalUser;
        System.runAs(new User(Id = UserInfo.getUserId())) {
            internalUser = hm_TestDataFactory.createInternalUser();
            insert internalUser;
            hm_TestDataFactory.assignHotelManagementPermissionSet(internalUser.Id);
        }

        System.runAs(internalUser) {
            List<SObject> recordsForInsert = new List<SObject>();
            hm_Hotel__c hotel = hm_TestDataFactory.createHotels(1).get(0);
            recordsForInsert.add(hotel);

            Account defaultAcc = hm_TestDataFactory.createAccounts(1).get(0);
            defaultAcc.Name = DEFAULT_ACCOUNT_NAME;
            recordsForInsert.add(defaultAcc);

            hm_HotelRequest__c eventMockingRequest = hm_TestDataFactory.createHotelRequests(1).get(0);
            eventMockingRequest.CheckInDate__c = Date.today().addDays(-2);
            eventMockingRequest.CheckOutDate__c = Date.today().addDays(15);
            recordsForInsert.add(eventMockingRequest);
            insert recordsForInsert;

            hm_HotelRequest__c hotelRequest = hm_TestDataFactory.createHotelRequestsWithEventId(
                    1,
                    eventMockingRequest.Id
                )
                .get(0);
            hotelRequest.Account__c = defaultAcc.Id;

            insert hotelRequest;

            List<Contact> volunteerContacts = hm_TestDataFactory.createContacts(SETUP_VOLUNTEER_COUNT);
            for (Contact volunteerContact : volunteerContacts) {
                volunteerContact.PortalRole__c = VOLUNTEER_ROLE;
                volunteerContact.hm_IsVolunteerNeedsHotel__c = true;
            }
            insert volunteerContacts;

            insert hm_TestDataFactory.createSettings(defaultAcc.Id, eventMockingRequest.Id);
        }
    }

    @IsTest
    static void afterInsert() {
        hm_HotelRequest__c eventMockingRequest = getEventMockingRequest();
        Account defaultAcc = [SELECT Id FROM Account WHERE Name = :DEFAULT_ACCOUNT_NAME];
        Integer officerContactCount = 7;
        List<Contact> officerContacts = hm_TestDataFactory.createContacts(officerContactCount);

        for (Contact officerContact : officerContacts) {
            officerContact.PortalRole__c = STATE_OFFICER_ROLE;
        }

        Test.startTest();
        System.runAs(hm_TestDataFactory.getInternalUser()) {
            insert officerContacts;
        }
        Test.stopTest();

        Integer visitorCountAfterInsert = [
            SELECT COUNT()
            FROM hm_HotelRequestVisitor__c
            WHERE
                Contact__c IN :officerContacts
                AND HotelRequest__r.Account__c = :defaultAcc.Id
                AND HotelRequest__r.EventId__c = :eventMockingRequest.Id
        ];

        Assert.areEqual(
            officerContacts.size(),
            visitorCountAfterInsert,
            'Contact trigger should automatically create hotel request visitors for Contacts where PortalRole__c = ' +
            STATE_OFFICER_ROLE
        );
    }

    @IsTest
    static void afterUpdate() {
        hm_HotelRequest__c eventMockingRequest = getEventMockingRequest();
        Account defaultAcc = [SELECT Id FROM Account WHERE Name = :DEFAULT_ACCOUNT_NAME];
        List<Contact> volunteerContacts = [SELECT Id FROM Contact WHERE PortalRole__c INCLUDES (:VOLUNTEER_ROLE)];
        Assert.areEqual(SETUP_VOLUNTEER_COUNT, volunteerContacts.size(), 'Setup method failed to create contacts');

        for (Contact volunteerContact : volunteerContacts) {
            volunteerContact.hm_IsVolunteerApproved__c = true;
        }

        Test.startTest();
        System.runAs(hm_TestDataFactory.getInternalUser()) {
            update volunteerContacts;
        }
        Test.stopTest();

        Integer visitorCountAfterApprove = [
            SELECT COUNT()
            FROM hm_HotelRequestVisitor__c
            WHERE
                Contact__c IN :volunteerContacts
                AND HotelRequest__r.Account__c = :defaultAcc.Id
                AND HotelRequest__r.EventId__c = :eventMockingRequest.Id
        ];

        Assert.areEqual(
            volunteerContacts.size(),
            visitorCountAfterApprove,
            'Contact trigger should automatically create visitors for default account\'s hotel request ' +
            'once volunteer contacts has been approved'
        );
    }

    static hm_HotelRequest__c getEventMockingRequest() {
        return [SELECT CheckInDate__c, CheckOutDate__c FROM hm_HotelRequest__c WHERE EventId__c = NULL];
    }
}