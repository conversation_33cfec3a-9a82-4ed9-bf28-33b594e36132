@IsTest
class hm_HotelRequestVisitorTriggerTest {
    @TestSetup
    static void setup() {
        User internalUser;
        System.runAs(new User(Id = UserInfo.getUserId())) {
            internalUser = hm_TestDataFactory.createInternalUser();
            insert internalUser;
            hm_TestDataFactory.assignHotelManagementPermissionSet(internalUser.Id);
        }

        System.runAs(internalUser) {
            Account visitorAccount = hm_TestDataFactory.createAccounts(1).get(0);
            insert visitorAccount;

            hm_HotelRequest__c hotelRequest = hm_TestDataFactory.createHotelRequests(1).get(0);
            hotelRequest.Account__c = visitorAccount.Id;
            insert hotelRequest;

            List<Contact> visitorContacts = hm_TestDataFactory.createContacts(2);
            insert visitorContacts;
        }
    }

    @IsTest
    static void insertDuplicateVisitorsInSeparateTransactions() {
        hm_HotelRequest__c hotelRequest = [SELECT Id FROM hm_HotelRequest__c LIMIT 1];
        List<Contact> visitorContacts = [SELECT Id FROM Contact];

        List<hm_HotelRequestVisitor__c> visitors = new List<hm_HotelRequestVisitor__c>();
        List<hm_HotelRequestVisitor__c> duplicateVisitors = new List<hm_HotelRequestVisitor__c>();

        for (Contact visitorContact : visitorContacts) {
            hm_HotelRequestVisitor__c visitor = new hm_HotelRequestVisitor__c(
                HotelRequest__c = hotelRequest.Id,
                Contact__c = visitorContact.Id
            );
            hm_HotelRequestVisitor__c duplicateVisitor = visitor.clone();

            visitors.add(visitor);
            duplicateVisitors.add(duplicateVisitor);
        }

        Test.startTest();
        System.runAs(hm_TestDataFactory.getInternalUser()) {
            insert visitors;

            try {
                insert duplicateVisitors;
                Assert.fail('Creation of duplicate hotel request visitor should fail');
            } catch (Exception e) {
                Assert.isTrue(
                    e.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION'),
                    'Custom validation exception should be thrown for duplicate visitor'
                );
            }
        }
        Test.stopTest();
    }

    @IsTest
    static void insertDuplicateVisitorsInSingleTransaction() {
        hm_HotelRequest__c hotelRequest = [SELECT Id FROM hm_HotelRequest__c LIMIT 1];
        List<Contact> visitorContacts = [SELECT Id FROM Contact];

        List<hm_HotelRequestVisitor__c> visitors = new List<hm_HotelRequestVisitor__c>();

        for (Contact visitorContact : visitorContacts) {
            hm_HotelRequestVisitor__c visitor = new hm_HotelRequestVisitor__c(
                HotelRequest__c = hotelRequest.Id,
                Contact__c = visitorContact.Id
            );
            hm_HotelRequestVisitor__c duplicateVisitor = visitor.clone();
            visitors.add(visitor);
            visitors.add(duplicateVisitor);
        }

        Test.startTest();
        System.runAs(hm_TestDataFactory.getInternalUser()) {
            try {
                insert visitors;
                Assert.fail('Creation of duplicate hotel request visitor should fail');
            } catch (Exception e) {
                Assert.isTrue(
                    e.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION'),
                    'Custom validation exception should be thrown for duplicate visitor'
                );
            }
        }
        Test.stopTest();
    }

    @IsTest
    static void updateVisitorRooms() {
        hm_HotelRequest__c hotelRequest = [SELECT CheckInDate__c, CheckOutDate__c FROM hm_HotelRequest__c LIMIT 1];
        User internalUser = hm_TestDataFactory.getInternalUser();

        List<hm_HotelRequestRoom__c> requestRooms;
        List<hm_HotelRequestVisitor__c> visitors;
        List<String> roomTypes = new List<String>(hm_RoomTypeService.getNameToRoomTypeDto().keySet());

        System.runAs(internalUser) {
            requestRooms = hm_TestDataFactory.createHotelRequestRooms(2, hotelRequest, roomTypes.get(0));
            insert requestRooms;

            List<Contact> visitorContacts = hm_TestDataFactory.createContacts(3);
            visitors = hm_TestDataFactory.createHotelRequestGuestVisitors(hotelRequest.Id, visitorContacts);
            //placing first two visitors in the first room
            visitors.get(0).HotelRequestRoom__c = requestRooms.get(0).Id;
            visitors.get(1).HotelRequestRoom__c = requestRooms.get(0).Id;

            insert visitors;
        }

        Boolean noExceptionThrown = true;

        Test.startTest();
        System.runAs(internalUser) {
            //placing third visitor in the first room, moving second visitor to second room which was empty previously
            visitors.get(1).HotelRequestRoom__c = requestRooms.get(1).Id;
            visitors.get(2).HotelRequestRoom__c = requestRooms.get(0).Id;

            try {
                update new List<hm_HotelRequestVisitor__c>{ visitors.get(1), visitors.get(2) };
            } catch (Exception e) {
                noExceptionThrown = false;
            }
        }
        Test.stopTest();

        Assert.isTrue(
            noExceptionThrown,
            'Trigger should not fail when changing rooms of two visitors in single transaction'
        );
    }
}