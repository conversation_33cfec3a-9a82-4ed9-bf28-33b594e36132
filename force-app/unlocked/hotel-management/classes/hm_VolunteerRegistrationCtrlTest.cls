@IsTest
class hm_VolunteerRegistrationCtrlTest {
    static final String EXTERNAL_USER_EMAIL = '<EMAIL>';
    static final String NOTIFICATION_GROUP_NAME = 'hm_VolunteerApprovers';
    static final Date EVENT_START_DATE = Date.today().addDays(-5);
    static final Date EVENT_END_DATE = Date.today().addDays(5);

    @TestSetup
    static void setup() {
        User adminUser = hm_TestDataFactory.createAdminUser();
        insert adminUser;

        System.runAs(adminUser) {
            insert hm_TestDataFactory.createCoreSettings();

            hm_HotelRequest__c eventMockingRequest = hm_TestDataFactory.createHotelRequests(1).get(0);
            eventMockingRequest.CheckInDate__c = EVENT_START_DATE;
            eventMockingRequest.CheckOutDate__c = EVENT_END_DATE;
            insert eventMockingRequest;

            Account externalAccount = hm_TestDataFactory.createAccounts(1).get(0);
            insert externalAccount;

            Contact externalContact = hm_TestDataFactory.createContacts(1).get(0);
            externalContact.AccountId = externalAccount.Id;
            externalContact.Email = EXTERNAL_USER_EMAIL;
            insert externalContact;

            User externalUser = hm_TestDataFactory.createExternalUser(externalContact);
            User unprivilegedUser = hm_TestDataFactory.createUnprivilegedUser();
            insert new List<User>{ externalUser, unprivilegedUser };

            Group notificationGroup = [SELECT Id FROM Group WHERE DeveloperName = :NOTIFICATION_GROUP_NAME];
            hm_HotelManagementSettings__c settings = hm_TestDataFactory.createSettings(
                externalAccount.Id,
                eventMockingRequest.Id
            );
            settings.VolunteerApproversGroupId__c = notificationGroup.Id;
            //resetting default value because there is no layouts in package
            settings.VolunteerRegistrationLayoutName__c = null;
            insert settings;

            insert new GroupMember(UserOrGroupId = adminUser.Id, GroupId = notificationGroup.Id);
        }
    }

    @IsTest
    static void getFormFail() {
        Test.startTest();
        System.runAs(hm_TestDataFactory.getUser(EXTERNAL_USER_EMAIL)) {
            try {
                hm_VolunteerRegistrationCtrl.getForm();
                Assert.fail('getForm() expected to fail because there is no specified layout in settings');
            } catch (Exception e) {
                Assert.isNotNull(e.getMessage(), 'Exception message cannot be null');
            }
        }
        Test.stopTest();
    }

    @IsTest
    static void saveRecordSuccess() {
        aclab.FormLayout formLayout;
        Contact volunteerContact = hm_TestDataFactory.createContacts(1).get(0);
        volunteerContact.FirstName = 'Volunteer Name';
        volunteerContact.LastName = 'Volunteer LastName';

        aclab.GoogleReCaptchaService.IS_CAPTCHA_DISABLED = true;

        Test.startTest();
        System.runAs(hm_TestDataFactory.getUser(EXTERNAL_USER_EMAIL)) {
            hm_VolunteerRegistrationCtrl.saveRecord(volunteerContact, 'testtoken');
        }
        Test.stopTest();

        Integer contactCountAfterSave = [
            SELECT COUNT()
            FROM Contact
            WHERE FirstName = :volunteerContact.FirstName AND LastName = :volunteerContact.LastName
        ];

        Assert.areEqual(1, contactCountAfterSave, 'saveRecord() method failed to create new contact');
    }

    @IsTest
    static void saveRecordValidationFail() {
        aclab.FormLayout formLayout;
        Contact volunteerContact = hm_TestDataFactory.createContacts(1).get(0);
        volunteerContact.FirstName = 'Volunteer Name';
        volunteerContact.LastName = 'Volunteer LastName';
        volunteerContact.hm_VolunteerCheckInDate__c = EVENT_START_DATE.addDays(-1);
        volunteerContact.hm_VolunteerCheckOutDate__c = EVENT_END_DATE.addDays(1);

        aclab.GoogleReCaptchaService.IS_CAPTCHA_DISABLED = true;

        Test.startTest();
        System.runAs(hm_TestDataFactory.getUser(EXTERNAL_USER_EMAIL)) {
            try {
                hm_VolunteerRegistrationCtrl.saveRecord(volunteerContact, 'testtoken');
                Assert.fail('Volunteer save should fail because of dates');
            } catch (AuraHandledException e) {
                Assert.isNotNull(e.getMessage(), 'Exception message cannot be null');
            }
        }
        Test.stopTest();
    }

    @IsTest
    static void saveRecordFlsFail() {
        aclab.FormLayout formLayout;
        Contact volunteerContact = hm_TestDataFactory.createContacts(1).get(0);

        aclab.GoogleReCaptchaService.IS_CAPTCHA_DISABLED = true;

        Test.startTest();
        System.runAs(hm_TestDataFactory.getUnprivilegedUser()) {
            try {
                hm_VolunteerRegistrationCtrl.saveRecord(volunteerContact, 'testtoken');
                Assert.fail('saveRecord() should fail if called by unprivileged (chatter only) user');
            } catch (Exception e) {
                Assert.isNotNull(e.getMessage(), 'Exception message cannot be null');
            }
        }
        Test.stopTest();
    }
}