public with sharing class hm_Selector {
    public static List<hm_Hotel__c> getAvailableHotels() {
        return [
            SELECT Name, IsInWalkingDistanceFromEvent__c, DistanceToConferenceHall__c, Address__c, Site__c, MapLink__c
            FROM hm_Hotel__c
            WHERE IsAvailable__c = TRUE
            WITH USER_MODE
            ORDER BY Name ASC
        ];
    }

    public static List<hm_HotelRequestPreferredHotel__c> getPreferredHotels(Id hotelRequestId) {
        return [
            SELECT
                HotelRequest__c,
                PriorityOrder__c,
                Hotel__r.Name,
                Hotel__r.IsInWalkingDistanceFromEvent__c,
                Hotel__r.DistanceToConferenceHall__c,
                Hotel__r.Address__c,
                Hotel__r.Site__c,
                Hotel__r.MapLink__c
            FROM hm_HotelRequestPreferredHotel__c
            WHERE HotelRequest__c = :hotelRequestId
            WITH USER_MODE
            ORDER BY PriorityOrder__c ASC
        ];
    }

    public static List<hm_HotelRequestPreferredHotel__c> getRemainingPreferredHotels(
        Id hotelRequestId,
        Set<Id> excludeIds
    ) {
        return [
            SELECT Id
            FROM hm_HotelRequestPreferredHotel__c
            WHERE HotelRequest__c = :hotelRequestId AND (NOT Id IN :excludeIds)
            WITH USER_MODE
        ];
    }

    public static hm_HotelRequest__c getHotelRequestWithRoomVisitors(Id hotelRequestId) {
        return [
            SELECT
                CheckInDate__c,
                CheckOutDate__c,
                ReadOnly__c,
                (
                    SELECT
                        FirstName__c,
                        LastName__c,
                        HotelRequestRoom__c,
                        Contact__r.PortalRole__c,
                        RoomPlaceNumber__c,
                        Gender__c
                    FROM HotelRequestVisitors__r
                ),
                (SELECT CheckInDate__c, CheckOutDate__c, Type__c, HotelRequest__c FROM HotelRequestRooms__r)
            FROM hm_HotelRequest__c
            WHERE Id = :hotelRequestId
            WITH USER_MODE
        ];
    }

    public static hm_HotelRequestVisitor__c getHotelRequestVisitor(Id visitorId) {
        return [
            SELECT
                FirstName__c,
                LastName__c,
                HotelRequestRoom__c,
                Contact__r.PortalRole__c,
                RoomPlaceNumber__c,
                Gender__c
            FROM hm_HotelRequestVisitor__c
            WHERE Id = :visitorId
            WITH USER_MODE
        ];
    }

    public static Integer getAvailableHotelCount() {
        return [SELECT COUNT() FROM hm_Hotel__c WHERE IsAvailable__c = TRUE WITH USER_MODE];
    }

    public static Integer getEventGuestsCount(String eventId) {
        return [
            SELECT COUNT()
            FROM hm_HotelRequestVisitor__c
            WHERE
                HotelRequest__r.EventId__c = :eventId
                AND Contact__r.Account.hm_IsExemptedFromHotelAssignment__c = FALSE
            WITH USER_MODE
        ];
    }

    public static List<hm_HotelRequest__c> getHotelRequestsForEventSummary(String eventId) {
        return [
            SELECT
                Account__c,
                (
                    SELECT Type__c
                    FROM HotelRequestRooms__r
                )
            FROM hm_HotelRequest__c
            WHERE EventId__c = :eventId AND Account__r.hm_IsExemptedFromHotelAssignment__c = FALSE
            WITH USER_MODE
        ];
    }

    public static List<hm_HotelRequest__c> getHotelRequestsForTetris(String eventId) {
        return [
            SELECT
                Name,
                EventId__c,
                Account__r.Name,
                Account__r.BillingStreet,
                Account__r.BillingCity,
                Account__r.BillingState,
                Account__r.BillingPostalCode,
                Account__r.BillingCountry,
                Account__r.Phone,
                EnforcedHotel__r.Name,
                OnSiteTransportation__c,
                (SELECT Hotel__c, PriorityOrder__c FROM HotelRequestPreferredHotels__r),
                (
                    SELECT
                        CheckInDate__c,
                        CheckOutDate__c,
                        Type__c,
                        (
                            SELECT
                                FirstName__c,
                                LastName__c,
                                Email__c,
                                Phone__c,
                                Position__c,
                                HotelRequestRoom__c,
                                HotelRequest__c
                            FROM HotelRequestVisitors__r
                        )
                    FROM HotelRequestRooms__r
                )
            FROM hm_HotelRequest__c
            WHERE EventId__c = :eventId AND Account__r.hm_IsExemptedFromHotelAssignment__c = FALSE
            WITH USER_MODE
        ];
    }

    public static List<hm_Hotel__c> getHotelsForTetris(String eventId) {
        return [
            SELECT
                Name,
                IsAvailable__c,
                IsInWalkingDistanceFromEvent__c,
                (SELECT Type__c, Subtype__c, NumberOfRooms__c FROM HotelRoomTypes__r ORDER BY Fee__c)
            FROM hm_Hotel__c
            WHERE IsAvailable__c = TRUE
            WITH USER_MODE
        ];
    }

    public static hm_HotelTetrisResult__c getActiveHotelTetrisResult() {
        hm_HotelTetrisResult__c tetrisResult;
        List<hm_HotelTetrisResult__c> tetrisResults = [
            SELECT LastSentToAllHotelManagers__c, EventId__c
            FROM hm_HotelTetrisResult__c
            WHERE IsActive__c = TRUE
            WITH USER_MODE
            ORDER BY CreatedDate DESC
            LIMIT 1
        ];
        if (!tetrisResults.isEmpty()) {
            tetrisResult = tetrisResults.get(0);
        }
        return tetrisResult;
    }

    public static List<hm_HotelTetrisResultGroup__c> getTetrisResultGroupsByResultId(Id tetrisResultId) {
        return [
            SELECT
                Hotel__r.Name,
                HotelEmail__c,
                LastSentToHotelManager__c,
                LastUpdatedByHotelManagerDate__c,
                (
                    SELECT HotelManagementStatus__c, LastModifiedDate, LastModifiedBy.Profile.UserType, Guest1Company__c
                    FROM HotelRoomReservationLines__r
                )
            FROM hm_HotelTetrisResultGroup__c
            WHERE HotelTetrisResult__c = :tetrisResultId
            WITH USER_MODE
        ];
    }

    public static List<hm_HotelTetrisResultGroup__c> getTetrisResultGroups(List<Id> tetrisGroupIds) {
        return [
            SELECT HotelEmail__c, QueryId__c, Passcode__c, Hotel__c
            FROM hm_HotelTetrisResultGroup__c
            WHERE Id IN :tetrisGroupIds
            WITH USER_MODE
        ];
    }

    public static EmailTemplate getEmailTemplate(String templateName) {
        return [
            SELECT HtmlValue, Subject
            FROM EmailTemplate
            WHERE DeveloperName = :templateName
        ];
    }

    public static User getCurrentUser() {
        return [SELECT AccountId FROM User WHERE Id = :UserInfo.getUserId() WITH USER_MODE];
    }

    public static hm_HotelRequest__c getHotelRequestByEventIdAccountId(String eventId, Id accountId) {
        hm_HotelRequest__c hotelRequest;
        List<hm_HotelRequest__c> hotelRequests = [
            SELECT Id
            FROM hm_HotelRequest__c
            WHERE EventId__c = :eventId AND Account__c = :accountId
            WITH USER_MODE
            LIMIT 1
        ];
        if (!hotelRequests.isEmpty()) {
            hotelRequest = hotelRequests.get(0);
        }
        return hotelRequest;
    }

    public static hm_HotelRequest__c getHotelRequestByEventIdAccountId(
        Set<String> fields,
        String eventId,
        Id accountId
    ) {
        String queryFields = String.join(fields, ', ');

        String query =
            'SELECT ' +
            queryFields +
            ' FROM hm_HotelRequest__c' +
            ' WHERE EventId__c = :eventId AND Account__c = :accountId' +
            ' LIMIT 1';

        hm_HotelRequest__c hotelRequest;
        List<hm_HotelRequest__c> hotelRequests = Database.query(query, AccessLevel.USER_MODE); //NOPMD

        if (!hotelRequests.isEmpty()) {
            hotelRequest = hotelRequests.get(0);
        }

        return hotelRequest;
    }

    public static hm_HotelRequest__c getHotelRequestById(Set<String> fields, Id hotelRequestId) {
        String queryFields = String.join(fields, ', ');

        String query =
            'SELECT ' +
            queryFields +
            ' FROM hm_HotelRequest__c' +
            ' WHERE Id = :hotelRequestId' +
            ' LIMIT 1';

        hm_HotelRequest__c hotelRequest;
        List<hm_HotelRequest__c> hotelRequests = Database.query(query, AccessLevel.USER_MODE); //NOPMD

        if (!hotelRequests.isEmpty()) {
            hotelRequest = hotelRequests.get(0);
        }

        return hotelRequest;
    }

    public static List<SObject> getEventsById(List<Id> eventIds, Set<String> fields, System.AccessLevel accessMode) {
        Schema.SObjectType eventType = eventIds.get(0).getSObjectType();
        String queryFields = String.escapeSingleQuotes(String.join(fields, ', '));
        String queryEventIds = '\'' + String.join(eventIds, '\',\'') + '\'';
        String query = String.format(
            'SELECT {0} FROM {1} WHERE Id IN (' + queryEventIds + ')',
            new List<Object>{ queryFields, String.valueOf(eventType) }
        );

        if (accessMode == AccessLevel.SYSTEM_MODE) {
            return WS.retrieveRecords(query);
        }

        return Database.query(query, AccessLevel.USER_MODE);
    }

    public static List<hm_HotelTetrisResultGroup__c> getTetrisGroupsWithReservationLines(
        Set<Id> groupIds,
        Set<Id> excludeLineIds
    ) {
        return [
            SELECT
                HotelTetrisResult__r.EventId__c,
                Hotel__c,
                (
                    SELECT CheckInDate__c, CheckOutDate__c, Capacity__c, RoomType__c
                    FROM HotelRoomReservationLines__r
                    WHERE NOT Id IN :excludeLineIds
                    ORDER BY CheckInDate__c
                )
            FROM hm_HotelTetrisResultGroup__c
            WHERE Id IN :groupIds
            WITH USER_MODE
        ];
    }

    public static CustomNotificationType getNotificationType() {
        return [
            SELECT Id, DeveloperName
            FROM CustomNotificationType
            WHERE DeveloperName = :hm_Constant.CUSTOM_NOTIFICATION_TYPE
        ];
    }

    public static List<GroupMember> getGroupMembersWs(Set<Id> groupIds) {
        String query = 'SELECT GroupId, TYPEOF UserOrGroup WHEN User THEN Name, Email END FROM GroupMember WHERE GroupId IN :groupIds';
        Map<String, Object> queryBinds = new Map<String, Object>{ 'groupIds' => groupIds };
        return (List<GroupMember>) WS.retrieveRecords(query, queryBinds);
    }

    public static List<User> getGroupMemberUsersWs(Id groupId) {
        String query =
            'SELECT Name, Email FROM User WHERE Id IN (SELECT UserOrGroupId FROM GroupMember WHERE GroupId = \'' +
            groupId +
            '\')';
        return (List<User>) WS.retrieveRecords(query);
    }

    public static List<hm_HotelRoomType__c> getHotelRoomTypes(Set<Id> roomTypeIds) {
        return [
            SELECT Type__c, Subtype__c, NumberOfRooms__c
            FROM hm_HotelRoomType__c
            WHERE Id IN :roomTypeIds
            WITH USER_MODE
        ];
    }
}