public with sharing class hm_HotelTetrisCtrl {
    @AuraEnabled
    public static hm_DTO.HotelTetrisData getDataForHotelTetris(String eventId) {
        try {
            if (eventId == null) {
                eventId = hm_EventService.getActiveEventId();
            }
            return hm_HotelTetrisService.getDataForHotelTetris(eventId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static Id saveTetrisResult(Id eventId, hm_DTO.HotelTetrisResult allocationResult) {
        try {
            if (eventId == null) {
                eventId = hm_EventService.getActiveEventId();
            }
            hm_HotelTetrisResult__c savedTetrisResult = hm_HotelTetrisService.saveTetrisResult(
                eventId,
                allocationResult
            );
            return savedTetrisResult.Id;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static List<hm_DTO.RoomType> getRoomTypesSettings() {
        try {
            return hm_RoomTypeService.getNameToRoomTypeDto().values();
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}