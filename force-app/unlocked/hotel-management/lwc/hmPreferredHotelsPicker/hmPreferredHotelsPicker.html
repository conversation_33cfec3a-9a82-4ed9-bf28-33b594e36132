<template>
    <lightning-spinner if:true={isSpinnerShown}></lightning-spinner>
    <label class="slds-form-element__label" for="preferred-hotels-container">{labels.hmHotelPriorityLabel}</label>
    <lightning-helptext content={labels.hmHotelPriorityHelpText}></lightning-helptext>
    <article id="preferred-hotels-container"
        class="slds-card slds-card_boundary slds-p-around_x-small slds-scrollable_y slds-m-bottom_x-small hm-max-height-16">
        <template for:each={preferredHotels} for:item="prefHotel" for:index="index">
            <template if:true={prefHotel.hotelId}>
                <article key={prefHotel.hotelId} data-hotel-id={prefHotel.hotelId} data-slot-index={index}
                    ondragover={handleDropZoneDragOver} ondrop={handlePreferredHotelSlotDrop}
                    ondragleave={handleDropZoneDragLeave} draggable="true" ondragstart={handlePrefHotelDragStart}
                    ondragend={handlePrefHotelDragEnd} onclick={handlePreferredHotelClick}
                    class="slds-card slds-card_boundary slds-p-around_x-small hm-filled-drop-slot hm-dropable">
                    <div class="slds-grid slds-p-left_small slds-p-bottom_x-small">
                        <header class="slds-media slds-media_center slds-has-flexi-truncate">
                            <div class="slds-media__body slds-text-title_bold">{prefHotel.hotelName}</div>
                            <div class="slds-no-flex" if:true={prefHotel.site}>
                                <a href={prefHotel.site} target="_blank">
                                    <lightning-button-icon size="small" icon-name="utility:link">
                                    </lightning-button-icon>
                                </a>
                            </div>
                        </header>
                    </div>
                    <div class="slds-grid slds-wrap slds-grid_vertical-align-center">
                        <div class="slds-col slds-text-color_weak slds-grow-none" if:true={prefHotel.address}>
                            <lightning-icon icon-name="utility:checkin" size="xx-small"> </lightning-icon>
                            <a href={prefHotel.mapLink} target="_blank"
                                class="slds-p-top_xx-small slds-p-left_xx-small">
                                {prefHotel.address}
                            </a>
                        </div>
                        <div class="slds-col slds-text-color_weak slds-grow-none" if:true={prefHotel.confHallDistance}>
                            <lightning-icon icon-name="utility:tour" size="xx-small"> </lightning-icon>
                            <span class="slds-p-top_xx-small slds-p-left_xx-small">{prefHotel.confHallDistance}</span>
                        </div>
                    </div>
                </article>
            </template>
            <!-- there might be placeholder for preferred hotel so displaying it separately -->
            <template if:false={prefHotel.hotelId}>
                <article key={prefHotel.key} data-slot-index={index} ondragover={handleDropZoneDragOver}
                    ondrop={handlePreferredHotelSlotDrop} ondragleave={handleDropZoneDragLeave}
                    onclick={handlePreferredSlotClick}
                    class="slds-card slds-card_boundary slds-p-around_x-small hm-drop-slot hm-drop-slot-empty hm-min-height-3p5">
                    <p class="slds-text-body_regular slds-p-left_small">{prefHotel.label}</p>
                </article>
            </template>
        </template>
    </article>
    <label class="slds-form-element__label" for="preferable-hotels-container">{labels.hmRequestAvailableHotels}</label>
    <article id="preferable-hotels-container"
        class="slds-card slds-card_boundary slds-p-around_x-small slds-scrollable_y slds-m-bottom_x-small hm-max-height-16"
        ondragover={handleDropZoneDragOver} ondrop={handleAvailableHotelsDrop} ondragleave={handleDropZoneDragLeave}
        onclick={handleAvailableHotelsListClick}>
        <template for:each={preferableHotels} for:item="hotel">
            <article key={hotel.hotelId} data-hotel-id={hotel.hotelId}
                class="slds-card slds-card_boundary slds-p-around_x-small hm-preferable-hotel hm-dropable"
                draggable="true" ondragstart={handlePrefHotelDragStart} ondragend={handlePrefHotelDragEnd}
                onclick={handlePreferableHotelClick}>
                <div class="slds-grid slds-p-left_small slds-p-bottom_x-small">
                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                        <div class="slds-media__body slds-text-title_bold">{hotel.hotelName}</div>
                        <div class="slds-no-flex" if:true={hotel.site}>
                            <a href={hotel.site} target="_blank">
                                <lightning-button-icon size="small" icon-name="utility:link">
                                </lightning-button-icon>
                            </a>
                        </div>
                    </header>
                </div>
                <div class="slds-grid slds-wrap slds-grid_vertical-align-center">
                    <div class="slds-col slds-text-color_weak slds-grow-none" if:true={hotel.address}>
                        <lightning-icon icon-name="utility:checkin" size="xx-small"> </lightning-icon>
                        <a href={hotel.mapLink} target="_blank" class="slds-p-top_xx-small slds-p-left_xx-small">
                            {hotel.address}
                        </a>
                    </div>
                    <div class="slds-col slds-text-color_weak slds-grow-none" if:true={hotel.confHallDistance}>
                        <lightning-icon icon-name="utility:tour" size="xx-small"> </lightning-icon>
                        <span class="slds-p-top_xx-small slds-p-left_xx-small">{hotel.confHallDistance}</span>
                    </div>
                </div>
            </article>
        </template>
    </article>
</template>