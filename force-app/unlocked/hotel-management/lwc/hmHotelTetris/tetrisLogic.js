import { ObjectHelper } from './helpers';
import { RoomTypeStorage } from './models';
const MAX_RETRIES = 3;
const MAX_RECURSION_DEPTH_FOR_RETRY = 1;
const MAX_RECURSION_DEPTH_FOR_UPGRADES = 1;

export function allocateRequestsByCapacity(hotelsOrigin, requestsOrigin, recursionDepth = 1) {
    const { requests, hotels } = cloneTetrisResult(hotelsOrigin, requestsOrigin);
    let fallbackResult;

    for (const request of requests) {
        if (request.selectedHotelId) {
            continue;
        }

        const diffToHotel = hotels.reduce((diffMap, h) => {
            const diff = h.getRequestCapacityDiff(request);
            if (diff != null) {
                diffMap[diff] = h;
            }
            return diffMap;
        }, {});

        if (!Object.keys(diffToHotel).length) {
            if (recursionDepth <= MAX_RECURSION_DEPTH_FOR_UPGRADES && RoomTypeStorage.isUpgradesAvailable) {
                allocateRequestWithRoomUpgrades(request, hotels);
            }
            continue;
        }

        const sortedDiffs = Object.keys(diffToHotel).sort((a, b) => a - b);

        if (recursionDepth > MAX_RECURSION_DEPTH_FOR_RETRY) {
            const hotel = diffToHotel[sortedDiffs[0]];
            hotel.allocateRequest(request);
            continue;
        }

        const retryCount = sortedDiffs.length > MAX_RETRIES ? MAX_RETRIES : sortedDiffs.length;

        for (let i = 0; i < retryCount; i++) {
            const hotel = diffToHotel[sortedDiffs[i]];
            hotel.allocateRequest(request);
            const furtherAllocationResult = allocateRequestsByCapacity(hotels, requests, recursionDepth + 1);

            if (furtherAllocationResult.isCompleted) {
                return furtherAllocationResult;
            }

            const currentAllocatedRequestCount = fallbackResult?.requests?.filter((r) => r.selectedHotelId)?.length;
            const furtherAllocatedRequestCount = furtherAllocationResult.requests.filter(
                (r) => r.selectedHotelId
            ).length;
            const isLastDiff = i === retryCount - 1;

            if (!isLastDiff) {
                hotel.removeLastRequest();
            }

            fallbackResult =
                currentAllocatedRequestCount > furtherAllocatedRequestCount ? fallbackResult : furtherAllocationResult;
        }
    }

    const isCompleted = !requests.some((r) => !r.selectedHotelId);

    return !isCompleted && fallbackResult ? fallbackResult : { hotels, requests, isCompleted };
}

export function allocateRequestsByPreference(hotelsOrigin, requestsOrigin) {
    const { requests, hotels } = cloneTetrisResult(hotelsOrigin, requestsOrigin);

    const idToHotel = hotels.reduce((map, h) => ({ ...map, [h.id]: h }), {});
    let capacityAllocationResult = allocateRequestsByCapacity(hotelsOrigin, requestsOrigin);

    if (!capacityAllocationResult.isCompleted) {
        return capacityAllocationResult;
    }

    /*eslint no-labels: ["error", { "allowLoop": true }]*/
    requestsLoop: for (const request of requests) {
        if (request.selectedHotelId) {
            continue;
        }

        if (request.hotelPrefs?.length) {
            request.hotelPrefs.sort((a, b) => a.order - b.order);

            for (const hotelPref of request.hotelPrefs) {
                const hotel = idToHotel[hotelPref.hotelId];

                if (!hotel?.canAllocateRequest(request)) {
                    continue;
                }

                hotel.allocateRequest(request);

                capacityAllocationResult = allocateRequestsByCapacity(hotels, requests);
                if (capacityAllocationResult.isCompleted) {
                    continue requestsLoop;
                }

                hotel.removeLastRequest();
            }
        }

        if (!request.selectedHotelId) {
            const capacityAllocatedRequest = capacityAllocationResult.requests.find((r) => r.id === request.id);
            if (capacityAllocatedRequest.selectedHotelId) {
                const selectedHotel = idToHotel[capacityAllocatedRequest.selectedHotelId];
                if (capacityAllocatedRequest.upgradedRooms) {
                    request.upgradedRooms = capacityAllocatedRequest.upgradedRooms;
                    request.recalculateTypeToRoomCount();
                }
                selectedHotel.allocateRequest(request);
            }
            continue;
        }
    }

    return { hotels, requests };
}

export function allocateRequestsByEnforcedHotel(hotelsOrigin, requestsOrigin) {
    const { requests, hotels } = cloneTetrisResult(hotelsOrigin, requestsOrigin);

    let isCompleted = true;

    for (const request of requests) {
        if (!request.enforcedHotelId || request.selectedHotelId) {
            continue;
        }
        const enforcedHotel = hotels.find((h) => h.id === request.enforcedHotelId);
        if (enforcedHotel) {
            enforcedHotel.allocateRequest(request);
        } else {
            isCompleted = false;
        }
    }

    return { hotels, requests, isCompleted };
}

export function allocateRequestWithRoomUpgrades(request, hotels) {
    const negativesSumReducer = (s, e) => (e < 0 ? s + Math.abs(e) : s);

    const sortedHotels = [...hotels].sort((a, b) => {
        const aUpgradeCount = Object.values(a.getRequestCapacityDiffPerType(request)).reduce(negativesSumReducer, 0);
        const bUpgradeCount = Object.values(b.getRequestCapacityDiffPerType(request)).reduce(negativesSumReducer, 0);
        return aUpgradeCount - bUpgradeCount;
    });

    for (const hotel of sortedHotels) {
        const typesForUpgradeToCount = getRequestUpgradeMap(request, hotel);

        if (typesForUpgradeToCount) {
            performRequestRoomUpgrades(request, typesForUpgradeToCount);
            hotel.allocateRequest(request);
            hotel.recalculateTypeToCurrentRoomCount();
            break;
        }
    }
}

export function getRequestUpgradeMap(request, hotel, isRequestAllocated = false) {
    const typeToHotelRoomCount = isRequestAllocated
        ? hotel.typeToCurrentRoomCount
        : hotel.getRequestCapacityDiffPerType(request);

    const typeForUpgradeToCount = Object.entries(typeToHotelRoomCount).reduce((m, [typeName, diff]) => {
        if (diff < 0) {
            m[typeName] = Math.abs(diff);
        }
        return m;
    }, {});

    const upgradeTypeToUpgradeCount = Object.entries(typeForUpgradeToCount).reduce((m, [typeName, diff]) => {
        const upgradeType = RoomTypeStorage.getUpgradeTypeByName(typeName);
        if (upgradeType) {
            m[upgradeType.name] = diff;
        }
        return m;
    }, {});

    let isEnoughUpgradedRooms = false;

    if (isRequestAllocated) {
        isEnoughUpgradedRooms = ObjectHelper.moreThan(hotel.typeToCurrentRoomCount, upgradeTypeToUpgradeCount);
    } else {
        const upgradeTypeToTotalCount = ObjectHelper.add(
            { ...upgradeTypeToUpgradeCount },
            ObjectHelper.subtract({ ...request.typeToRoomCount }, typeForUpgradeToCount)
        );
        isEnoughUpgradedRooms = ObjectHelper.moreThan(hotel.typeToCurrentRoomCount, upgradeTypeToTotalCount);
    }

    const isEnoughUpgradableRooms = ObjectHelper.moreThan(
        request.getTypeToUpgradableRoomCount(),
        typeForUpgradeToCount
    );

    if (!isEnoughUpgradedRooms || !isEnoughUpgradableRooms) {
        return null;
    }

    return typeForUpgradeToCount;
}

export function performRequestRoomUpgrades(request, typeToUpgradeCount) {
    request.upgradedRooms = request.rooms.map((r) => ({ ...r }));

    for (const [typeName, upgradeCount] of Object.entries(typeToUpgradeCount)) {
        const type = RoomTypeStorage.getTypeByName(typeName);
        const upgradeType = RoomTypeStorage.getUpgradeTypeByName(type.name);
        let upgradedCount = 0;

        for (const room of request.upgradedRooms) {
            if (room.type !== typeName) {
                continue;
            }

            room.type = upgradeType.name;
            upgradedCount += 1;

            if (upgradedCount === upgradeCount) {
                break;
            }
        }
    }

    request.recalculateTypeToRoomCount();
}

export function mergeAllocatedRequestCopies(hotels, requests) {
    const idToObjectReducer = (m, o) => ({ ...m, [o.id]: o });
    const allocatedRequests = hotels.flatMap((h) => h.allocatedRequests);
    const idToAllocatedRequests = allocatedRequests.reduce(idToObjectReducer, {});
    const idToAllRequests = requests.reduce(idToObjectReducer, {});
    Object.assign(idToAllRequests, idToAllocatedRequests);

    return Object.values(idToAllRequests);
}

export function getMaxNumberOfRooms(rooms) {
    let maxNumberOfRooms = 0;

    if (!rooms?.length) {
        return maxNumberOfRooms;
    }

    for (let i = 0; i < rooms.length; i++) {
        let checkDate = new Date(rooms[i].checkInDate);

        while (checkDate < new Date(rooms[i].checkOutDate)) {
            let numberOfRoomsInDate = 1;

            for (let k = i + 1; k < rooms.length; k++) {
                const comparedRoomCheckIn = new Date(rooms[k].checkInDate);
                const comparedRoomCheckOut = new Date(rooms[k].checkOutDate);

                if (comparedRoomCheckIn <= checkDate && checkDate < comparedRoomCheckOut) {
                    numberOfRoomsInDate += 1;
                }
            }

            if (maxNumberOfRooms < numberOfRoomsInDate) {
                maxNumberOfRooms = numberOfRoomsInDate;
            }

            checkDate = new Date(checkDate.getTime() + 36e5 * 24);
        }
    }

    return maxNumberOfRooms;
}

function cloneTetrisResult(hotelsOrigin, requestsOrigin) {
    const requests = requestsOrigin.map((r) => r.makeCopy()).sort((a, b) => b.compareTo(a));
    const idToRequest = requests.reduce((m, r) => {
        m[r.id] = r;
        return m;
    }, {});
    const hotels = hotelsOrigin.map((h) => {
        const hotelClone = h.makeCopy();
        hotelClone.allocatedRequests = h.allocatedRequests.map((r) => idToRequest[r.id]);
        return hotelClone;
    });
    return { hotels, requests };
}
