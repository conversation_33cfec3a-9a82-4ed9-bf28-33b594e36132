import { LightningElement, api } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import LightningConfirm from 'lightning/confirm';
import { loadStyle } from 'lightning/platformResourceLoader';
import { showErrors } from 'c/errorHandler';
import stylesResource from '@salesforce/resourceUrl/hm_Styles';
import {
    allocateRequestsByEnforcedHotel,
    allocateRequestsByPreference,
    getRequestUpgradeMap,
    mergeAllocatedRequestCopies,
    performRequestRoomUpgrades
} from './tetrisLogic';
import { Hotel, HotelRequest, RoomTypeStorage, getTypedTetrisResult } from './models';
import { ObjectHelper } from './helpers';
import saveTetrisResult from '@salesforce/apex/hm_HotelTetrisCtrl.saveTetrisResult';
import getDataForHotelTetris from '@salesforce/apex/hm_HotelTetrisCtrl.getDataForHotelTetris';
import getRoomTypesSettings from '@salesforce/apex/hm_HotelTetrisCtrl.getRoomTypesSettings';
import { labels } from './labels';

const TETRIS_RESULT_STORAGE_KEY = 'tetrisResult';

export default class HmHotelTetris extends NavigationMixin(LightningElement) {
    @api stickyElementsTop = '130px';

    tetrisData;
    tetrisResult;
    draggedRequest;
    isSpinnerShown;
    labels = labels;

    get isComponentShown() {
        return this.tetrisResult?.requests?.length;
    }

    get unassignedRequests() {
        return this.tetrisResult.requests.filter((r) => !r.selectedHotelId);
    }

    get isAnyRequestsAllocated() {
        return this.tetrisResult?.requests?.some((r) => r.selectedHotelId);
    }

    get isNoUnassignedRequests() {
        return !this.unassignedRequests?.length;
    }

    async connectedCallback() {
        this.isSpinnerShown = true;
        await Promise.all([this.fetchTetrisData(), this.fetchRoomTypesSettings()]).finally(
            () => (this.isSpinnerShown = false)
        );
        const tetrisResult = this.getTetrisResultFromLocalStorage();

        if (tetrisResult) {
            this.tetrisResult = tetrisResult;
            this.syncTetrisResultsWithData();
        } else {
            this.tetrisResult = getTypedTetrisResult(this.tetrisData);
        }

        loadStyle(this, stylesResource + '/main.css');
    }

    renderedCallback() {
        this.template.host.style.setProperty('--hm-sticky-top', this.stickyElementsTop);
    }

    runTetris() {
        const enforcedAssignmentResult = allocateRequestsByEnforcedHotel(
            this.tetrisResult?.hotels || this.tetrisData.hotels,
            this.tetrisResult?.requests || this.tetrisData.requests
        );
        const tetrisResult = allocateRequestsByPreference(
            enforcedAssignmentResult.hotels,
            enforcedAssignmentResult.requests
        );
        tetrisResult.requests = mergeAllocatedRequestCopies(tetrisResult.hotels, tetrisResult.requests);

        this.tetrisResult = Object.assign(this.tetrisResult, tetrisResult);
        this.saveTetrisResultToLocalStorage(this.tetrisResult);
    }

    fetchTetrisData() {
        return getDataForHotelTetris()
            .then((tetrisData) => {
                this.tetrisData = {
                    hotels: tetrisData.hotels.map((h) => new Hotel(h)),
                    requests: tetrisData.requests.map((r) => new HotelRequest(r))
                };
            })
            .catch((error) => showErrors(this, error));
    }

    fetchRoomTypesSettings() {
        return getRoomTypesSettings()
            .then((roomTypes) => {
                RoomTypeStorage.setTypes(roomTypes);
            })
            .catch((error) => showErrors(this, error));
    }

    saveTetrisResult() {
        const hotelsCopy = JSON.parse(JSON.stringify(this.tetrisResult.hotels));
        const hotelsForSave = [];

        for (const hotel of hotelsCopy) {
            if (!hotel.allocatedRequests?.length) {
                continue;
            }
            for (const request of hotel.allocatedRequests) {
                if (request.upgradedRooms?.length) {
                    request.rooms = request.upgradedRooms;
                }
            }
            hotelsForSave.push(hotel);
        }

        return saveTetrisResult({ eventId: this.eventId, allocationResult: { hotels: hotelsForSave } })
            .then((tetrisResultId) => {
                this.redirectToRecordPage(tetrisResultId);
            })
            .catch((error) => showErrors(this, error));
    }

    handleRequestDragStart(event) {
        const draggedRequestId = event.currentTarget.dataset.id;
        this.draggedRequest = this.tetrisResult.requests.find((r) => r.id === draggedRequestId);
        this.highlightDropzones();
    }

    handleRequestDragEnd() {
        this.draggedRequest = null;
        this.removeDropzoneHighlights();
    }

    handleDropZoneDragOver(event) {
        event.preventDefault();
    }

    handleRequestDrop(event) {
        event.preventDefault();
        const newHotelId = event.currentTarget.dataset.id;
        let prevHotel;

        if (this.draggedRequest.selectedHotelId === newHotelId) {
            return;
        }

        if (this.draggedRequest.selectedHotelId) {
            prevHotel = this.tetrisResult.hotels.find((h) => h.id === this.draggedRequest.selectedHotelId);
        }

        if (prevHotel) {
            prevHotel.removeRequestById(this.draggedRequest.id);
            prevHotel.setRequestsUpgradability();
        }

        if (newHotelId) {
            const newHotel = this.tetrisResult.hotels.find((h) => h.id === newHotelId);
            newHotel.allocateRequest(this.draggedRequest);
            newHotel.setRequestsUpgradability();
        }

        this.draggedRequest = null;
        this.removeDropzoneHighlights();
        this.refreshTetrisResult();
    }

    handleLaunchTetrisClick() {
        this.isSpinnerShown = true;

        //Making sure that runTetris executes after lwc reactivity handles isSpinnerShown change
        //Without setTimeout spinner will never appear.
        // eslint-disable-next-line @lwc/lwc/no-async-operation
        setTimeout(() => {
            try {
                this.runTetris();
            } catch (error) {
                showErrors(this, error);
            }
            this.isSpinnerShown = false;
        }, 0);
    }

    handleSyncClick() {
        this.isSpinnerShown = true;
        this.fetchTetrisData()
            .then(() => this.syncTetrisResultsWithData())
            .finally(() => (this.isSpinnerShown = false));
    }

    async handleResetTetrisResultClick() {
        const isConfirmed = await LightningConfirm.open({
            message: this.labels.hmTetrisResetConfirm,
            variant: 'headerless',
            label: this.labels.hmTetrisResetConfirm
        });

        if (!isConfirmed) {
            return;
        }

        for (const hotel of this.tetrisResult.hotels) {
            hotel.removeAllRequests();
        }

        this.tetrisResult = { ...this.tetrisResult };
        this.removeTetrisResultFromLocalStorage();
    }

    handleSaveClick() {
        const validationError = this.validateTetrisResult();
        if (validationError) {
            showErrors(this, new Error(validationError));
            return;
        }

        this.isSpinnerShown = true;
        this.saveTetrisResult().finally(() => (this.isSpinnerShown = false));
    }

    handleRequestUpgradeClick(event) {
        const hotelId = event.currentTarget.dataset.hotelId;
        const requestId = event.currentTarget.dataset.id;
        const hotel = this.tetrisResult.hotels.find((h) => h.id === hotelId);
        const request = hotel.allocatedRequests.find((r) => r.id === requestId);
        const upgradeMap = getRequestUpgradeMap(request, hotel, true);

        performRequestRoomUpgrades(request, upgradeMap);
        hotel.recalculateTypeToCurrentRoomCount();
        hotel.setRequestsUpgradability();
        this.refreshTetrisResult();
    }

    handleRemoveAllHotelRequestsClick(event) {
        const hotelId = event.currentTarget.dataset.hotelId;
        const hotel = this.tetrisResult.hotels.find((h) => h.id === hotelId);
        if (hotel) {
            hotel.removeAllRequests();
            this.refreshTetrisResult();
        }
    }

    highlightDropzones() {
        const hotelDropzoneNodes = this.template.querySelectorAll('.hm-hotel-allocated-requests-container');
        const idToHotelDropzoneNode = Array.from(hotelDropzoneNodes).reduce((map, hn) => {
            const hotelId = hn.dataset.id;
            map[hotelId] = hn;
            return map;
        }, {});

        for (const hotel of this.tetrisResult.hotels) {
            const dropzoneNode = idToHotelDropzoneNode[hotel.id];

            if (this.draggedRequest.selectedHotelId === hotel.id || hotel.canAllocateRequest(this.draggedRequest)) {
                dropzoneNode.classList.add('hm-drop-slot_valid');
            } else {
                dropzoneNode.classList.add('hm-drop-slot_invalid');
            }
        }

        const unassignedRequestsNode = this.template.querySelector('.hm-unassigned-requests-container');
        unassignedRequestsNode.classList.add('hm-drop-slot_valid');
    }

    removeDropzoneHighlights() {
        const hotelDropzoneNodes = this.template.querySelectorAll(
            // eslint-disable-next-line no-useless-concat
            '.hm-hotel-allocated-requests-container,' + '.hm-unassigned-requests-container'
        );
        for (const node of Array.from(hotelDropzoneNodes)) {
            node.classList.remove('hm-drop-slot_valid');
            node.classList.remove('hm-drop-slot_invalid');
        }
    }

    redirectToRecordPage(recordId) {
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: recordId,
                actionName: 'view'
            }
        });
    }

    refreshTetrisResult() {
        this.tetrisResult = { ...this.tetrisResult };
        this.saveTetrisResultToLocalStorage(this.tetrisResult);
    }

    saveTetrisResultToLocalStorage(tetrisResult) {
        localStorage.setItem(TETRIS_RESULT_STORAGE_KEY, JSON.stringify(tetrisResult));
    }

    removeTetrisResultFromLocalStorage() {
        localStorage.removeItem(TETRIS_RESULT_STORAGE_KEY);
    }

    getTetrisResultFromLocalStorage() {
        const tetrisResultJson = localStorage.getItem(TETRIS_RESULT_STORAGE_KEY);
        if (!tetrisResultJson) {
            return null;
        }
        return getTypedTetrisResult(JSON.parse(tetrisResultJson));
    }

    syncTetrisResultsWithData() {
        this.tetrisResult = {
            ...this.tetrisResult,
            hotels: this.syncHotelsWithData(),
            requests: this.syncHotelRequestsWithData()
        };
        this.saveTetrisResultToLocalStorage(this.tetrisResult);
    }

    syncHotelsWithData() {
        const idToHotelData = ObjectHelper.mapToField(this.tetrisData.hotels, 'id');
        const hotels = [];

        for (const hotel of this.tetrisResult.hotels) {
            const hotelData = idToHotelData[hotel.id];
            if (hotelData) {
                hotel.syncWith(hotelData);
                hotels.push(hotel);
                continue;
            }

            hotel.removeAllRequests();
        }

        const idToHotel = ObjectHelper.mapToField(this.tetrisResult.hotels, 'id');

        for (const hotelData of this.tetrisData.hotels) {
            if (!idToHotel[hotelData.id]) {
                hotels.push(new Hotel(hotelData));
            }
        }

        return hotels;
    }

    syncHotelRequestsWithData() {
        const idToRequestData = ObjectHelper.mapToField(this.tetrisData.requests, 'id');
        const requests = [];

        for (const request of this.tetrisResult.requests) {
            const requestData = idToRequestData[request.id];
            if (requestData) {
                request.syncWith(requestData);
                requests.push(request);
                continue;
            }

            if (request.selectedHotelId) {
                const selectedHotel = this.tetrisResult.hotels.find((h) => h.id === request.selectedHotelId);
                selectedHotel.removeRequestById(request.id);
            }
        }

        const idToRequest = ObjectHelper.mapToField(this.tetrisResult.requests, 'id');

        for (const requestData of this.tetrisData.requests) {
            if (!idToRequest[requestData.id]) {
                requests.push(new HotelRequest(requestData));
            }
        }

        return requests;
    }

    validateTetrisResult() {
        const isAllRequestsAllocated = !this.tetrisResult.requests.some((r) => !r.selectedHotelId);
        if (!isAllRequestsAllocated) {
            return this.labels.hmTetrisUnassignedReqSaveErr;
        }

        const isHotelsHaveSufficientCapacity = this.tetrisResult.hotels.some((h) => h.hasInsufficientCapacity());
        if (isHotelsHaveSufficientCapacity) {
            return this.labels.hmTetrisHotelCapacitySaveErr;
        }

        return null;
    }
}
