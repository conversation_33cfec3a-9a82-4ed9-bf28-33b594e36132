<template>
    <lightning-spinner lwc:if={isSpinnerShown}></lightning-spinner>
    <div lwc:if={isComponentShown}>
        <article
            class="slds-card slds-p-vertical_medium slds-p-horizontal_small slds-m-bottom_small slds-grid slds-grid_align-end slds-size_1-of-1"
        >
            <lightning-button
                variant="brand-outline"
                label={labels.hmTetrisLaunchTetrisBtn}
                onclick={handleLaunchTetrisClick}
                class="slds-m-right_small"
            ></lightning-button>
            <lightning-button
                variant="brand-outline"
                label={labels.hmTetrisSyncDataBtn}
                onclick={handleSyncClick}
                class="slds-m-right_small"
            ></lightning-button>
            <lightning-button
                lwc:if={isAnyRequestsAllocated}
                onclick={handleResetTetrisResultClick}
                class="slds-m-right_small"
                label={labels.hmTetrisResetBtn}
                variant="destructive-text"
            >
            </lightning-button>
            <lightning-button variant="brand" label="Save" onclick={handleSaveClick}></lightning-button>
        </article>
        <article class="slds-card slds-col slds-p-vertical_medium slds-m-horizontal_xxx-small slds-grid slds-gutters">
            <div lwc:if={tetrisResult} class="slds-col slds-size_1-of-4">
                <div class="hm-requests-block">
                    <h3 class="slds-text-title_bold slds-m-bottom_small">Unassigned Requests</h3>
                    <article
                        class="slds-card slds-card_boundary hm-unassigned-requests-container slds-scrollable_y slds-p-around_x-small"
                        ondragover={handleDropZoneDragOver}
                        ondrop={handleRequestDrop}
                        ondragleave={handleDropZoneDragLeave}
                    >
                        <template for:each={unassignedRequests} for:item="request">
                            <article
                                key={request.id}
                                class="slds-card slds-card_boundary hm-hotel-request-tile"
                                draggable="true"
                                ondragstart={handleRequestDragStart}
                                ondragend={handleRequestDragEnd}
                                data-id={request.id}
                            >
                                <div class="slds-m-left_small">
                                    <h4>
                                        <span class="slds-text-title_bold">{request.accountName}</span
                                        >&nbsp;({request.name})
                                        <lightning-helptext
                                            lwc:if={request.onSiteTransportation}
                                            class="hm-inline-helptext_small slds-m-left_xx-small"
                                            icon-name="utility:travel_and_places"
                                            content={labels.hmTetrisTransportHelp}
                                        ></lightning-helptext>
                                    </h4>
                                </div>
                                <div class="slds-m-left_small">
                                    <template for:each={request.roomCounts} for:item="roomCount">
                                        <span class="slds-m-right_x-small" key={roomCount.typeName}
                                            >{roomCount.typeName}:&nbsp;{roomCount.count}</span
                                        >
                                    </template>
                                </div>
                            </article>
                        </template>
                        <div lwc:if={isNoUnassignedRequests} class="slds-card__body slds-card__body_inner">
                            <div class="slds-illustration slds-illustration_small">
                                <div class="slds-text-longform">
                                    <h3 class="slds-text-heading_medium">
                                        {labels.hmTetrisAllRequestsAllocatedHeader}
                                    </h3>
                                    <p class="slds-text-body_regular">{labels.hmTetrisAllRequestsAllocatedText}</p>
                                </div>
                            </div>
                        </div>
                    </article>
                </div>
            </div>
            <div lwc:if={tetrisResult} class="slds-col slds-size_3-of-4">
                <h3 class="slds-text-title_bold slds-m-bottom_small">Hotels</h3>
                <template for:each={tetrisResult.hotels} for:item="hotel">
                    <article key={hotel.id} class="slds-card slds-card_boundary hm-hotel-tile">
                        <div class="slds-grid">
                            <div class="slds-col slds-size_2-of-3">
                                <div class="slds-m-left_small slds-m-top_small">
                                    <h3 class="slds-text-heading_small slds-text-title_bold">
                                        {hotel.name}
                                        <lightning-helptext
                                            lwc:if={hotel.isInWalkingDistance}
                                            class="hm-inline-helptext_small hm-inline-helptext-bottom_medium slds-m-left_xxx-small"
                                            icon-name="utility:transport_walking"
                                            content={labels.hmTetrisWalkDistHelp}
                                        ></lightning-helptext>
                                    </h3>
                                </div>
                                <div class="slds-m-left_small slds-m-bottom_small">
                                    <template for:each={hotel.currentRoomCounts} for:item="roomCount">
                                        <span
                                            lwc:if={roomCount.isNegative}
                                            class="slds-m-right_x-small slds-text-color_error"
                                            key={roomCount.typeName}
                                            >{roomCount.typeName}:&nbsp;{roomCount.count}</span
                                        >
                                        <span lwc:else class="slds-m-right_x-small" key={roomCount.typeName}
                                            >{roomCount.typeName}:&nbsp;{roomCount.count}</span
                                        >
                                    </template>
                                </div>
                            </div>
                            <div
                                class="slds-col slds-col_bump-left slds-m-right_small slds-grid slds-grid_align-end slds-grid_vertical-align-center"
                            >
                                <div>
                                    <button
                                        lwc:if={hotel.hasRequests}
                                        onclick={handleRemoveAllHotelRequestsClick}
                                        data-hotel-id={hotel.id}
                                        class="slds-button slds-button_text-destructive"
                                    >
                                        {labels.hmTetrisRemoveAllReqBtn}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div
                            class="slds-grid slds-wrap hm-hotel-allocated-requests-container"
                            ondragover={handleDropZoneDragOver}
                            ondrop={handleRequestDrop}
                            ondragleave={handleDropZoneDragLeave}
                            data-id={hotel.id}
                        >
                            <template for:each={hotel.allocatedRequests} for:item="request">
                                <div key={request.id} class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                    <article
                                        class="slds-card slds-card_boundary hm-hotel-request-tile"
                                        draggable="true"
                                        ondragstart={handleRequestDragStart}
                                        ondragend={handleRequestDragEnd}
                                        data-id={request.id}
                                    >
                                        <div
                                            class="slds-grid slds-gutters slds-p-horizontal_small slds-p-vertical_x-small"
                                        >
                                            <div class="slds-col">
                                                <div>
                                                    <h4>
                                                        <span class="slds-text-title_bold">{request.accountName}</span
                                                        >&nbsp;({request.name})
                                                        <lightning-helptext
                                                            lwc:if={request.onSiteTransportation}
                                                            class="hm-inline-helptext_small slds-m-left_xx-small"
                                                            icon-name="utility:travel_and_places"
                                                            content={labels.hmTetrisTransportHelp}
                                                        ></lightning-helptext>
                                                    </h4>
                                                </div>
                                                <div>
                                                    <template lwc:if={request.upgradedRoomCounts}>
                                                        <template
                                                            for:each={request.upgradedRoomCounts}
                                                            for:item="roomCount"
                                                        >
                                                            <span class="slds-m-right_small" key={roomCount.typeName}
                                                                >{roomCount.typeName}:&nbsp;{roomCount.count}</span
                                                            >
                                                        </template>
                                                        <lightning-helptext
                                                            class="hm-inline-helptext_medium"
                                                            icon-name="utility:jump_to_top"
                                                            content={request.baseRoomCountsMessage}
                                                        ></lightning-helptext>
                                                    </template>
                                                    <template
                                                        lwc:else
                                                        for:each={request.roomCounts}
                                                        for:item="roomCount"
                                                    >
                                                        <span class="slds-m-right_x-small" key={roomCount.typeName}
                                                            >{roomCount.typeName}:&nbsp;{roomCount.count}</span
                                                        >
                                                    </template>
                                                </div>
                                            </div>
                                            <div
                                                class="slds-col slds-col_bump-left slds-m-right_small slds-grid slds-grid_align-end slds-grid_vertical-align-center"
                                            >
                                                <div>
                                                    <button
                                                        lwc:if={request.isUpgradable}
                                                        onclick={handleRequestUpgradeClick}
                                                        data-hotel-id={hotel.id}
                                                        data-id={request.id}
                                                        class="slds-button slds-button_outline-brand"
                                                    >
                                                        {labels.hmTetrisUpgradeBtn}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </article>
                                </div>
                            </template>
                        </div>
                    </article>
                </template>
            </div>
        </article>
    </div>
</template>
