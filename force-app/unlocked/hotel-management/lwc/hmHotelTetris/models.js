import { ObjectHelper } from './helpers';
import { getMaxNumberOfRooms, getRequestUpgradeMap, mergeAllocatedRequestCopies } from './tetrisLogic';

export class RoomTypeStorage {
    static types = [];
    static nameToType = {};
    static isUpgradesAvailable = false;

    static setTypes(roomTypes) {
        this.types = roomTypes;
        this.nameToType = this.types.reduce((m, t) => ({ ...m, [t.name]: t }), {});
        this.isUpgradesAvailable = this.types.some((t) => !!t.upgradeTypeName);
    }

    static getTypes() {
        return this.types;
    }

    static getTypeByName(typeName) {
        return this.nameToType[typeName];
    }

    static getUpgradeTypeByName(typeName) {
        const type = this.getTypeByName(typeName);
        if (!type.upgradeTypeName) {
            return null;
        }
        return this.getTypeByName(type.upgradeTypeName);
    }

    static getSortedTypeNames() {
        const capacitySortedTypes = [...this.types].sort((a, b) => a.capacity - b.capacity);
        return capacitySortedTypes.map((t) => t.name);
    }
}

export class Hotel {
    id;
    name;
    isInWalkingDistance;
    typeToRoomCount = {};
    typeToCurrentRoomCount;
    allocatedRequests = [];

    get hasRequests() {
        return this.allocatedRequests?.length;
    }

    get currentRoomCounts() {
        return getRoomCounts(this.typeToCurrentRoomCount);
    }

    constructor(fields) {
        Object.assign(this, fields);
        if (!this.typeToCurrentRoomCount) {
            this.typeToCurrentRoomCount = this.typeToRoomCount;
        }
    }

    canAllocateRequest(request) {
        return ObjectHelper.lessThan(request.typeToRoomCount, this.typeToCurrentRoomCount);
    }

    canAllocateOneOfRequests(requests) {
        return requests.some((r) => this.canAllocateRequest(r));
    }

    recalculateTypeToCurrentRoomCount() {
        this.typeToCurrentRoomCount = { ...this.typeToRoomCount };
        for (const request of this.allocatedRequests) {
            const usedTypeToCount = request.upgradedTypeToRoomCount || request.typeToRoomCount;
            ObjectHelper.subtract(this.typeToCurrentRoomCount, usedTypeToCount);
        }
    }

    getRequestCapacityDiff(request) {
        let diff = 0;

        for (const roomType of Object.keys(request.typeToRoomCount)) {
            const hasEnoughRoomsOfType = this.typeToCurrentRoomCount[roomType] >= request.typeToRoomCount[roomType];

            if (!hasEnoughRoomsOfType) {
                return null;
            }

            diff += this.typeToCurrentRoomCount[roomType] - request.typeToRoomCount[roomType];
        }

        return diff;
    }

    getRequestCapacityDiffPerType(request) {
        return ObjectHelper.getDiff(this.typeToCurrentRoomCount, request.typeToRoomCount);
    }

    allocateRequest(request) {
        const subtractRooms = request.upgradedTypeToRoomCount || request.typeToRoomCount;
        ObjectHelper.subtract(this.typeToCurrentRoomCount, subtractRooms);
        this.allocatedRequests.push(request);
        request.setSelectedHotelId(this.id);
    }

    removeLastRequest() {
        this.removeRequestByIndex(this.allocatedRequests.length - 1);
    }

    removeRequestById(requestId) {
        const requestIndex = this.allocatedRequests.findIndex((r) => r.id === requestId);
        if (requestIndex !== -1) {
            this.removeRequestByIndex(requestIndex);
        }
    }

    removeAllRequests() {
        for (const request of this.allocatedRequests) {
            request.setSelectedHotelId(null);
        }
        this.allocatedRequests = [];
        this.recalculateTypeToCurrentRoomCount();
    }

    removeRequestByIndex(requestIndex) {
        const [removedRequest] = this.allocatedRequests.splice(requestIndex, 1);
        const gainedRoomTypeToCount = removedRequest.upgradedTypeToRoomCount || removedRequest.typeToRoomCount;
        ObjectHelper.add(this.typeToCurrentRoomCount, gainedRoomTypeToCount);
        removedRequest.setSelectedHotelId(null);
    }

    getCurrentTotalRoomCount() {
        return ObjectHelper.sumValues(this.typeToCurrentRoomCount);
    }

    makeCopy() {
        let copy = new Hotel(this);
        copy.typeToRoomCount = { ...this.typeToRoomCount };
        copy.typeToCurrentRoomCount = { ...this.typeToCurrentRoomCount };
        copy.allocatedRequests = [...this.allocatedRequests];
        return copy;
    }

    syncWith(hotelData) {
        this.name = hotelData.name;
        this.typeToRoomCount = hotelData.typeToRoomCount;
        this.isInWalkingDistance = hotelData.isInWalkingDistance;
        this.recalculateTypeToCurrentRoomCount();
    }

    hasInsufficientCapacity() {
        return Object.values(this.typeToCurrentRoomCount).some((c) => c < 0);
    }

    setRequestsUpgradability() {
        const isInsufficientCapacity = this.hasInsufficientCapacity();

        for (const request of this.allocatedRequests) {
            if (!isInsufficientCapacity || request.upgradedTypeToRoomCount) {
                request.isUpgradable = false;
                continue;
            }

            const upgradeMap = getRequestUpgradeMap(request, this, true);
            request.isUpgradable = upgradeMap && !!Object.keys(upgradeMap).length;
        }
    }

    compareTo(comparableHotel) {
        let currentRoomCount = this.getCurrentTotalRoomCount();
        let comparableCurrentRoomCount = comparableHotel.getCurrentTotalRoomCount();

        if (currentRoomCount === comparableCurrentRoomCount) {
            let maxRoomCount = ObjectHelper.getMaxValue(this.typeToCurrentRoomCount);
            let comparableMaxRoomCount = ObjectHelper.getMaxValue(comparableHotel.typeToCurrentRoomCount);
            return maxRoomCount >= comparableMaxRoomCount ? 1 : -1;
        }

        return currentRoomCount > comparableCurrentRoomCount ? 1 : -1;
    }
}

export class HotelRequest {
    constructor(fields) {
        this.id = fields?.id;
        this.name = fields?.name;
        this.accountName = fields?.accountName;
        this.onSiteTransportation = fields?.onSiteTransportation;
        this.typeToRoomCount = fields?.typeToRoomCount || {};
        this.upgradedTypeToRoomCount = fields?.upgradedTypeToRoomCount;
        this.enforcedHotelId = fields?.enforcedHotelId;
        this.enforcedHotelName = fields?.enforcedHotelName;
        this.hotelPrefs = fields?.hotelPrefs || [];
        this.selectedHotelId = fields?.selectedHotelId;
        this.typeToReducedRoomCount = fields?.typeToReducedRoomCount || {};
        this.rooms = fields?.rooms || [];
        this.upgradedRooms = fields?.upgradedRooms;
        this.isUpgradable = fields?.isUpgradable;

        if (this.rooms?.length && !Object.keys(this.typeToReducedRoomCount).length) {
            this.recalculateTypeToRoomCount();
        }
    }

    get roomCounts() {
        return getRoomCounts(this.typeToRoomCount);
    }

    get upgradedRoomCounts() {
        return this.upgradedTypeToRoomCount ? getRoomCounts(this.upgradedTypeToRoomCount) : null;
    }

    get baseRoomCountsMessage() {
        let message = 'Request rooms were upgraded, initial values: ';
        const typeCountEntries = this.roomCounts.map((rc) => `${rc.typeName}: ${rc.count}`);
        message += typeCountEntries.join(', ');
        return message;
    }

    getTotalRoomCount() {
        return ObjectHelper.sumValues(this.typeToRoomCount);
    }

    getLargestRoomCount() {
        return ObjectHelper.getMaxValue(this.typeToRoomCount);
    }

    setSelectedHotelId(id) {
        this.selectedHotelId = id;
        if (!id) {
            this.upgradedRooms = null;
            this.upgradedTypeToRoomCount = null;
            this.isUpgradable = false;
        }
    }

    makeCopy() {
        return new HotelRequest(this);
    }

    compareTo(comparable) {
        let comparableRequest = comparable;
        if (this.getTotalRoomCount() === comparableRequest.getTotalRoomCount()) {
            if (this.getLargestRoomCount() > comparableRequest.getLargestRoomCount()) {
                return 1;
            }
            return 0;
        }
        return this.getTotalRoomCount() > comparableRequest.getTotalRoomCount() ? 1 : -1;
    }

    recalculateTypeToRoomCount() {
        [this.typeToRoomCount, this.typeToReducedRoomCount] = this.calculateRoomCountsWithReduction(this.rooms);

        if (this.upgradedRooms) {
            [this.upgradedTypeToRoomCount, this.typeToReducedRoomCount] = this.calculateRoomCountsWithReduction(
                this.upgradedRooms
            );
        } else {
            this.upgradedTypeToRoomCount = null;
        }
    }

    calculateRoomCountsWithReduction(allRooms) {
        const typeToRooms = ObjectHelper.mapManyToField(allRooms, 'type');
        const typeToRoomCount = {};
        const typeToReducedRoomCount = {};

        for (const [type, rooms] of Object.entries(typeToRooms)) {
            const totalRoomCount = rooms.length;
            const minRequiredRoomCount = getMaxNumberOfRooms(rooms);
            typeToRoomCount[type] = minRequiredRoomCount;
            typeToReducedRoomCount[type] = totalRoomCount - minRequiredRoomCount;
        }

        return [typeToRoomCount, typeToReducedRoomCount];
    }

    getTypeToUpgradableRoomCount() {
        return this.rooms.reduce((m, r) => {
            m[r.type] = m[r.type] || 0;
            const type = RoomTypeStorage.getTypeByName(r.type);
            if (type.upgradeTypeName && r.visitorCount >= type.minVisitorsForUpgrade) {
                m[r.type] += 1;
            }
            return m;
        }, {});
    }

    syncWith(requestData) {
        this.rooms = requestData.rooms;
        this.enforcedHotelId = requestData.enforcedHotelId;
        this.enforcedHotelName = requestData.enforcedHotelName;
        this.hotelPrefs = requestData.hotelPrefs;
        this.onSiteTransportation = requestData.onSiteTransportation;
        this.recalculateTypeToRoomCount();
    }
}

export function getTypedTetrisResult(tetrisResult) {
    const typedTetrisResult = {
        hotels: tetrisResult.hotels.map((h) => {
            const hModel = new Hotel(h);
            hModel.allocatedRequests = hModel.allocatedRequests.map((r) => new HotelRequest(r));
            return hModel;
        }),
        requests: tetrisResult.requests.map((r) => new HotelRequest(r))
    };
    typedTetrisResult.requests = mergeAllocatedRequestCopies(typedTetrisResult.hotels, typedTetrisResult.requests);
    return typedTetrisResult;
}

function getRoomCounts(typeToRoomCount) {
    return RoomTypeStorage.getSortedTypeNames().map((typeName) => {
        const typeEntry = { typeName, count: typeToRoomCount?.[typeName] ?? 0 };
        typeEntry.isNegative = typeEntry.count < 0;
        return typeEntry;
    });
}
