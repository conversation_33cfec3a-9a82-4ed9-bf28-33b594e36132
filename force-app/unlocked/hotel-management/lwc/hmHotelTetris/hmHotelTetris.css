:host {
    position: relative;
    --hm-sticky-top: 130px;
}

.hm-requests-block {
    position: sticky;
    top: var(--hm-sticky-top);
}

.hm-unassigned-requests-container {
    min-height: 10rem;
    max-height: calc(90vh - var(--hm-sticky-top));
}

.hm-hotel-allocated-requests-container {
    min-height: 2rem;
    outline: 1px dashed var(--slds-g-color-border-base-1, rgb(201, 201, 201));
}

.hm-inline-helptext_medium {
    position: relative;
    --lwc-squareIconMediumContentAlt: 1.2rem;
    bottom: 0.5em;
}

.hm-inline-helptext_small {
    position: relative;
    --lwc-squareIconMediumContentAlt: 1rem;
    bottom: 0.3em;
}

.hm-inline-helptext-bottom_medium {
    bottom: 0.5em;
}
