import { LightningElement, api } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getForm from '@salesforce/apex/hm_VolunteerRegistrationCtrl.getForm';
import saveRecord from '@salesforce/apex/hm_VolunteerRegistrationCtrl.saveRecord';
import checkInDateField from '@salesforce/schema/Contact.hm_VolunteerCheckInDate__c';
import checkOutDateField from '@salesforce/schema/Contact.hm_VolunteerCheckOutDate__c';
import needHotelField from '@salesforce/schema/Contact.hm_IsVolunteerNeedsHotel__c';
import hmSuccessfullySaved from '@salesforce/label/c.hmSuccessfullySaved';
import { showErrors } from 'c/errorHandler';

const DATE_FIELD_NAMES = [checkInDateField.fieldApiName, checkOutDateField.fieldApiName];

export default class extends NavigationMixin(LightningElement) {
    @api isCaptchaDisabled = false;
    @api postSavePageName;
    isSpinnerShown;
    form;
    getFormMethod = getForm;
    respectFieldLevelSecurity = false;
    useCaptcha = !this.isCaptchaDisabled;
    formOverrides = [
        { name: checkInDateField.fieldApiName, show: false },
        { name: checkOutDateField.fieldApiName, show: false }
    ];

    handleFieldChange(event) {
        if (event.detail.name === needHotelField.fieldApiName) {
            const isDateFieldsShown = event.detail.value;
            const overrides = [];
            for (const fieldName of DATE_FIELD_NAMES) {
                overrides.push({ name: fieldName, show: isDateFieldsShown, required: true });
            }
            this.refs.form.setOverrides(overrides);
        }
    }

    handleFormSubmit(event) {
        const contact = event.detail.record;
        const captchaToken = event.detail.captchaToken;

        this.toggleSpinnerDuring(saveRecord({ volunteerContact: contact, captchaToken }))
            .then(() => {
                this.showSuccessSaveToast();
                this.redirectToPostSavePage();
            })
            .catch((error) => showErrors(this, error));
    }

    redirectToPostSavePage() {
        if (!this.postSavePageName) {
            return;
        }
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: this.postSavePageName
            }
        });
    }

    showSuccessSaveToast() {
        this.dispatchEvent(
            new ShowToastEvent({
                variant: 'success',
                message: hmSuccessfullySaved
            })
        );
    }

    toggleSpinnerDuring(promise) {
        this.isSpinnerShown = true;
        return promise.finally(() => (this.isSpinnerShown = false));
    }
}
