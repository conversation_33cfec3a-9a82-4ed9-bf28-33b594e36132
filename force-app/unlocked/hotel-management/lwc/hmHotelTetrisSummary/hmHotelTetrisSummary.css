:host {
    position: relative;
}
.hm-border-bottom {
    border-bottom-style: solid;
    border-bottom-width: var(--lwc-borderWidthThin, 1px);
    border-bottom-color: var(--lwc-colorBorderSeparatorAlt, #ababab);
}

.hm-text-left {
    text-align: left;
}

.hm-text-right {
    text-align: right;
}

.hm-conf-summary-fields {
    max-width: 45rem;
}

.slds-notify lightning-icon {
    --slds-c-icon-color-foreground-default: var(--lwc-colorTextInverse, #ffff);
}
