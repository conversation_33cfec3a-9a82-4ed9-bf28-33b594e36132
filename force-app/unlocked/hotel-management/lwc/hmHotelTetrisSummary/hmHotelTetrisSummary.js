import getActiveEventSummary from '@salesforce/apex/hm_HotelTetrisSummaryCtrl.getActiveEventSummary';
import { LightningElement, track } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import hmTetrisSchools from '@salesforce/label/c.hmTetrisSchools';
import hmTetrisGuests from '@salesforce/label/c.hmTetrisGuests';
import hmTetrisHotels from '@salesforce/label/c.hmTetrisHotels';
import hmTetrisNumberOfRooms from '@salesforce/label/c.hmTetrisNumberOfRooms';
import hmTetrisNoActiveEventErr from '@salesforce/label/c.hmTetrisNoActiveEventErr';
import { showErrors } from 'c/errorHandler';

export default class extends NavigationMixin(LightningElement) {
    @track summary;
    isSpinnerShown;
    tetrisResult;
    errorMessage;
    hotels;
    requests;

    labels = {
        hmTetrisSchools,
        hmTetrisGuests,
        hmTetrisHotels,
        hmTetrisNumberOfRooms,
        hmTetrisNoActiveEventErr
    };

    get roomTypes() {
        return this.summary ? Object.values(this.summary?.nameToRoomType) : [];
    }

    get isNoActiveEvent() {
        return this.summary && !this.summary.eventId;
    }

    connectedCallback() {
        this.toggleSpinnerDuring(this.fetchSummary());
    }

    fetchSummary() {
        return getActiveEventSummary()
            .then((summary) => {
                this.summary = this.summary ? Object.assign(this.summary, summary) : summary;
                if (!this.summary.eventId) {
                    return;
                }
                this.setRoomTypeCountLabels(this.summary.nameToRoomType);
            })
            .catch((error) => showErrors(this, error));
    }

    setRoomTypeCountLabels(nameToRoomType) {
        for (const roomType of Object.values(nameToRoomType)) {
            roomType.countLabel = hmTetrisNumberOfRooms.replace('{0}', roomType.label).replace('{1}', roomType.count);
        }
    }

    toggleSpinnerDuring(promise) {
        this.isSpinnerShown = true;
        return promise.finally(() => (this.isSpinnerShown = false));
    }
}
