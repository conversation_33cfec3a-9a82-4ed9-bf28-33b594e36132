import { LightningElement, api } from 'lwc';
import stylesResource from '@salesforce/resourceUrl/hm_Styles';
import { loadStyle } from 'lightning/platformResourceLoader';
import hmRequestFormTab from '@salesforce/label/c.hmRequestFormTab';
import hmRoomsAssignmentTab from '@salesforce/label/c.hmRoomsAssignmentTab';
import hmRoomsFinishBtn from '@salesforce/label/c.hmRoomsFinishBtn';

export default class extends LightningElement {
    @api get recordId() {
        return this._recordId;
    }
    set recordId(value) {
        this._recordId = value;
    }
    @api isDateInputsDisabled = false;

    _recordId;
    _eventId;
    hotelRequestError;

    labels = {
        hmRequestFormTab,
        hmRoomsAssignmentTab,
        hmRoomsFinishBtn
    };

    connectedCallback() {
        loadStyle(this, stylesResource + '/main.css');
    }

    renderedCallback() {
        this.toggleRoomsTab();
    }

    handleHotelRequestSave(event) {
        this._recordId = event.detail.hotelRequestId;
    }

    handleHotelRequestError(event) {
        this.hotelRequestError = event.detail;
    }

    handleNextTabSwitch() {
        const tabset = this.template.querySelector('lightning-tabset');
        const nextTabNumber = parseInt(tabset.activeTabValue, 10) + 1;
        tabset.activeTabValue = nextTabNumber.toString();
    }

    toggleRoomsTab() {
        //disabling pointer events on rooms tab until there is hotel request id
        const guestToRoomsTabPointer = this._recordId ? 'all' : 'none';
        this.template.host.style.setProperty('--hm-tab-pointer-events', guestToRoomsTabPointer);
    }
}
