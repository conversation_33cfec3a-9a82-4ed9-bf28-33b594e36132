import hmApproveRoomCount from '@salesforce/label/c.hmApproveRoomCount';
import hmApproveCompanies from '@salesforce/label/c.hmApproveCompanies';
import hmExportCsv from '@salesforce/label/c.hmExportCsv';
import hmPasscodePromptMes from '@salesforce/label/c.hmPasscodePromptMes';
import hmPasscodeInput from '@salesforce/label/c.hmPasscodeInput';
import hmPasscodeNextBtn from '@salesforce/label/c.hmPasscodeNextBtn';
import hmReservationSummary from '@salesforce/label/c.hmReservationSummary';
import hmRoomNeedApproval from '@salesforce/label/c.hmRoomNeedApproval';
import hmFieldChangePrev from '@salesforce/label/c.hmFieldChangePrev';
import hmFieldChangeCurr from '@salesforce/label/c.hmFieldChangeCurr';
import hmApproveSaveSendUpdateBtn from '@salesforce/label/c.hmApproveSaveSendUpdateBtn';
import hmAcknowledgedReservations from '@salesforce/label/c.hmAcknowledgedReservations';
import hmApproveNoReservationErr from '@salesforce/label/c.hmApproveNoReservationErr';
import hmSuccessfullySaved from '@salesforce/label/c.hmSuccessfullySaved';
import hmTableLineHeader from '@salesforce/label/c.hmTableLineHeader';
import hmApproveAllBtn from '@salesforce/label/c.hmApproveAllBtn';
import hmExportXls from '@salesforce/label/c.hmExportXls';

export const labels = {
    hmApproveRoomCount,
    hmApproveCompanies,
    hmExportCsv,
    hmPasscodePromptMes,
    hmPasscodeInput,
    hmPasscodeNextBtn,
    hmReservationSummary,
    hmRoomNeedApproval,
    hmFieldChangePrev,
    hmFieldChangeCurr,
    hmApproveSaveSendUpdateBtn,
    hmAcknowledgedReservations,
    hmApproveNoReservationErr,
    hmSuccessfullySaved,
    hmTableLineHeader,
    hmApproveAllBtn,
    hmExportXls
};
