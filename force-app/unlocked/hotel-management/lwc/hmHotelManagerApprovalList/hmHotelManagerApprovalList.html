<template>
    <template if:true={reservationSummary}>
        <lightning-spinner if:true={isSpinnerShown}></lightning-spinner>
        <h2 class="slds-m-vertical_small slds-text-heading_medium">{labels.hmReservationSummary}</h2>
        <div class="slds-grid slds-wrap slds-grid_align-start slds-m-bottom_medium">
            <template for:each={roomTypes} for:item="roomType">
                <div key={roomType.name} class="slds-col slds-text-heading_small">
                    {roomType.countLabel}
                </div>
            </template>
            <div class="slds-col slds-text-heading_small">
                {labels.hmApproveCompanies} {reservationSummary.companyCount}
            </div>
            <div class="slds-col slds-col_bump-left slds-grid">
                <a upload-result-file-keeper href="#" display="none"></a>
                <div class="slds-grid slds-size_3-of-6 slds-grid_align-end">
                    <button onclick={handleCsvExportClick} class="slds-button slds-button_outline-brand slds-col">
                        {labels.hmExportCsv}
                    </button>
                </div>
                <div class="slds-grid slds-size_3-of-6 slds-grid_align-end">
                    <button onclick={handleXlsExportClick} class="slds-button slds-button_outline-brand slds-col">
                        {labels.hmExportXls}
                    </button>
                </div>
            </div>
        </div>
    </template>
    <template if:false={reservationSummary}>
        <section role="dialog" class="slds-modal slds-fade-in-open">
            <lightning-spinner if:true={isSpinnerShown}></lightning-spinner>
            <div class="slds-modal__container">
                <div class="slds-modal__header">
                    <h2 class="slds-modal__title slds-hyphenate">{labels.hmPasscodePromptMes}</h2>
                </div>
                <div class="slds-modal__content slds-p-around_medium">
                    <lightning-input
                        type="text"
                        label={labels.hmPasscodeInput}
                        data-id="passcodeInput"
                    ></lightning-input>
                    <div class="hm-captcha-target slds-m-top_small" id="hm-captcha-target"></div>
                </div>
                <div class="slds-modal__footer">
                    <button class="slds-button slds-button_brand" onclick={handleNextClick}>
                        {labels.hmPasscodeNextBtn}
                    </button>
                </div>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open" role="presentation"></div>
    </template>
    <template if:true={isThereEditableLines}>
        <h3 class="slds-m-bottom_medium slds-text-heading_small">{labels.hmRoomNeedApproval}</h3>
        <div class="slds-grid slds-scrollable">
            <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-col">
                <thead>
                    <tr class="slds-line-height_reset">
                        <th>
                            <div class="slds-truncate" title={labels.hmTableLineHeader}>{labels.hmTableLineHeader}</div>
                        </th>
                        <template for:each={tableColumns} for:item="column">
                            <th if:false={column.editable} key={column.name} scope="col">
                                <div class="slds-truncate" title={column.label}>{column.label}</div>
                            </th>
                            <th if:true={column.editable} key={column.name} scope="col">
                                <div class="slds-truncate hm-input-col-head" title={column.label}>{column.label}</div>
                            </th>
                        </template>
                    </tr>
                </thead>
                <tbody>
                    <template for:each={editableReservationLines} for:item="line">
                        <tr key={line.id} class="slds-hint-parent">
                            <td>
                                <div class="slds-truncate" title={line.number}>{line.number}</div>
                            </td>
                            <template for:each={line.fields} for:item="lineField">
                                <td key={lineField.attributes.name} data-label={lineField.attributes.label}>
                                    <div
                                        if:false={lineField.attributes.editable}
                                        class="slds-truncate"
                                        title={lineField.value}
                                    >
                                        <span if:false={lineField.oldValue}>{lineField.value}</span>
                                        <template if:true={lineField.oldValue}>
                                            <p>
                                                <span class="hm-field-change-label">
                                                    {labels.hmFieldChangePrev}&nbsp;
                                                </span>
                                                <span class="hm-field-change-value-old">{lineField.oldValue}</span>
                                            </p>
                                            <p>
                                                <span class="hm-field-change-label">
                                                    {labels.hmFieldChangeCurr}&nbsp;
                                                </span>
                                                <span class="hm-field-change-value-new">{lineField.value}</span>
                                            </p>
                                        </template>
                                    </div>
                                    <template if:true={lineField.attributes.editable}>
                                        <lightning-input
                                            if:true={lineField.attributes.isText}
                                            value={lineField.value}
                                            data-line-id={line.id}
                                            data-field-name={lineField.attributes.name}
                                            onchange={handleReservationLineInputChange}
                                            type="text"
                                            required={lineField.attributes.required}
                                            variant="label-hidden"
                                        ></lightning-input>
                                        <lightning-combobox
                                            if:true={lineField.attributes.isPicklist}
                                            value={lineField.value}
                                            data-line-id={line.id}
                                            data-field-name={lineField.attributes.name}
                                            options={lineField.attributes.options}
                                            onchange={handleReservationLineInputChange}
                                            variant="label-hidden"
                                            required={lineField.attributes.required}
                                        ></lightning-combobox>
                                    </template>
                                </td>
                            </template>
                        </tr>
                    </template>
                    <tr>
                        <td colspan={approveAllColSpanBefore}></td>
                        <td class="hm-text-align-center hm-no-padding">
                            <button
                                class="slds-button slds-button_outline-brand slds-m-vertical_small"
                                onclick={handleApproveAllClick}
                            >
                                {labels.hmApproveAllBtn}
                            </button>
                        </td>
                        <td colspan={approveAllColSpanAfter}></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="slds-grid slds-p-top_small slds-size_3-of-3 slds-grid_align-end slds-m-bottom_medium">
            <button onclick={handleSendUpdateClick} class="slds-button slds-button_brand slds-col">
                {labels.hmApproveSaveSendUpdateBtn}
            </button>
        </div>
    </template>
    <template if:true={isThereReadonlyLines}>
        <h3 class="slds-m-bottom_medium slds-text-heading_small">{labels.hmAcknowledgedReservations}</h3>
        <div class="slds-grid slds-scrollable">
            <table class="slds-table slds-table_cell-buffer slds-table_bordered slds-col">
                <thead>
                    <tr class="slds-line-height_reset">
                        <th>
                            <div class="slds-truncate" title={labels.hmTableLineHeader}>{labels.hmTableLineHeader}</div>
                        </th>
                        <template for:each={tableColumns} for:item="column">
                            <th key={column.name} scope="col">
                                <div class="slds-truncate" title={column.label}>{column.label}</div>
                            </th>
                        </template>
                    </tr>
                </thead>
                <tbody>
                    <template for:each={readonlyReservationLines} for:item="line">
                        <tr key={line.id} class="slds-hint-parent">
                            <td>
                                <div class="slds-truncate" title={line.number}>{line.number}</div>
                            </td>
                            <template for:each={line.fields} for:item="lineField">
                                <td key={lineField.attributes.name} data-label={lineField.attributes.label}>
                                    <div class="slds-truncate" title={lineField.value}>{lineField.value}</div>
                                </td>
                            </template>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </template>
</template>
