@IsTest
public with sharing class cep_UploadApplicationImagesCtrlTest {
    @TestSetup
    static void setup() {
        cep_TestUtils.createSettings();
        createAdminUser();
        createLeadAdvisorUser();
    }

    @Future
    private static void createAdminUser() {
        cep_TestUtils.createAdminUser();
    }

    @Future
    private static void createLeadAdvisorUser() {
        System.runAs(cep_TestUtils.getAdmin()) {
            Account acc = new Account(Name = 'Main');
            insert acc;
            Contact leadAdvisorContact = new Contact(
                FirstName = 'Tom',
                LastName = 'Smith',
                Email = '<EMAIL>',
                AccountId = acc.Id,
                PortalRole__c = 'Lead Advisor'
            );
            insert leadAdvisorContact;
            PortalUser.create(leadAdvisorContact);
        }
    }

    @IsTest
    static void updateAppImageTest() {
        List<ContentDocument> updatedCd = new List<ContentDocument>();
        Id chapterId;
        Id cdId;
        cep_UploadApplicationImagesService.FileInfo fileInfo = new cep_UploadApplicationImagesService.FileInfo();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c();
            insert cep;
            chapterId = cep.Id;
            ContentVersion cv = new ContentVersion(
                Title = 'Test',
                PathOnClient = '/file.txt',
                ContentLocation = 'S',
                VersionData = EncodingUtil.base64Decode('Test Body')
            );
            insert cv;
            cdId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id].ContentDocumentId;
            fileInfo.contentVersionId = cv.Id;
            fileInfo.contentDocumentId = cdId;
            fileInfo.skillTitle = System.Label.cepPersonalSkillActivityPhoto;
            fileInfo.description = 'Description test';
        }
        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            cep_UploadApplicationImagesCtrl.updateAppImage(chapterId, JSON.serialize(fileInfo));
            updatedCd = [SELECT Id, Title, Description FROM ContentDocument WHERE Id = :cdId];
        }
        Test.stopTest();
        Assert.areEqual(1, updatedCd.size(), 'Wrong content document link list size');
        Assert.areEqual(System.Label.cepPersonalSkillActivityPhoto, updatedCd[0].Title, 'Wrong content document description');
        Assert.areEqual('Description test', updatedCd[0].Description, 'Wrong content document description');
    }

    @IsTest
    static void updateAppImageErrorTest() {
        Boolean isError = false;
        Test.startTest();
        try {
            cep_UploadApplicationImagesCtrl.updateAppImage(null, null);
        } catch (Exception e) {
            isError = true;
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong isError value');
    }

    @IsTest
    static void getAppImagesInfoTest() {
        String dayBefore = DateTime.now().addDays(-2).format('dd/MM');
        String dayAfter = DateTime.now().addDays(2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayBefore + '-' + dayAfter);
        Id chapterId;
        User leadAdvisor = [SELECT Id, ContactId, AccountId FROM User WHERE Email = '<EMAIL>'];
        cep_UploadApplicationImagesService.ImagesInfo info = new cep_UploadApplicationImagesService.ImagesInfo();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                ChapterPresident__c = leadAdvisor.ContactId,
                NameOfSchool__c = leadAdvisor.AccountId
            );
            insert cep;
            chapterId = cep.Id;
            ContentVersion cv = new ContentVersion(
                Title = System.Label.cepPersonalSkillActivityPhoto,
                PathOnClient = '/file.txt',
                ContentLocation = 'S',
                VersionData = EncodingUtil.base64Decode('Test Body')
            );
            insert cv;
            Id cdId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id].ContentDocumentId;
            ContentDocumentLink cdl = new ContentDocumentLink(
                LinkedEntityId = chapterId,
                ContentDocumentId = cdId,
                ShareType = 'I',
                Visibility = 'AllUsers'
            );
            insert cdl;
        }
        Test.startTest();
        System.runAs(leadAdvisor) {
            info = cep_UploadApplicationImagesCtrl.getAppImagesInfo(chapterId);
        }
        Test.stopTest();
        Assert.areEqual(1, info.skillNameToActivityPhoto.size(), 'Wrong skillNameToActivityPhoto map size');
        Assert.areEqual(
            System.Label.cepPersonalSkillActivityPhoto,
            info.skillNameToActivityPhoto.get(System.Label.cepPersonalSkillActivityPhoto).Title,
            'Wrong content document title'
        );
    }

    @IsTest
    static void getAppImagesInfoErrorTest() {
        String dayBefore = DateTime.now().addDays(-2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayBefore);
        User leadAdvisor = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Boolean isError = false;
        Test.startTest();
        System.runAs(leadAdvisor) {
            try {
                cep_UploadApplicationImagesCtrl.getAppImagesInfo(null);
            } catch(Exception e) {
                isError = true;
            }
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong isError value');
    }
    
    @IsTest
    static void deleteAppImageTest() {
        List<ContentVersion> contentVersions = new List<ContentVersion>();
        Test.startTest();
        ContentVersion cv = new ContentVersion(
            Title = System.Label.cepPersonalSkillActivityPhoto,
            PathOnClient = '/file.txt',
            ContentLocation = 'S',
            VersionData = EncodingUtil.base64Decode('Test Body')
        );
        insert cv;
        cep_UploadApplicationImagesCtrl.deleteAppImage(cv.Id);
        contentVersions = [SELECT Id FROM ContentVersion WHERE Id = :cv.Id];
        Test.stopTest();
        Assert.areEqual(0, contentVersions.size(), 'Wrong contentVersions list size');
    }

    @IsTest
    static void deleteAppImageErrorTest() {
        Boolean isError = false;
        Test.startTest();
        try {
            ContentVersion cv = new ContentVersion(
                Title = System.Label.cepPersonalSkillActivityPhoto,
                PathOnClient = '/file.txt',
                ContentLocation = 'S',
                VersionData = EncodingUtil.base64Decode('Test Body')
            );
            Id cdId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id].ContentDocumentId;
            delete new ContentDocument(Id = cdId);
            cep_UploadApplicationImagesCtrl.deleteAppImage(cv.Id);
        } catch (Exception e) {
            isError = true;
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong isError value');
    }
}