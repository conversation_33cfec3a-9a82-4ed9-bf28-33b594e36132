@IsTest
public class cep_ExcellenceProgramsTriggerHandlerTest {
    @TestSetup
    static void setup() {
        cep_TestUtils.createSettings();
        createAdminUser();
        createLeadAdvisorUser();
    }

    @Future
    private static void createAdminUser() {
        cep_TestUtils.createAdminUser();
    }

    @Future
    private static void createLeadAdvisorUser() {
        System.runAs(cep_TestUtils.getAdmin()) {
            Account acc = new Account(Name = 'Main');
            insert acc;
            Contact theContact = new Contact(
                FirstName = 'Tom',
                LastName = 'Smith',
                Email = '<EMAIL>',
                AccountId = acc.Id,
                PortalRole__c = 'Lead Advisor'
            );
            insert theContact;
            PortalUser.create(theContact);
        }
    }

    @IsTest
    static void shareRecordsAfterInsertTest() {
        List<ChapterExcellenceProgram__c> chapters = new List<ChapterExcellenceProgram__c>();
        User leadAdvisor = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                ChapterSecretary__c = leadAdvisor.ContactId
            );
            insert cep;
        }
        Test.startTest();
        System.runAs(leadAdvisor) {
            chapters = [
                SELECT Id 
                FROM ChapterExcellenceProgram__c
                WHERE ChapterSecretary__c = :leadAdvisor.ContactId
            ];
        }
        Test.stopTest();
        System.assertEquals(1, chapters.size(), 'Wrong chapters size');
    }

    @IsTest
    static void shareRecordsAfterUpdateTest() {
        User leadAdvisor = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        List<ChapterExcellenceProgram__c> chapters = new List<ChapterExcellenceProgram__c>();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c chapter = new ChapterExcellenceProgram__c();
            chapter.ChapterSecretary__c = leadAdvisor.ContactId;
            insert chapter;
            chapter.ChapterSecretary__c = null;
            update chapter;
        }
        Test.startTest();
        System.runAs(leadAdvisor) {
            chapters = [
                SELECT Id 
                FROM ChapterExcellenceProgram__c
                WHERE ChapterSecretary__c = :leadAdvisor.ContactId
            ];
        }
        Test.stopTest();
        System.assertEquals(0, chapters.size(), 'Wrong chapters size');
    }

    @IsTest
    static void removeRecordsAfterUpdateTest() {
        User leadAdvisor = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        List<ChapterExcellenceProgram__c> chapters = new List<ChapterExcellenceProgram__c>();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c chapter = new ChapterExcellenceProgram__c();
            chapter.ChapterSecretary__c = leadAdvisor.ContactId;
            chapter.ChapterPresident__c = leadAdvisor.ContactId;
            insert chapter;
            chapter.ChapterSecretary__c = null;
            update chapter;
        }
        Test.startTest();
        System.runAs(leadAdvisor) {
            chapters = [
                SELECT Id 
                FROM ChapterExcellenceProgram__c
                WHERE ChapterPresident__c = :leadAdvisor.ContactId
            ];
        }
        Test.stopTest();
        System.assertEquals(1, chapters.size(), 'Wrong chapters size');
    }
}