@IsTest
public class cep_RecordDetailCtrlTest {
    @TestSetup
    static void setup() {
        cep_TestUtils.createSettings();
        createAdminUser();
        createJudgeUser();
        createLeadAdvisorUser();
    }

    @Future
    private static void createAdminUser() {
        cep_TestUtils.createAdminUser();
    }

    @Future
    private static void createJudgeUser() {
        System.runAs(cep_TestUtils.getAdmin()) {
            Account acc = new Account(Name = 'Main');
            insert acc;
            Contact judgeContact = new Contact(
                FirstName = 'Tom',
                LastName = 'Smith',
                Email = '<EMAIL>',
                AccountId = acc.Id,
                PortalRole__c = 'Judge'
            );
            insert judgeContact;
            PortalUser.create(judgeContact);
        }
    }

    @Future
    private static void createLeadAdvisorUser() {
        System.runAs(cep_TestUtils.getAdmin()) {
            Account acc = new Account(Name = 'Main');
            insert acc;
            Contact leadAdvisorContact = new Contact(
                FirstName = 'Tom',
                LastName = 'Smith',
                Email = '<EMAIL>',
                AccountId = acc.Id,
                PortalRole__c = 'Lead Advisor'
            );
            insert leadAdvisorContact;
            PortalUser.create(leadAdvisorContact);
        }
    }

    @IsTest
    static void checkVisibilityTest() {
        Boolean isVisible;
        String dayBefore = DateTime.now().addDays(-1).format('dd/MM');
        String dayAfter = DateTime.now().addDays(2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayBefore + '-' + dayAfter);
        User leadAdvisor = [SELECT ContactId, AccountId FROM User WHERE Email = '<EMAIL>'];
        Test.startTest();
        System.runAs(leadAdvisor) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                ChapterAdvisor__c = leadAdvisor.ContactId,
                NameOfSchool__c = leadAdvisor.AccountId
            );
            insert cep;
            isVisible = cep_RecordDetailCtrl.checkVisibility(null);
        }
        Test.stopTest();
        Assert.areEqual(true, isVisible, 'Wrong visibility value');
    }

    @IsTest
    static void checkVisibilityErrorTest() {
        String dayBefore = DateTime.now().addDays(-1).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayBefore);
        User leadAdvisor = [SELECT Id FROM User WHERE Email = '<EMAIL>'];
        Boolean isVisible;
        Boolean isError = false;
        Test.startTest();
        System.runAs(leadAdvisor) {
            try {
                isVisible = cep_RecordDetailCtrl.checkVisibility(null);
            } catch (Exception e) {
                isError = true;
            }
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong error value');
    }

    @IsTest
    static void getFormSectionsErrorTest() {
        aclab.FormLayout form;
        Boolean isError = false;
        Test.startTest();
        try {
            cep_RecordDetailCtrl.getFormSections(null);
        } catch (Exception e) {
            isError = true;
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong error value');
    }

    @IsTest
    static void updateChapterTest() {
        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                ChapterSubmittedMembership__c = true,
                TheChapterCompletedAProgramOfWork__c = true
            );
            insert cep;
            cep_RecordDetailCtrl.updateChapter(cep);
        }
        Test.stopTest();
        ChapterExcellenceProgram__c cepAfter = [
            SELECT ChapterSubmittedMembership__c, TheChapterCompletedAProgramOfWork__c
            FROM ChapterExcellenceProgram__c
            LIMIT 1
        ];
        Assert.isTrue(cepAfter.ChapterSubmittedMembership__c, 'Invalid Value');
        Assert.isTrue(cepAfter.TheChapterCompletedAProgramOfWork__c, 'Invalid Value');
    }

    @IsTest
    static void updateChapterErrorTest() {
        Boolean isError = false;
        Test.startTest();
        try {
            cep_RecordDetailCtrl.updateChapter(null);
        } catch (Exception e) {
            isError = true;
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong error value');
    }

    @IsTest
    static void getChapterInfoTest() {
        String dayBefore = DateTime.now().addDays(-1).format('dd/MM');
        String dayAfter = DateTime.now().addDays(2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayBefore + '-' + dayAfter);
        Id recordId;
        cep_RecordDetailService.ChapterInformation info;

        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                Status__c = cep_RecordDetailService.NEW_STATUS
            );
            insert cep;
            recordId = cep.Id;
            info = cep_RecordDetailCtrl.getChapterInfo(recordId);
        }
        Test.stopTest();
        Assert.areEqual(true, info.readOnlyFields.size() > 0, 'Wrong info size value');
        Assert.areEqual(false, info.chapterRepresentPicklist.isDisabled, 'Wrong isDisabled value');
        Assert.areEqual(false, info.chapterRepresentText.isDisabled, 'Wrong isDisabled value');
        Assert.areEqual(null, info.isExpired, 'Wrong isDisabled value');
    }

    @IsTest
    static void getChapterInfoErrorTest() {
        Boolean isError = false;
        Test.startTest();
        try {
            cep_RecordDetailCtrl.getChapterInfo(null);
        } catch (Exception e) {
            isError = true;
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong error value');
    }

    @IsTest
    static void getJdugeVisibilityInfoTest() {
        String dayBefore = DateTime.now().addDays(-2).format('dd/MM');
        String dayAfter = DateTime.now().addDays(2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.RATE_DEADLINE, dayBefore + '-' + dayAfter);
        User judgeUser = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Boolean isVisible = false;
        Id recordId;
        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(Status__c = 'Ready for assessment');
            insert cep;
            recordId = cep.Id;
            cep_AssessmentJudge__c judge = new cep_AssessmentJudge__c(
                ChapterExcellenceProgram__c = recordId,
                Contact__c = judgeUser.ContactId,
                AssessmentStatus__c = 'New'
            );
            insert judge;
        }
        System.runAs(judgeUser) {
            isVisible = cep_RecordDetailCtrl.checkJudgeVisibility(recordId);
        }
        Test.stopTest();
        Assert.areEqual(true, isVisible, 'Wrong visibility value');
    }

    @IsTest
    static void getJudgeVisibilityInfoInvisibleTest() {
        String dayBefore = DateTime.now().addDays(-2).format('dd/MM');
        String dayAfter = DateTime.now().addDays(-2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.RATE_DEADLINE, dayBefore + '-' + dayAfter);
        User judgeUser = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Boolean isVisible = false;
        Id recordId;
        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(Status__c = 'Ready for assessment');
            insert cep;
            recordId = cep.Id;
            cep_AssessmentJudge__c judge = new cep_AssessmentJudge__c(
                ChapterExcellenceProgram__c = recordId,
                Contact__c = judgeUser.ContactId,
                AssessmentStatus__c = 'New'
            );
            insert judge;
        }
        System.runAs(judgeUser) {
            isVisible = cep_RecordDetailCtrl.checkJudgeVisibility(recordId);
        }
        Test.stopTest();
        Assert.areEqual(false, isVisible, 'Wrong visibility value');
    }

    @IsTest
    static void getJudgeVisibilityInfoErrorTest() {
        Boolean isError = false;
        User judgeUser = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Test.startTest();
        try {
            System.runAs(judgeUser) {
                cep_RecordDetailCtrl.checkJudgeVisibility('');
            }
        } catch (Exception e) {
            isError = true;
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong isError value');
    }

    @IsTest
    static void saveAssessmentTest() {
        User judgeUser = [SELECT Id, ContactId FROM User WHERE Email = '<EMAIL>'];
        Id recordId;
        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(Status__c = 'Ready for assessment');
            insert cep;
            recordId = cep.Id;
            cep_AssessmentJudge__c judge = new cep_AssessmentJudge__c(
                ChapterExcellenceProgram__c = recordId,
                Contact__c = judgeUser.ContactId,
                AssessmentStatus__c = 'New'
            );
            insert judge;
        }
        System.runAs(judgeUser) {
            cep_RecordDetailCtrl.saveAssessments(new List<cep_AssessmentResult__c>(), recordId);
        }
        Test.stopTest();
        cep_AssessmentJudge__c updateJudge = [
            SELECT Id, AssessmentStatus__c
            FROM cep_AssessmentJudge__c
            WHERE Contact__c = :judgeUser.ContactId
        ];
        Assert.areEqual('Done', updateJudge.AssessmentStatus__c, 'Wrong assessment status value');
    }

    @IsTest
    static void saveAssessmentErrorTest() {
        Boolean isError = false;
        Test.startTest();
        try {
            cep_RecordDetailCtrl.saveAssessments(null, null);
        } catch (Exception e) {
            isError = true;
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong isError value');
    }
}