@IsTest
public class cep_TestUtils {
    private static final String ADMIN_EMAIL = '<EMAIL>';
    private static final String COMMUNITY_USER_PROFILE_NAME = 'Customer Community Plus Login User';
    private static final String ADMIN_PS = 'cep_AdminPermissions';
    public static final String LEAD_ADVISOR_PERMISSION_SET_NAME = 'cep_LeadAdvisorPermissions';
    public static final String JUDGE_PERMISSION_SET_NAME = 'cep_JudgePermissions';
    public static final String STUDENT_PERMISSION_SET_NAME = 'cep_StudentPermissions';

    public static void createSettings() {
        insert new CoreSetting__c(
            PortalUserDefaultProfileName__c = COMMUNITY_USER_PROFILE_NAME,
            LeadAdvisorPermissionSets__c = LEAD_ADVISOR_PERMISSION_SET_NAME,
            JudgePermissionSets__c = JUDGE_PERMISSION_SET_NAME,
            StudentPermissionSets__c = STUDENT_PERMISSION_SET_NAME
        );
    }

    public static User createAdminUser() {
        Profile p = [SELECT Id FROM Profile WHERE Name = 'System Administrator'];

        UserRole role = new UserRole(DeveloperName = 'CEO_TEST', Name = 'CEO_TEST');
        insert role;

        User admin = new User(
            Alias = 'standard',
            Email = ADMIN_EMAIL,
            EmailEncodingKey = 'UTF-8',
            LastName = 'Testing',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            ProfileId = p.Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            Username = ADMIN_EMAIL,
            UserRoleId = role.Id
        );
        insert admin;

        Id permissionSetId = [SELECT Id FROM PermissionSet WHERE Name = :ADMIN_PS].Id;
        insert new PermissionSetAssignment(PermissionSetId = permissionSetId, AssigneeId = admin.Id);

        return admin;
    }

    public static User getAdmin() {
        return [SELECT Id FROM User WHERE Email = :ADMIN_EMAIL];
    }

    public static void setUpPeriodSetting(String fieldName, String formattedDate) {
        cep_Settings__c setting = new cep_Settings__c();
        setting.put(fieldName, formattedDate);

        insert setting;
    }
}