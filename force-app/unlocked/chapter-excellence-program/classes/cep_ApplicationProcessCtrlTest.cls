@IsTest
public class cep_ApplicationProcessCtrlTest {
    @TestSetup
    static void setup() {
        cep_TestUtils.createSettings();
        createAdminUser();
        createLeadAdvisorUser();
    }

    @Future
    private static void createAdminUser() {
        cep_TestUtils.createAdminUser();
    }

    @Future
    private static void createLeadAdvisorUser() {
        System.runAs(cep_TestUtils.getAdmin()) {
            Account acc = new Account(Name = 'Main');
            insert acc;
            Contact theContact = new Contact(
                FirstName = 'Tom',
                LastName = 'Smith',
                Email = '<EMAIL>',
                AccountId = acc.Id,
                PortalRole__c = 'Lead Advisor'
            );
            insert theContact;
            PortalUser.create(theContact);
        }
    }

    @IsTest
    static void checkVisibilityTest() {
        Boolean isVisible;
        String dayBefore = DateTime.now().addDays(-1).format('dd/MM');
        String dayAfter = DateTime.now().addDays(2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayBefore + '-' + dayAfter);
        User leadAdvisor = [SELECT ContactId, AccountId FROM User WHERE Email = '<EMAIL>'];
        Test.startTest();
        System.runAs(leadAdvisor) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                ChapterAdvisor__c = leadAdvisor.ContactId,
                NameOfSchool__c = leadAdvisor.AccountId
            );
            insert cep;
            isVisible = cep_ApplicationProcessCtrl.checkVisibility();
        }
        Test.stopTest();
        Assert.areEqual(true, isVisible, 'Wrong visibility value');
    }

    @IsTest
    static void checkVisibilityErrorTest() {
        Boolean isVisible;
        Boolean isError = false;
        String dayBefore = DateTime.now().addDays(-1).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayBefore);
        User leadAdvisor = [SELECT Id FROM User WHERE Email = '<EMAIL>'];
        Test.startTest();
        System.runAs(leadAdvisor) {
            try {
                ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                    ChapterAdvisor__c = leadAdvisor.ContactId,
                    NameOfSchool__c = leadAdvisor.AccountId
                );
                insert cep;
                isVisible = cep_ApplicationProcessCtrl.checkVisibility();
            } catch(Exception e) {
                isError = true;
            }
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong isError value');
    }

    @IsTest
    static void createApplicationProcessLeadTest() {
        String dayBefore = DateTime.now().addDays(-2).format('dd/MM');
        String dayAfter = DateTime.now().addDays(2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayBefore + '-' + dayAfter);
        cep_ApplicationProcessService.ProcessApplicationInfo info;
        User leadAdvisor = [SELECT Id FROM User WHERE Email = '<EMAIL>'];
        Test.startTest();
        System.runAs(leadAdvisor) {
            info = cep_ApplicationProcessCtrl.createApplicationProcess();
        }
        Test.stopTest();
        Assert.areNotEqual(null, info, 'Wrong info value');
    }

    @IsTest
    static void createApplicationProcessMemberTest() {
        String dayBefore = DateTime.now().addDays(-2).format('dd/MM');
        String dayAfter = DateTime.now().addDays(2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayBefore + '-' + dayAfter);
        cep_ApplicationProcessService.ProcessApplicationInfo info;
        User leadAdvisor = [SELECT Id, ContactId, AccountId FROM User WHERE Email = '<EMAIL>'];
        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                ChapterSecretary__c = leadAdvisor.ContactId,
                NameOfSchool__c = leadAdvisor.AccountId
            );
            insert cep;
        }
        System.runAs(leadAdvisor) {
            info = cep_ApplicationProcessCtrl.createApplicationProcess();
        }
        Test.stopTest();
        Assert.areNotEqual(null, info, 'Wrong info value');
    }

    @IsTest
    static void createApplicationProcessErrorTest() {
        String dayAfter = DateTime.now().addDays(2).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.SUBMIT_DEADLINE, dayAfter);
        Boolean isError;
        User leadAdvisor = [SELECT Id, ContactId, AccountId FROM User WHERE Email = '<EMAIL>'];
        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            ChapterExcellenceProgram__c cep = new ChapterExcellenceProgram__c(
                ChapterSecretary__c = leadAdvisor.ContactId,
                NameOfSchool__c = leadAdvisor.AccountId
            );
            insert cep;
        }
        System.runAs(leadAdvisor) {
            try {
                cep_ApplicationProcessCtrl.createApplicationProcess();
            } catch (Exception e) {
                isError = true;
            }
        }
        Test.stopTest();
        Assert.areEqual(true, isError, 'Wrong isError value');
    }
}