@IsTest
public class cep_JudgeRatingsNotificationSDTest {
    @TestSetup
    static void setup() {
        User admin = cep_TestUtils.createAdminUser();
        System.runAs(admin) {
            Contact theContact = new Contact(LastName = 'Smith', Email = '<EMAIL>');
            insert theContact;
            cep_AssessmentJudge__c judgeRating1 = new cep_AssessmentJudge__c(
                Contact__c = theContact.Id,
                AssessmentStatus__c = 'New'
            );
            cep_AssessmentJudge__c judgeRating2 = new cep_AssessmentJudge__c(
                Contact__c = theContact.Id,
                AssessmentStatus__c = 'New'
            );
            insert new List<cep_AssessmentJudge__c>{ judgeRating1, judgeRating2 };
        }
    }

    @IsTest
    static void sendNotificationsScheduledJob() {
        String dayBefore = DateTime.now().addDays(-15).format('dd/MM');
        String dayAfter = DateTime.now().addDays(-14).format('dd/MM');
        cep_TestUtils.setUpPeriodSetting(cep_Constant.RATE_DEADLINE, dayBefore + '-' + dayAfter);
        Test.startTest();
        System.runAs(cep_TestUtils.getAdmin()) {
            cep_JudgeRatingsNotificationScheduler batchClassInstance = new cep_JudgeRatingsNotificationScheduler();
            batchClassInstance.execute(null);
        }
        Test.stopTest();
        List<cep_AssessmentJudge__c> judgeRatings = [
            SELECT Id
            FROM cep_AssessmentJudge__c
            WHERE AssessmentStatus__c = 'New'
        ];
        Assert.areEqual(2, judgeRatings.size(), 'The wrong number');
    }
}