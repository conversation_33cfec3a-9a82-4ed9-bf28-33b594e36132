public class cep_JudgeNotificationBatch implements Database.Batchable<SObject> {
    private Id orgWideEmailId;
    private String query;

    public cep_JudgeNotificationBatch() {
        initializeNotificationSettings();
        buildQuery();
    }

    private void initializeNotificationSettings() {
        orgWideEmailId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;
    }

    private void buildQuery() {
        query = 'SELECT Id, Name, Contact__c, ChapterExcellenceProgram__c, AssessmentStatus__c FROM cep_AssessmentJudge__c WHERE AssessmentStatus__c = \'New\'';
    }

    public Database.QueryLocator start(Database.BatchableContext BC) {
        return Database.getQueryLocator(query);
    }

    public void execute(Database.BatchableContext BC, List<sObject> scope) {
        sendNotifications(groupJudgeRatings(scope));
    }

    private Map<Id, List<cep_AssessmentJudge__c>> groupJudgeRatings(List<cep_AssessmentJudge__c> judgeRatings) {
        Map<Id, List<cep_AssessmentJudge__c>> groupedRatings = new Map<Id, List<cep_AssessmentJudge__c>>();

        for (cep_AssessmentJudge__c judgeRating : judgeRatings) {
            if (!groupedRatings.containsKey(judgeRating.Contact__c)) {
                groupedRatings.put(judgeRating.Contact__c, new List<cep_AssessmentJudge__c>{ judgeRating });
            } else {
                groupedRatings.get(judgeRating.Contact__c).add(judgeRating);
            }
        }
        return groupedRatings;
    }

    private void sendNotifications(Map<Id, List<cep_AssessmentJudge__c>> judgeRatings) {
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();

        for (Id key : judgeRatings.keySet()) {
            emails.add(generateEmail(key, judgeRatings.get(key)));
        }

        Messaging.sendEmail(emails, false);
    }

    private Messaging.SingleEmailMessage generateEmail(Id judgeId, List<cep_AssessmentJudge__c> judgeRatings) {
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setTargetObjectId(judgeId);
        if (String.isNotBlank(orgWideEmailId)) {
            mail.setOrgWideEmailAddressId(orgWideEmailId);
        }
        mail.setSubject(System.Label.cepJudgeNotificationTitle);

        String body = System.Label.cepJudgeNotificationBody + ' \n\n';

        for (cep_AssessmentJudge__c rating : judgeRatings) {
            String recordLink = URL.getOrgDomainURL().toExternalForm() + '/' + rating.ChapterExcellenceProgram__c;
            body += recordLink + '\n';
        }
        mail.setPlainTextBody(body);
        return mail;
    }

    public void finish(Database.BatchableContext BC) {
    }
}