public with sharing class cep_JudgeRatingsNotificationScheduler implements Schedulable {
    private static final Integer BATCH_SIZE = 2000;

    public void execute(SchedulableContext context) {
        cep_ChapterService.DateInfo dateInfo = cep_ChapterService.generateDateInfo(cep_Constant.RATE_DEADLINE);
        Date targetDate = Date.newInstance(
            dateInfo.currentDate.year(),
            dateInfo.endDate.month(),
            dateInfo.endDate.day()
        );

        Boolean isToday14DaysBefore = dateInfo.currentDate.addDays(-14) == targetDate;
        Boolean isToday11DaysBefore = dateInfo.currentDate.addDays(-11) == targetDate;
        Boolean isToday9DaysBefore = dateInfo.currentDate.addDays(-9) == targetDate;
        Boolean isToday4DaysBefore = dateInfo.currentDate.addDays(-4) == targetDate;

        if (isToday14DaysBefore || isToday11DaysBefore || isToday9DaysBefore || isToday4DaysBefore) {
            Database.executeBatch(new cep_JudgeNotificationBatch(), BATCH_SIZE);
        }
    }
}