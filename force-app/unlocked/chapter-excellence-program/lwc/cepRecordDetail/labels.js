import cepAllSectionProgramAdvisorsAreSubmittedLabel from '@salesforce/label/c.cepAllSectionProgramAdvisorsAreSubmittedLabel';
import cepTheChapterConductedWellPlannedLabel from '@salesforce/label/c.cepTheChapterConductedWellPlannedLabel';
import cepTheChapterCompletedAProjectedBudgetLabel from '@salesforce/label/c.cepTheChapterCompletedAProjectedBudgetLabel';
import cepTheChapterCompletedAProgramOfWorkLabel from '@salesforce/label/c.cepTheChapterCompletedAProgramOfWorkLabel';
import cepPersonalSkillLabel from '@salesforce/label/c.cepPersonalSkillLabel';
import cepWorkplaceSkillLabel from '@salesforce/label/c.cepWorkplaceSkillLabel';
import cepTechnicalSkillLabel from '@salesforce/label/c.cepTechnicalSkillLabel';
import cepCelebratedSkillsUSAWeekLabel from '@salesforce/label/c.cepCelebratedSkillsUSAWeekLabel';
import cepChapterAwardsProgramIsLabel from '@salesforce/label/c.cepChapterAwardsProgramIsLabel';
import cepChapterMembersAttendedOneActivityLabel from '@salesforce/label/c.cepChapterMembersAttendedOneActivityLabel';
import cepConductedActivityToEngageBusinessLabel from '@salesforce/label/c.cepConductedActivityToEngageBusinessLabel';
import cepConductedChapterRecruitmentActivityLabel from '@salesforce/label/c.cepConductedChapterRecruitmentActivityLabel';
import cepFrameworkEssentialElementGoals1Label from '@salesforce/label/c.cepFrameworkEssentialElementGoals1Label';
import cepFrameworkEssentialElementGoals2Label from '@salesforce/label/c.cepFrameworkEssentialElementGoals2Label';
import cepFrameworkEssentialElementGoals3Label from '@salesforce/label/c.cepFrameworkEssentialElementGoals3Label';
import cepFrameworkEssentialElementOutcome1Label from '@salesforce/label/c.cepFrameworkEssentialElementOutcome1Label';
import cepFrameworkEssentialElementOutcome2Label from '@salesforce/label/c.cepFrameworkEssentialElementOutcome2Label';
import cepFrameworkEssentialElementOutcome3Label from '@salesforce/label/c.cepFrameworkEssentialElementOutcome3Label';
import cepHeldExecutiveCommitteeMeetingsLabel from '@salesforce/label/c.cepHeldExecutiveCommitteeMeetingsLabel';
import cepHeldSkillsUSALocalLeadershipAreaLabel from '@salesforce/label/c.cepHeldSkillsUSALocalLeadershipAreaLabel';
import cepHeldSkillsUSALocalTechnicalAreaLabel from '@salesforce/label/c.cepHeldSkillsUSALocalTechnicalAreaLabel';
import cepLocalChapterHasSocialMediaLabel from '@salesforce/label/c.cepLocalChapterHasSocialMediaLabel';
import cepMembersAreEngagedLabel from '@salesforce/label/c.cepMembersAreEngagedLabel';
import cepParticipateInCareerEssentialsLabel from '@salesforce/label/c.cepParticipateInCareerEssentialsLabel';
import cepOneOrMoreArticlesWerePublishedLabel from '@salesforce/label/c.cepOneOrMoreArticlesWerePublishedLabel';
import cepPlanToParticipateInSkillsUSASigningLabel from '@salesforce/label/c.cepPlanToParticipateInSkillsUSASigningLabel';
import cepPlanToparticipateInStateLeadershipLabel from '@salesforce/label/c.cepPlanToparticipateInStateLeadershipLabel';
import cepReportChapterActivitiesLabel from '@salesforce/label/c.cepReportChapterActivitiesLabel';
import cepChapterExcellenceSuccessUpdateMessage from '@salesforce/label/c.cepChapterExcellenceSuccessUpdateMessage';
import cepChapterInformationSectionLabel from '@salesforce/label/c.cepChapterInformationSectionLabel';
import cepWhatWasTheEssentialElement1Label from '@salesforce/label/c.cepWhatWasTheEssentialElement1Label';
import cepWhatWasTheEssentialElement2Label from '@salesforce/label/c.cepWhatWasTheEssentialElement2Label';
import cepWhatWasTheEssentialElement3Label from '@salesforce/label/c.cepWhatWasTheEssentialElement3Label';
import cepWhatWereThreeGoalsOfTheActivity1Label from '@salesforce/label/c.cepWhatWereThreeGoalsOfTheActivity1Label';
import cepWhatWereThreeGoalsOfTheActivity2Label from '@salesforce/label/c.cepWhatWereThreeGoalsOfTheActivity2Label';
import cepWhatWereThreeGoalsOfTheActivity3Label from '@salesforce/label/c.cepWhatWereThreeGoalsOfTheActivity3Label';
import cepWhichFrameworkElementsApply1Label from '@salesforce/label/c.cepWhichFrameworkElementsApply1Label';
import cepWhichFrameworkElementsApply2Label from '@salesforce/label/c.cepWhichFrameworkElementsApply2Label';
import cepWhichFrameworkElementsApply3Label from '@salesforce/label/c.cepWhichFrameworkElementsApply3Label';
import cepStudentsAttendedFallLeadershipLabel from '@salesforce/label/c.cepStudentsAttendedFallLeadershipLabel';
import cepStudentsAreSkillsUSAMembersLabel from '@salesforce/label/c.cepStudentsAreSkillsUSAMembersLabel';
import cepPersonalSkillActivityPhoto from '@salesforce/label/c.cepPersonalSkillActivityPhoto';
import cepWorkplaceSkillActivityPhoto from '@salesforce/label/c.cepWorkplaceSkillActivityPhoto';
import cepTechnicalSkillActivityPhoto from '@salesforce/label/c.cepTechnicalSkillActivityPhoto';
import cepAssessmentPersonalSkills from '@salesforce/label/c.cepAssessmentPersonalSkills';
import cepAssessmentWorkplaceSkills from '@salesforce/label/c.cepAssessmentWorkplaceSkills';
import cepAssessmentTechnicalSkills from '@salesforce/label/c.cepAssessmentTechnicalSkills';
import cepJudgeAssessmentSubmitButtonLabel from '@salesforce/label/c.cepJudgeAssessmentSubmitButtonLabel';
import cepAssessmentSuccessToastMessage from '@salesforce/label/c.cepAssessmentSuccessToastMessage';
import cepAssessmentTotalPointsColumn from '@salesforce/label/c.cepAssessmentTotalPointsColumn';
import cepStudentSubmitConfirmationMsg from '@salesforce/label/c.cepStudentSubmitConfirmationMsg';
import cepLeadAdvisorSubmitConfirmationMsg from '@salesforce/label/c.cepLeadAdvisorSubmitConfirmationMsg';

export default {
    cepAssessmentTotalPointsColumn,
    cepAllSectionProgramAdvisorsAreSubmittedLabel,
    cepTheChapterConductedWellPlannedLabel,
    cepTheChapterCompletedAProjectedBudgetLabel,
    cepTheChapterCompletedAProgramOfWorkLabel,
    cepPersonalSkillLabel,
    cepWorkplaceSkillLabel,
    cepTechnicalSkillLabel,
    cepCelebratedSkillsUSAWeekLabel,
    cepChapterAwardsProgramIsLabel,
    cepChapterMembersAttendedOneActivityLabel,
    cepConductedActivityToEngageBusinessLabel,
    cepConductedChapterRecruitmentActivityLabel,
    cepFrameworkEssentialElementGoals1Label,
    cepFrameworkEssentialElementGoals2Label,
    cepFrameworkEssentialElementGoals3Label,
    cepFrameworkEssentialElementOutcome1Label,
    cepFrameworkEssentialElementOutcome2Label,
    cepFrameworkEssentialElementOutcome3Label,
    cepHeldExecutiveCommitteeMeetingsLabel,
    cepHeldSkillsUSALocalLeadershipAreaLabel,
    cepHeldSkillsUSALocalTechnicalAreaLabel,
    cepLocalChapterHasSocialMediaLabel,
    cepMembersAreEngagedLabel,
    cepParticipateInCareerEssentialsLabel,
    cepOneOrMoreArticlesWerePublishedLabel,
    cepPlanToParticipateInSkillsUSASigningLabel,
    cepPlanToparticipateInStateLeadershipLabel,
    cepReportChapterActivitiesLabel,
    cepChapterExcellenceSuccessUpdateMessage,
    cepChapterInformationSectionLabel,
    cepWhatWasTheEssentialElement1Label,
    cepWhatWasTheEssentialElement2Label,
    cepWhatWasTheEssentialElement3Label,
    cepWhatWereThreeGoalsOfTheActivity1Label,
    cepWhatWereThreeGoalsOfTheActivity2Label,
    cepWhatWereThreeGoalsOfTheActivity3Label,
    cepWhichFrameworkElementsApply1Label,
    cepWhichFrameworkElementsApply2Label,
    cepWhichFrameworkElementsApply3Label,
    cepStudentsAttendedFallLeadershipLabel,
    cepStudentsAreSkillsUSAMembersLabel,
    cepPersonalSkillActivityPhoto,
    cepWorkplaceSkillActivityPhoto,
    cepTechnicalSkillActivityPhoto,
    cepAssessmentPersonalSkills,
    cepAssessmentWorkplaceSkills,
    cepAssessmentTechnicalSkills,
    cepJudgeAssessmentSubmitButtonLabel,
    cepAssessmentSuccessToastMessage,
    cepStudentSubmitConfirmationMsg,
    cepLeadAdvisorSubmitConfirmationMsg
};
