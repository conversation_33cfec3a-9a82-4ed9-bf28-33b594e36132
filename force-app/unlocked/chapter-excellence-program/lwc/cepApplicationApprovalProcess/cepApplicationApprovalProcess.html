<template>
    <lightning-spinner if:true={isLoading} alternative-text="Loading" size="medium"></lightning-spinner>
    <div if:true={isVisibleComponent} class="app-approval-process-container">
        <div if:true={visibilityInfo.isVisibleRejectButton} class="reject-container">
            <lightning-input
                class="reject-comment-input"
                placeholder={labels.cepApprovalProcessRejectPlaceholder}
                type="text"
                variant="label-hidden"
            >
            </lightning-input>

            <button
                class="slds-button slds-button_destructive reject-btn"
                title={labels.cepApprovalProcessReject}
                onclick={handleClickDecline}
            >
                {labels.cepApprovalProcessReject}
            </button>
        </div>

        <button
            class="slds-button slds-button_success accept-btn"
            title={labels.cepApprovalProcessAccept}
            onclick={handleClickApprove}
        >
            {labels.cepApprovalProcessAccept}
        </button>
    </div>

    <c-cep-modal-confirmation if:true={isOpenConfirmationModal} onmodalstatuschange={handleChangeModalStatus}>
        <h3 slot="body">{confirmationMessage}</h3>
    </c-cep-modal-confirmation>
</template>
