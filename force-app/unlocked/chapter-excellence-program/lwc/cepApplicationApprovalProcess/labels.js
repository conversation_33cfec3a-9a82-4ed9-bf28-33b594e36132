import cepApprovalProcessAccept from '@salesforce/label/c.cepApprovalProcessAccept';
import cepApprovalProcessReject from '@salesforce/label/c.cepApprovalProcessReject';
import cepApprovalProcessRejectPlaceholder from '@salesforce/label/c.cepApprovalProcessRejectPlaceholder';
import cepApprovalProcessSuccessToastMessage from '@salesforce/label/c.cepApprovalProcessSuccessToastMessage';
import cepApprovalProcessSuccessToastTitle from '@salesforce/label/c.cepApprovalProcessSuccessToastTitle';
import cepApprovalProcessRejectCustomValidity from '@salesforce/label/c.cepApprovalProcessRejectCustomValidity';
import cepLeadAdvisorApproveConfirmationMsg from '@salesforce/label/c.cepLeadAdvisorApproveConfirmationMsg';
import cepLeadAdvisorRejectConfirmationMsg from '@salesforce/label/c.cepLeadAdvisorRejectConfirmationMsg';
import cepStudentApproveConfirmationMsg from '@salesforce/label/c.cepStudentApproveConfirmationMsg';

export default {
    cepApprovalProcessAccept,
    cepApprovalProcessReject,
    cepApprovalProcessRejectPlaceholder,
    cepApprovalProcessSuccessToastMessage,
    cepApprovalProcessSuccessToastTitle,
    cepApprovalProcessRejectCustomValidity,
    cepLeadAdvisorApproveConfirmationMsg,
    cepLeadAdvisorRejectConfirmationMsg,
    cepStudentApproveConfirmationMsg
};
