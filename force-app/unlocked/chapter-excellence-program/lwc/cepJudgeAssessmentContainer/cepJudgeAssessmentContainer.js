import { LightningElement, api } from 'lwc';
import ASSESSMENT_OBJECT from '@salesforce/schema/cep_AssessmentResult__c';

export default class CepJudgeAssessmentContainer extends LightningElement {
    @api comboboxes;
    assessments = { ...ASSESSMENT_OBJECT.fields };
    defaultTotalPoints = 0;

    @api getAssessments() {
        return this.assessments;
    }

    @api checkValidation() {
        const comboboxes = this.template.querySelectorAll('lightning-combobox');
        let isValid = true;

        comboboxes.forEach((combobox) => {
            if (!combobox.reportValidity()) {
                isValid = false;
            }
        });

        return isValid;
    }

    handleChangeNumber(event) {
        const target = event.target;
        const value = this.calculateWeightArea(target.value, target.dataset.weightArea);
        this.assessments[target.dataset.name] = value;
        this.template.querySelector(`.assessment-total-point[data-name=${target.dataset.name}]`).textContent = value;

        this.sendTotalPointsEvent();
    }

    calculateWeightArea(value, weightArea) {
        return value >= 0 ? value * weightArea : 0;
    }

    sendTotalPointsEvent() {
        this.dispatchEvent(new CustomEvent('changetotalpoints'));
    }
}
