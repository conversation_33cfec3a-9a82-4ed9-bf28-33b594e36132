<template>
  <template lwc:if={overviewInfo}>
    <div class="slds-grid slds-wrap slds-gutters">
      <div class="slds-col slds-medium-size_2-of-3 slds-small-size_1-of-1">
        <div class="slds-text-heading_medium slds-m-bottom_medium">{labels.headerLabel}</div>
        <div class="slds-grid slds-wrap slds-gutters slds-grid_vertical-stretch">
          <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2 slds-m-bottom_medium">
            <div>
              <span class="slds-text-title_bold">{labels.clusterLabel}</span>
              <lightning-badge
                  label={overviewInfo.cluster}
                  class="slds-badge_lightest slds-m-left_medium slds-p-vertical_none"
              >
              </lightning-badge>
            </div>
            <div class="slds-m-top_medium">
              <div class="slds-text-title_bold">{labels.descriptionLabel}</div>
              <div>{overviewInfo.description}</div>
            </div>
            <template if:true={canUpdateCompetitionDescription}>
              <div class="slds-m-top_medium">
                <lightning-button
                    disabled={isReadOnly}
                    stretch
                    variant="neutral"
                    label={labels.updateDescriptionButtonLabel}
                    onclick={updateDescription}
                ></lightning-button>
              </div>
            </template>
          </div>
          <div
              class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2 slds-grid slds-grid_vertical slds-grid_align-spread slds-m-bottom_medium"
          >
            <div>
              <div class="slds-text-title_bold">{labels.technicalStandardsLabel}</div>
              <div>{overviewInfo.technicalStandardsDescription}</div>
            </div>
            <div class="slds-m-top_medium">
              <lightning-button
                  stretch
                  variant="brand"
                  icon-name="utility:new_window"
                  icon-position="right"
                  label={labels.technicalStandardsButtonLabel}
                  disabled={disableTechnicalStandardsButton}
                  onclick={openExternalLink}
              ></lightning-button>
            </div>
          </div>
        </div>
      </div>

      <div class="slds-col slds-medium-size_1-of-3 slds-small-size_1-of-1">
        <div class="slds-text-heading_medium slds-m-bottom_medium">{labels.logisticHeader}</div>
        <div>
          <template if:true={overviewInfo.buildingPlanLink}>
            <div>
              <div class="slds-text-title_bold">{labels.buildingPlanLabel}:</div>
              <a href={overviewInfo.buildingPlanLink} target="_blank">
                <lightning-badge
                    label={labels.downloadPlanLabel}
                    icon-name="utility:download"
                    icon-position="end"
                    class="slds-button_stretch slds-badge_lightest"
                ></lightning-badge>
              </a>
            </div>
          </template>

          <template if:true={overviewInfo.competitionLocation.address}>
            <div class="slds-m-top_small">
              <div class="slds-text-title_bold">{labels.locationLabel}:</div>
              <a
                  data-latitude={overviewInfo.competitionLocation.latitude}
                  data-longitude={overviewInfo.competitionLocation.longitude}
                  onclick={openMap}
              >
                <lightning-badge
                    label={overviewInfo.competitionLocation.address}
                    icon-name="utility:checkin"
                    icon-position="end"
                    class="slds-button_stretch slds-truncate"
                ></lightning-badge>
              </a>
            </div>
          </template>

          <template if:true={overviewInfo.competitionTime}>
            <div class="slds-m-top_small">
              <div class="slds-text-title_bold">{labels.timeLabel}:</div>
              <span class="slds-badge slds-theme_success slds-button_stretch">
                  <lightning-formatted-date-time
                      value={overviewInfo.competitionTime}
                      year="2-digit"
                      month="short"
                      day="2-digit"
                      hour="2-digit"
                      minute="2-digit"
                      time-zone-name="short"
                      time-zone="America/Chicago"
                  ></lightning-formatted-date-time>
              </span>
            </div>
          </template>

          <template if:true={overviewInfo.briefingLocation.address}>
            <div class="slds-m-top_small">
              <div class="slds-text-title_bold">{labels.briefingLocationLabel}:</div>
              <a
                  data-latitude={overviewInfo.briefingLocation.latitude}
                  data-longitude={overviewInfo.briefingLocation.longitude}
                  onclick={openMap}
              >
                <lightning-badge
                    label={overviewInfo.briefingLocation.address}
                    icon-name="utility:checkin"
                    icon-position="end"
                    class="slds-button_stretch slds-truncate"
                ></lightning-badge>
              </a>
            </div>
          </template>

          <template if:true={overviewInfo.briefingTime}>
            <div class="slds-m-top_small">
              <div class="slds-text-title_bold">{labels.briefingTimeLabel}:</div>
              <span class="slds-badge slds-theme_success slds-button_stretch">
                            <lightning-formatted-date-time
                                value={overviewInfo.briefingTime}
                                year="2-digit"
                                month="short"
                                day="2-digit"
                                hour="2-digit"
                                minute="2-digit"
                                time-zone-name="short"
                                time-zone="America/Chicago"
                            ></lightning-formatted-date-time>
                        </span>
            </div>
          </template>
        </div>
      </div>
    </div>
  </template>
</template>
