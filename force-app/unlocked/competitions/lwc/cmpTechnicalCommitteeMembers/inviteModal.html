<template>
    <lightning-modal-header label="Invite Judge to the Competition"></lightning-modal-header>
    <lightning-modal-body>
        <div class="slds-is-relative">
            <!--        {labels.cmCompetitionTechnicalCommitteeLinkDescription}-->
            <lightning-spinner if:true={isLoading} alternative-text="Loading" size="medium"></lightning-spinner>
            <div>
                Invite registered users under your School or Company: <br/>
                <div class="slds-m-bottom_small">
                    <lightning-record-picker
                        label="Users"
                        placeholder="Search User..."
                        object-api-name="Contact"
                        display-info={displayInfo}
                        filter={filter}
                        onchange={lookupChange}
                    ></lightning-record-picker>
                </div>
                <lightning-button
                    class="slds-m-top_medium"
                    disabled={isInviteButtonDisabled}
                    label="Invite"
                    icon-name="utility:adduser"
                    icon-position="right"
                    onclick={handleInviteButtonClick}
                ></lightning-button>
            </div>

            <div class="slds-p-top_medium">
                If you didn't find relevant user, please share this link with who will be in attendance: <br/>
                <lightning-input
                    class="slds-m-bottom_small"
                    label="Email"
                    type="email"
                    placeholder="Email"
                    onchange={emailChange}
                ></lightning-input>
                <lightning-button
                    disabled={isInviteByEmailButtonDisabled}
                    label="Invite by Email"
                    icon-name="utility:email"
                    icon-position="right"
                    onclick={handleInviteByEmailButtonClick}
                ></lightning-button>
            </div>
        </div>
    </lightning-modal-body>
</template>
