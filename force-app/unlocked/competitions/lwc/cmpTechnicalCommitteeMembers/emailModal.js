import LightningModal from "lightning/modal";
import { api } from "lwc";
import { showErrors } from "c/errorHandler";
import LightningConfirm from "lightning/confirm";
import sendEmail from "@salesforce/apex/cmp_TechnicalCommitteeMembersCtrl.sendEmail"
import { ShowToastEvent } from "lightning/platformShowToastEvent";

export default class extends LightningModal {
    @api competitionId;
    isLoading = false;
    subject = '';
    emailMessage = '';

    get disableSendEmail() {
        return !(this.subject && this.emailMessage) || this.isLoading;
    }

    changeSubject(event) {
        this.subject = event.target.value;
    }

    changeEmailMessage(event) {
        this.emailMessage = event.target.value;
    }

    async sendEmail() {
        const isConfirmed = await LightningConfirm.open({
            label: 'Are you sure you want to send email message to all judges?'
        });
        if (isConfirmed) {

            console.log('>>>', JSON.stringify({ competitionId: this.competitionId, subject: this.subject, body: this.emailMessage }));


            this.isLoading = true;
            sendEmail({ competitionId: this.competitionId, subject: this.subject, body: this.emailMessage })
                .then(() => {
                    const event = new ShowToastEvent({
                        title: 'Success!',
                        message: 'Email sent',
                        variant: 'success'
                    });
                    this.dispatchEvent(event);
                    this.close("canceled");
                })
                .catch((error) => {
                    showErrors(this, error);
                })
                .finally(() => {
                    this.isLoading = false;
                })
        }
    }
}