<template>
    <div class="slds-grid slds-wrap slds-gutters">
        <div class="slds-col">
            <div class="slds-text-heading_medium slds-m-bottom_medium">{labels.timelineLabel}:</div>
            <ul class="slds-timeline">
                <template if:false={timelines}>
                    <div class="slds-text-heading_medium slds-text-color_weak slds-p-top_medium">
                        {labels.cmCompetitionNoRecordsFound}
                    </div>
                </template>

                <template for:each={timelines} for:item="timeline">
                    <li key={timeline.Id}>
                        <div class={timeline.className}>
                            <div class="slds-media">
                                <div class="slds-media__figure">
                                    <lightning-icon
                                        icon-name={timeline.icon}
                                        size="small"
                                        class="slds-timeline__icon"
                                    ></lightning-icon>
                                </div>
                                <div class="slds-media__body">
                                    <div class="slds-grid slds-wrap slds-gutters">
                                        <div class="slds-col slds-medium-size_2-of-3 slds-small-size_1-of-1">
                                            <div class="slds-grid slds-grid_align-spread slds-m-bottom_x-small">
                                                <div class="slds-grid slds-grid_vertical-align-center slds-no-space">
                                                    <div class="slds-text-title_bold">
                                                        <lightning-formatted-date-time
                                                            value={timeline.deadline}
                                                            month="short"
                                                            day="2-digit"
                                                            time-zone-name="short"
                                                            time-zone="America/Chicago"
                                                        ></lightning-formatted-date-time>
                                                        &nbsp;| {timeline.name}
                                                    </div>
                                                </div>
                                                <div
                                                    class="slds-timeline__actions slds-grid_vertical-align-start slds-shrink-none"
                                                >
                                                    <template lwc:if={timeline.isOpen}>
                                                        <lightning-badge
                                                            label={labels.openDeadlineLabel}
                                                            class="slds-badge_lightest slds-p-vertical_none"
                                                        ></lightning-badge>
                                                    </template>
                                                    <template lwc:else>
                                                        <lightning-badge
                                                            label={labels.passedDeadlineLabel}
                                                            class="slds-p-vertical_none"
                                                        ></lightning-badge>
                                                    </template>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="slds-grid slds-wrap slds-gutters">
                                            <div class="slds-col slds-medium-size_2-of-3 slds-small-size_1-of-1">
                                                <article
                                                    class="slds-box slds-timeline__item_details slds-theme_shade slds-p-around_medium slds-m-bottom_medium"
                                                >
                                                    <lightning-formatted-rich-text
                                                        value={timeline.description}
                                                    ></lightning-formatted-rich-text>
                                                </article>
                                            </div>
                                            <div
                                                class="slds-col slds-medium-size_1-of-3 slds-small-size_1-of-1 slds-m-bottom_medium"
                                            >
                                                <template for:each={timeline.actions} for:item="action">
                                                    <div key={action.label} class="slds-p-bottom_medium">
                                                        <template lwc:if={action.isButton}>
                                                            <div lwc:if={action.isReadOnly}>
                                                                <lightning-button
                                                                    disabled="true"
                                                                    stretch
                                                                    variant={action.buttonVariant}
                                                                    icon-name="utility:new_window"
                                                                    icon-position="right"
                                                                    label={action.text}
                                                                ></lightning-button>
                                                            </div>
                                                            <a lwc:else href={action.buttonLink} target="_blank">
                                                                <lightning-button
                                                                    stretch
                                                                    variant={action.buttonVariant}
                                                                    icon-name="utility:new_window"
                                                                    icon-position="right"
                                                                    label={action.text}
                                                                ></lightning-button>
                                                            </a>
                                                        </template>
                                                        <template lwc:if={action.isText}> {action.text} </template>
                                                        <template lwc:if={action.isPrizeInformation}>
                                                            <div class="slds-m-bottom_medium">
                                                                <template lwc:if={isPrizeInfoSubmitted}>
                                                                    <div class="slds-notify_container slds-is-relative">
                                                                        <div
                                                                            class="slds-notify slds-notify_toast slds-theme_success"
                                                                        >
                                                                            <lightning-icon
                                                                                icon-name="utility:success"
                                                                                size="x-small"
                                                                                variant="inverse"
                                                                                class="slds-m-right_x-small"
                                                                            ></lightning-icon>
                                                                            <div class="slds-notify__content">
                                                                                <h2 class="slds-text-heading_small">
                                                                                    {labels.cmCompetitionDetailsTimelinePrizeInfoSubmitted}
                                                                                </h2>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </template>

                                                                <template lwc:else>
                                                                    <div class="slds-notify_container slds-is-relative">
                                                                        <div
                                                                            class="slds-notify slds-notify_toast slds-theme_error"
                                                                        >
                                                                            <lightning-icon
                                                                                icon-name="utility:error"
                                                                                size="x-small"
                                                                                variant="inverse"
                                                                                class="slds-m-right_x-small"
                                                                            ></lightning-icon>
                                                                            <div class="slds-notify__content">
                                                                                <h2 class="slds-text-heading_small">
                                                                                    {labels.cmCompetitionDetailsTimelinePrizeInfoNotSubmitted}
                                                                                </h2>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </template>
                                                            </div>

                                                            <lightning-button
                                                                stretch
                                                                disabled={isPrizeButtonDisabled}
                                                                variant={action.buttonVariant}
                                                                icon-position="right"
                                                                label={action.text}
                                                                onclick={updatePrizeInfo}
                                                            ></lightning-button>
                                                        </template>
                                                        <template lwc:if={action.isUploadFile}>
                                                            <template lwc:if={action.theLastFileUrl}>
                                                                <a href={action.theLastFileUrl} target="_blank">
                                                                    <lightning-button
                                                                        stretch
                                                                        icon-position="right"
                                                                        icon-name="utility:download"
                                                                        label="Download File"></lightning-button>
                                                                </a>
                                                            </template>

                                                            <lightning-file-upload
                                                                disabled={action.isReadOnly}
                                                                label="Attach File"
                                                                name="fileUploader"
                                                                accept={acceptedFormats}
                                                                record-id={action.timelineId}
                                                                onuploadfinished={handleUploadFinished}
                                                            ></lightning-file-upload>
                                                        </template>
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </template>
            </ul>
        </div>
    </div>
</template>
