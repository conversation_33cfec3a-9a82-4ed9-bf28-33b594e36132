import { api, LightningElement } from "lwc";
import LightningConfirm from "lightning/confirm";
import { showErrors } from "c/errorHandler";
import approveYourselfAsJudge from "@salesforce/apex/cmp_CompetitionJudgeCtrl.approveYourselfAsJudge";
import hasPendingJudge from "@salesforce/apex/cmp_CompetitionJudgeCtrl.hasPendingJudge";

export default class extends LightningElement {
    @api recordId;
    showComponent = false;
    isLoading = false;

    connectedCallback() {
        hasPendingJudge({competitionId: this.recordId})
            .then(response => {
                this.showComponent = response;
            })
            .catch(error => {
                showErrors(this, error);
            });
    }

    async approve() {
        const isConfirmed = await LightningConfirm.open({
            label: 'Are you sure you want to approve yourself as judge?'
        });

        if (isConfirmed) {
            this.approveAction(true);
        }
    }

    async reject() {
        const isConfirmed = await LightningConfirm.open({
            label: 'Are you sure you want to reject yourself as judge?',
            theme: 'error'
        });
        if (isConfirmed) {
            this.approveAction(false);
        }
    }

    approveAction(isApproved) {
        this.isLoading = true;
        approveYourselfAsJudge({competitionId: this.recordId, isApproved})
            .then(response => { // response = true if approved, response = false if rejected
                if (response) {
                    window.location.reload();
                } else {
                    window.location = '/s/';
                }
            })
            .catch(error => {
                showErrors(this, error);
            })
            .finally(() => {
                this.isLoading = false;
            })
    }

}