import { api, LightningElement } from "lwc";
import getCompetitionDetails from '@salesforce/apex/cmp_OverviewCtrl.getCompetitionDetails'
import EMAIL_MODAL from "./modal";
import labels from "./labels";
import { showErrors } from "c/errorHandler";

export default class extends LightningElement {
    @api recordId;
    @api isReadOnly = false;
    chairpersons;
    contacts = [];
    labels = labels;

    connectedCallback() {
        getCompetitionDetails({recordId: this.recordId})
            .then((result) => {
                let contactPersonOne = {
                    name: result.ContactPerson1__r?.Name,
                    email: result.ContactPerson1__r?.Email
                };
                let contactPersonTwo = {
                    name: result.ContactPerson2__r?.Name,
                    email: result.ContactPerson2__r?.Email
                };

                if (contactPersonOne.name) {
                    this.contacts.push(contactPersonOne);
                }
                if (contactPersonTwo.name) {
                    this.contacts.push(contactPersonTwo);
                }

                // Create data for chairpersons tiles
                if(result.competition.CompetitionChairpersons__r) {
                    this.chairpersons = result.competition.CompetitionChairpersons__r.map(el => {
                        return {
                            name: el.Chairperson__r.Name,
                            email: el.Chairperson__r.Email,
                            phone: el.Chairperson__r.Phone,
                            accountName: el.Chairperson__r.Account.Name,
                        }
                    })
                }
            })
            .catch(error => {
                showErrors(this, error)
            });
    }

    async sendEmail(event) {
        await EMAIL_MODAL.open({
            size: 'small',
            contactName: event.currentTarget.dataset.contactName,
            contactEmail: event.currentTarget.dataset.contactEmail,
        });
    }
}
