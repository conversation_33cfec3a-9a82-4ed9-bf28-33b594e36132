<template>

  <div class="slds-grid slds-wrap slds-gutters">
    <div class="slds-col slds-medium-size_2-of-3 slds-small-size_1-of-1">
      <div class="slds-text-heading_medium slds-m-bottom_medium">{labels.cmChairpersonComponentTitle}</div>
      <template if:false={chairpersons}>
        <div class="slds-text-align_center">
          <div class="slds-text-heading_medium slds-text-color_weak slds-p-top_xx-large">{labels.cmCompetitionNoRecordsFound}</div>
        </div>
      </template>

      <div class="slds-grid slds-nowrap slds-scrollable_x">
        <template for:each={chairpersons} for:item="chairPerson">
          <div class="person-card slds-m-right_medium" key={chairPerson.contactId}>
            <lightning-card
                title={chairPerson.name}
                icon-name="utility:user">
              <dl class="slds-p-horizontal_medium">
                <dt class="slds-text-color_weak">Email:</dt>
                <dd class="slds-m-bottom_x-small">{chairPerson.email}</dd>
                <dt class="slds-text-color_weak">Phone:</dt>
                <dd class="slds-m-bottom_x-small">{chairPerson.phone}</dd>
                <dt class="slds-text-color_weak">Associated with:</dt>
                <dd>{chairPerson.accountName}</dd>
              </dl>
            </lightning-card>
          </div>
        </template>
      </div>
    </div>
    <div class="slds-col slds-medium-size_1-of-3 slds-small-size_1-of-1">
      <div class="slds-text-heading_medium slds-m-bottom_medium">&nbsp;</div>

      <template for:each={contacts} for:item="contact">
        <div key={contact.email} class="slds-m-bottom_medium">
          <div class="slds-m-bottom_x-small slds-truncate">
            <span class="slds-text-title_bold">{contact.name} </span>
            <span class="slds-text-color_weak"> ({contact.email})</span>
          </div>
          <lightning-button
              stretch
              label="Send Email"
              icon-name="action:email"
              icon-position="left"
              variant="neutral"
              data-contact-name={contact.name}
              data-contact-email={contact.email}
              onclick={sendEmail}
          ></lightning-button>
        </div>
      </template>
    </div>
  </div>

</template>
