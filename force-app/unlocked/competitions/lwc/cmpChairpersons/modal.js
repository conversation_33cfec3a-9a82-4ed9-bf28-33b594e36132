import { api } from "lwc";
import { showErrors } from "c/errorHandler";
import LightningModal from "lightning/modal";
import sendEmail from "@salesforce/apex/cmp_ChairpersonsCtrl.sendEmail";
import LightningConfirm from "lightning/confirm";
import labels from "./labels";
import { ShowToastEvent } from "lightning/platformShowToastEvent";

export default class extends LightningModal {
    @api contactName;
    @api contactEmail;
    subject;
    emailMessage;

    get labels() {
        return labels;
    }

    get modalLabel() {
        return this.labels.cmChairpersonEmailModalHeader.replace("{userName}", this.contactName);
    }

    get disableSendEmail() {
        return !(this.subject && this.emailMessage);
    }

    changeSubject(event) {
        this.subject = event.target.value;
    }

    changeEmailMessage(event) {
        this.emailMessage = event.target.value;
    }

    async sendEmail() {
        const isConfirmed = await LightningConfirm.open({
            label: this.labels.cmChairpersonEmailConfirmMessage
        });
        if (isConfirmed) {
            sendEmail({ email: this.contactEmail, subject: this.subject, body: this.emailMessage })
                .then(() => {
                    const event = new ShowToastEvent({
                        title: 'Success',
                        message: 'Operation completed',
                        variant: 'success'
                    });
                    this.dispatchEvent(event);
                    this.close("canceled");
                })
                .catch((error) => {
                    showErrors(this, error);
                });
        }
    }

}
