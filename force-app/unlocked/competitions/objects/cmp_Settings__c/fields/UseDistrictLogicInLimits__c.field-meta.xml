<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>UseDistrictLogicInLimits__c</fullName>
    <defaultValue>false</defaultValue>
    <description>If True the logic to use limits according to the competition type (State or District) is skipped. The same limits will be applied for different types of competition. [false for Illinois, true for Massachusetts]</description>
    <externalId>false</externalId>
    <inlineHelpText>If True the logic to use limits according to the competition type (State or District) is skipped. The same limits will be applied for different types of competition. [false for Illinois, true for Massachusetts]</inlineHelpText>
    <label>Use District Logic In Limits</label>
    <trackTrending>false</trackTrending>
    <type>Checkbox</type>
</CustomField>
