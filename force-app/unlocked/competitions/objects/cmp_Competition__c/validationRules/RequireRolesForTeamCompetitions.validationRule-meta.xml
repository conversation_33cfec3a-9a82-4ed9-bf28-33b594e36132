<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>RequireRolesForTeamCompetitions</fullName>
    <active>true</active>
    <description>Do not leave the Roles field blank if the competition is marked as a team competition.</description>
    <errorConditionFormula>AND(
  ISBLANK(TEXT(TeamRoles__c)), 
  TEXT(TeamCompetitionType__c) = &apos;ROLES&apos;,
  IsTeamCompetition__c
)</errorConditionFormula>
    <errorDisplayField>TeamRoles__c</errorDisplayField>
    <errorMessage>The Roles field is required if the Competition is marked as a Team competition and Team Competition Type equals to ROLES.</errorMessage>
</ValidationRule>
