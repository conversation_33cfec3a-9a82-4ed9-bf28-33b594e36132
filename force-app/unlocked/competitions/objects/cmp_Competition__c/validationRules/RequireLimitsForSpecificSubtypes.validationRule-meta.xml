<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>RequireLimitsForSpecificSubtypes</fullName>
    <active>true</active>
    <errorConditionFormula>AND(
  $Setup.cmp_Settings__c.EnableLimitsForCompetitions__c,
  OR(
    UPPER( TEXT( Subtype__c ) ) = &apos;SIMPLE&apos;,
    UPPER( TEXT( Subtype__c ) ) = &apos;DISTRICT QUALIFIERS&apos;,
    UPPER( TEXT( Subtype__c ) ) = &apos;FRESHMAN ONLY&apos;,
    UPPER( TEXT( Subtype__c ) ) = &apos;BY INVITATION ONLY&apos;
  ),
  
  OR(
     ISBLANK( LimitOfCompetitors__c )
  )
)</errorConditionFormula>
    <errorDisplayField>LimitOfCompetitors__c</errorDisplayField>
    <errorMessage>The Limit Of Competitors is required if the subtype of competition is marked as &apos;SIMPLE&apos; or  &apos;FRESHMAN ONLY&apos; or &apos;FRESHMAN ONLY&apos; or &apos;BY INVITATION ONLY&apos;.</errorMessage>
</ValidationRule>
