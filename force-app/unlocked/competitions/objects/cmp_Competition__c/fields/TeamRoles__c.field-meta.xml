<?xml version="1.0" encoding="UTF-8" ?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>TeamRoles__c</fullName>
    <description>TODO</description>
    <label>Team Roles</label>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>A B</fullName>
                <default>false</default>
                <label>A and B</label>
            </value>
            <value>
                <fullName>A B D</fullName>
                <default>false</default>
                <label>A, B and D</label>
            </value>
            <value>
                <fullName>A B C</fullName>
                <default>false</default>
                <label>A, B and C</label>
            </value>
            <value>
                <fullName>A B C D</fullName>
                <default>false</default>
                <label>A, B, C and D</label>
            </value>
            <value>
                <fullName>A D C D</fullName>
                <default>false</default>
                <label>A, D, C and D</label>
            </value>
            <value>
                <fullName>A B C D E</fullName>
                <default>false</default>
                <label>A, B, C, D, and E</label>
            </value>
            <value>
                <fullName>A B C D E F</fullName>
                <default>false</default>
                <label>A, B, C, D, E and F</label>
            </value>
            <value>
                <fullName>A B C D E F G</fullName>
                <default>false</default>
                <label>A, B, C, D, E, F and G</label>
            </value>
            <value>
                <fullName>C E M P</fullName>
                <default>false</default>
                <label>C, E, M, and P</label>
            </value>
            <value>
                <fullName>C M</fullName>
                <default>false</default>
                <label>Contestant, Model</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
