<?xml version="1.0" encoding="UTF-8" ?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>CompetitionCluster__c</fullName>
    <description>TO DO</description>
    <label>Competition Cluster</label>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Architecture and Construction</fullName>
                <default>false</default>
                <label>Architecture and Construction</label>
            </value>
            <value>
                <fullName>Arts, A/V Technology, and Communications/Marketing</fullName>
                <default>false</default>
                <label>Arts, A/V Technology, and Communications/Marketing</label>
            </value>
            <value>
                <fullName>Education and Training</fullName>
                <default>false</default>
                <label>Education and Training</label>
            </value>
            <value>
                <fullName>Health Sciences</fullName>
                <default>false</default>
                <label>Health Sciences</label>
            </value>
            <value>
                <fullName>Hospitality and Tourism/Human Services</fullName>
                <default>false</default>
                <label>Hospitality and Tourism/Human Services</label>
            </value>
            <value>
                <fullName>I.T., Business Management, and Administration/Finance</fullName>
                <default>false</default>
                <label>I.T., Business Management, and Administration/Finance</label>
            </value>
            <value>
                <fullName>Law, Public Safety, Corrections and Safety</fullName>
                <default>false</default>
                <label>Law, Public Safety, Corrections and Safety</label>
            </value>
            <value>
                <fullName>Leadership</fullName>
                <default>false</default>
                <label>Leadership</label>
            </value>
            <value>
                <fullName>Manufacturing/STEM</fullName>
                <default>false</default>
                <label>Manufacturing/STEM</label>
            </value>
            <value>
                <fullName>Transportation, Distribution, and Logistics</fullName>
                <default>false</default>
                <label>Transportation, Distribution, and Logistics</label>
            </value>
            <value>
                <fullName>ARTS, A/V TECHNOLOGY AND COMMUNICATIONS/MARKETING</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>ARTS, A/V TECHNOLOGY AND COMMUNICATIONS/MARKETING</label>
            </value>
            <value>
                <fullName>HOSPITALITY AND TOURISM/ HUMAN SERVICES</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>HOSPITALITY AND TOURISM/ HUMAN SERVICES</label>
            </value>
            <value>
                <fullName>INFORMATION TECHNOLOGY/ BUSINESS MANAGEMENT AND ADMINISTRATION/FINANCE</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>INFORMATION TECHNOLOGY/ BUSINESS MANAGEMENT AND ADMINISTRATION/FINANCE</label>
            </value>
            <value>
                <fullName>LAW, PUBLIC SAFETY, CORRECTIONS AND SECURITY</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>LAW, PUBLIC SAFETY, CORRECTIONS AND SECURITY</label>
            </value>
            <value>
                <fullName>TRANSPORTATION, DISTRIBUTION AND LOGISTICS</fullName>
                <default>false</default>
                <isActive>false</isActive>
                <label>TRANSPORTATION, DISTRIBUTION AND LOGISTICS</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
