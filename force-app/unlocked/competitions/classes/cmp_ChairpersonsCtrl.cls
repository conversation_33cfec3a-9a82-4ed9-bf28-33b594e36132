public with sharing class cmp_ChairpersonsCtrl {

    @AuraEnabled(Cacheable=true)
    public static cmp_Service.CompetitionDTO getCompetitionDetails(Id recordId) {
        try {
            return cmp_Service.getCompetitionDetails(recordId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static void sendEmail(String email, String subject, String body) {
        try {
            String orgWideId = (String) CoreSetting__c.getOrgDefaults().OrgWideEmailId__c;
            Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
            mail.setOrgWideEmailAddressId(orgWideId);
            mail.setToAddresses(new List<String>{ email });
            mail.setSubject(subject);
            mail.setHtmlBody(body);
            Messaging.sendEmail(new List<Messaging.Email>{ mail });
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

}