public with sharing class cmp_CompetitionListCtrl {

    @AuraEnabled(Cacheable=false)
    public static List<cmp_Competition__c> getAvailableCompetitions(Id conferenceId, String role) {
        try {
            Boolean hasTCRole = PortalUser.hasRole(PortalUser.Role.TECHNICAL_CHAIR);
            Boolean hasJudgeRole = PortalUser.hasRole(PortalUser.Role.JUDGE);
            Boolean hasLeadAdvisorRole = PortalUser.hasRole(PortalUser.Role.LEAD_ADVISOR);
            User currentUser = [SELECT ContactId, Contact.Email FROM User WHERE Id = :UserInfo.getUserId()];

            if (!hasTCRole && !hasJudgeRole && !hasLeadAdvisorRole) {
                return new List<cmp_Competition__c>();
            }

            if (hasLeadAdvisorRole && role == 'Lead Advisor') {
                return new WSHelper().getCompetitionsByAccountIdForCompetitors(PortalUser.getAccountId(), conferenceId);
            }

            Set<Id> competitionIds = new Set<Id>();
            List<cmp_Judge__c> judges = new WSHelper().getJudges(currentUser.ContactId, currentUser.Contact.Email, 'Rejected', conferenceId);
            for (cmp_Judge__c judge: judges) {
                competitionIds.add(judge.Competition__c);
            }

            List<cmp_Chairperson__c> TCs = new WSHelper().getChairpersons(currentUser.ContactId, conferenceId);
            for (cmp_Chairperson__c TC: TCs) {
                competitionIds.add(TC.Competition__c);
            }

            return new WSHelper().getCompetitionsByIds(competitionIds);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    without sharing class WSHelper {
        public List<cmp_Competition__c> getCompetitionsByAccountIdForCompetitors(Id accountId, Id conferenceId) {
            List<cmp_Participant__c> participants = [
                    SELECT Id, Name, Competition__c
                    FROM cmp_Participant__c
                    WHERE Participant__r.AccountId = :accountId
                    AND Competition__r.Conference__c = :conferenceId
                    AND Competition__r.IsTeamCompetition__c = TRUE
            ];

            Set<Id> competitionIds = new Set<Id>();
            for (cmp_Participant__c participant: participants) {
                competitionIds.add(participant.Competition__c);
            }

            return [
                    SELECT Id, Name, Division__c, Type__c, IsTeamCompetition__c, CompetitionTime__c
                    FROM cmp_Competition__c
                    WHERE Id IN :competitionIds
            ];
        }

        public List<cmp_Competition__c> getCompetitionsByIds(Set<Id> competitionIds) {
            return [
                    SELECT Id, Name, Division__c, Type__c, IsTeamCompetition__c, CompetitionTime__c
                    FROM cmp_Competition__c
                    WHERE Id IN :competitionIds
            ];
        }

        public List<cmp_Judge__c> getJudges(Id contactId, String email, String status, Id conferenceId) {
            return [
                    SELECT Id, Competition__c
                    FROM cmp_Judge__c
                    WHERE (Contact__c = :contactId OR InvitedByEmail__c = :email)
                    AND ApprovalStatus__c != :status
                    AND Competition__r.Conference__c = :conferenceId
            ];
        }

        public List<cmp_Chairperson__c> getChairpersons(Id contactId, Id conferenceId) {
            return [
                    SELECT Id, Competition__c
                    FROM cmp_Chairperson__c
                    WHERE Chairperson__c = :contactId
                    AND Competition__r.Conference__c = :conferenceId
            ];
        }


    }

}