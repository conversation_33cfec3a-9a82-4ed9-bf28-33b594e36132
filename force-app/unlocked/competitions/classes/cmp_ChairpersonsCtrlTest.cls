@IsTest
private class cmp_ChairpersonsCtrlTest {
    @TestSetup
    static void setup() {
        cm_TestUtils.insertSettings();
        createAdmin();
        createRecords();
    }

    @Future
    static void createAdmin() {
        cm_TestUtils.createAdminUser();
    }

    @Future
    static void createRecords() {
        System.runAs(cm_TestUtils.getAdmin()) {
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(leadAdvisor);
        }
    }

    @IsTest
    static void sendGetCompetitionDetailsFail() {
        User admin = cm_TestUtils.getAdmin();
        System.runAs(admin) {
            Test.startTest();
            {
                try {
                    cmp_ChairpersonsCtrl.getCompetitionDetails('00x000000000000');
                } catch (Exception e) {
                }

            }
            Test.stopTest();
        }
    }

    @IsTest
    static void sendEmailSuccess() {
        User admin = cm_TestUtils.getAdmin();
        Integer emailsSent;
        System.runAs(admin) {
            Contact theContact = [SELECT Id, Email FROM Contact LIMIT 1];
            Test.startTest();
            {
                cmp_ChairpersonsCtrl.sendEmail(theContact.Email, 'Test Subject', 'Test Body');
                emailsSent = Limits.getEmailInvocations();
            }
            Test.stopTest();
        }
        Assert.areEqual(1, emailsSent, 'One Email must be sent');
    }
}