@IsTest
private class cmp_ServiceTest {

    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact LA = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            Contact judge = cm_TestUtils.createContact(theAccount.Id, 'Judge');
            Contact TC = cm_TestUtils.createContact(theAccount.Id, 'Technical Chair');

            PortalUser.create(LA);
            PortalUser.create(judge);
            PortalUser.create(TC);

            Contact student = cm_TestUtils.createContact(theAccount.Id, 'Student');
            PortalUser.create(student);

            cm_Conference__c conference = new cm_Conference__c(
                    Name = 'Test Conference',
                    IsCompetitionsAvailable__c = true
            );
            insert conference;

            cmp_Competition__c competition = new cmp_Competition__c(
                    Name = 'First',
                    Conference__c = conference.Id,
                    Division__c = 'Middle School'
            );
            insert competition;

            cm_Participant__c cmParticipant = new cm_Participant__c(
                    Conference__c = conference.Id,
                    Contact__c = student.Id,
                    RegistrationStatus__c = 'Approved'
            );
            insert cmParticipant;

            cmp_Participant__c participant = new cmp_Participant__c(
                    Competition__c = competition.Id,
                    Participant__c = student.Id,
                    ConferenceParticipant__c = cmParticipant.Id
            );
            insert participant;

        }
    }

    @IsTest
    static void recordNotFoundFail() {
        Boolean hasError = false;

        Test.startTest();
        {
            System.runAs(cm_TestUtils.getUserByRole('Judge')) {
                try {
                    cmp_Service.getCompetitionDetails('00x000000000000');
                } catch (Exception e) {
                    hasError = true;
                }
            }
        }
        Test.stopTest();

        Assert.isTrue(hasError, 'Error must exist');
    }

    @IsTest
    static void userHasNoSpecifiedRoleFail() {
        Boolean hasError = false;
        String errorMessage = '';

        Test.startTest();
        {
            try {
                cmp_Service.getCompetitionDetails('00x000000000000');
            } catch (Exception e) {
                hasError = true;
                errorMessage = e.getMessage();
            }
        }
        Test.stopTest();

        Assert.isTrue(hasError, 'Error must exist');
        Assert.isTrue(errorMessage.contains(cmp_Service.JUDGE_OR_TC_NOT_FOUND), 'Wrong error message');
    }

    @IsTest
    static void userIsNotInvitedAsJudgeOrTCFail() {
        Boolean hasError = false;
        String errorMessage = '';
        cmp_Competition__c competition = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First' LIMIT 1];

        Test.startTest();
        {
            System.runAs(cm_TestUtils.getUserByRole('Judge')) {
                try {
                    cmp_Service.getCompetitionDetails(competition.Id);
                } catch (Exception e) {
                    hasError = true;
                    errorMessage = e.getMessage();
                }
            }
        }
        Test.stopTest();

        Assert.isTrue(hasError, 'Error must exist');
        Assert.isTrue(errorMessage.contains(cmp_Service.JUDGE_OR_TC_NOT_INVITED), 'Wrong error message');
    }

    @IsTest
    static void getCompetitionDetailsSuccess() {
        User judge = cm_TestUtils.getUserByRole('Judge');
        User TC = cm_TestUtils.getUserByRole('Technical Chair');

        cmp_Competition__c competition = [SELECT Id FROM cmp_Competition__c WHERE Name = 'First' LIMIT 1];
        insert new cmp_Judge__c(Competition__c = competition.Id, Contact__c = judge.ContactId);
        insert new cmp_Chairperson__c(Competition__c = competition.Id, Chairperson__c = TC.ContactId);

        cmp_Service.CompetitionDTO response1 = null;
        cmp_Service.CompetitionDTO response2 = null;

        Test.startTest();
        {
            System.runAs(judge) {
                response1 = cmp_Service.getCompetitionDetails(competition.Id);
            }
            System.runAs(TC) {
                response2 = cmp_Service.getCompetitionDetails(competition.Id);
            }
        }
        Test.stopTest();

        Assert.isNotNull(response1, 'Response must not be null');
        Assert.areEqual(competition.Id, response1.competition.Id, 'Wrong competition id');

        Assert.isNotNull(response2, 'Response must not be null');
        Assert.areEqual(competition.Id, response2.competition.Id, 'Wrong competition id');
    }

    @IsTest
    static void getCompetitionMembers() {
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c WHERE Name = 'Test Conference' LIMIT 1];

        Test.startTest();
        {
            System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
                cmp_Service.getCompetitionMembers(conference.Id, true);
            }
        }
        Test.stopTest();
    }
}