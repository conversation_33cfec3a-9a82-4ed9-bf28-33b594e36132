@IsTest
public class cm_CompanyRegistrationCtrlTest {
    @TestSetup
    static void setup() {
        System.runAs(cm_TestUtils.createAdminUser()) {
            cm_TestUtils.insertSettings();
            Account theAccount = cm_TestUtils.createAccount('Acme');
            Contact leadAdvisor = cm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(leadAdvisor);

            cm_Conference__c conference = new cm_Conference__c(Name = 'Test Conference', IsCompetitionsAvailable__c = true);
            insert conference;
        }
    }

    @IsTest
    static void getForm() {
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c LIMIT 1];

        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                try {
                    cm_CompanyRegistrationCtrl.getForm(conference.Id);
                } catch (Exception e) {
                    // Layout not found, expected error because layout is not in the package
                }

            }
            Test.stopTest();
        }
    }

    @IsTest
    static void testSaveRecord() {
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c LIMIT 1];

        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            Test.startTest();
            {
                try {
                    cm_Exhibitor__c record = new cm_Exhibitor__c();
                    cm_CompanyRegistrationCtrl.saveRecord(record, conference.Id, '1111');
                } catch (Exception e) {
                    // Layout not found, expected error because layout is not in the package
                }

            }
            Test.stopTest();
        }
    }

    @IsTest
    static void testGetPredefinedValues() {
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c LIMIT 1];

        System.runAs(cm_TestUtils.getUserByRole('Lead Advisor')) {
            Test.startTest();
            {
                cm_CompanyRegistrationCtrl.getPredefinedValues(conference.Id);
                try {
                } catch (Exception e) {
                    // Layout not found, expected error because layout is not in the package
                }

            }
            Test.stopTest();
        }
    }

    @IsTest
    static void getConferenceExhibitorIdNotExist() {
        Account theAccount = [SELECT Id FROM Account LIMIT 1];
        cm_Conference__c conference = [SELECT Id FROM cm_Conference__c LIMIT 1];
        Id conferenceExhibitorId;

        System.runAs(cm_TestUtils.getAdmin()) {
            Test.startTest();
            {
                conferenceExhibitorId = cm_CompanyRegistrationCtrl.getConferenceExhibitorId(conference.Id);
            }
            Test.stopTest();
        }

        Assert.areEqual(null, conferenceExhibitorId, 'Conference Exhibitor Id does not exist');
    }

    @IsTest
    static void getConferenceExhibitorIdExist() {
        Account theAccount = [SELECT Id FROM Account LIMIT 1];
        Id conferenceExhibitorId;

        System.runAs(cm_TestUtils.getAdmin()) {
            cm_Conference__c conference = [SELECT Id FROM cm_Conference__c LIMIT 1];

            insert new cm_Exhibitor__c(
                    OrganizationName__c = 'Test',
                    OrganizationType__c = 'Industry Partner or Exhibitor',
                    OnSiteContactFirstName__c = 'Joel',
                    OnSiteContactLastName__c = 'Smith',
                    Email__c = '<EMAIL>',
                    Phone__c = '7777',
                    AddressStreetLineOne__c = 'Test street',
                    AddressStreetLineTwo__c = 'Test street',
                    AddressCity__c = 'Test City',
                    AddressPostalCode__c = '777',
                    AddressState__c = 'Test state',
                    IndustryClusters__c = 'Health Sciences',
                    BoothSize__c = 'Single',
                    NumberOfAdditionalTables__c = '1',
                    ElectricalDrop__c = 'No',
                    NeedPrize__c = 'Yes',
                    Conference__c = conference.Id,
                    Account__c = theAccount.Id
            );

            Test.startTest();
            {
                conferenceExhibitorId = cm_CompanyRegistrationCtrl.getConferenceExhibitorId(conference.Id);
            }
            Test.stopTest();
        }

        Assert.areNotEqual(null, conferenceExhibitorId, 'Conference Exhibitor Id must exist');
    }

    @IsTest
    static void getConferenceExhibitorIdError() {
        Id wrongId = '00x000000000000';
        System.runAs(cm_TestUtils.getAdmin()) {
            Id exhibitorId = cm_CompanyRegistrationCtrl.getConferenceExhibitorId(wrongId);
            Assert.isNull(exhibitorId, 'Record id must be null for wrong id ');
        }
    }
}