import { api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { showErrors } from 'c/errorHandler';
import LightningModal from 'lightning/modal';
import getForm from '@salesforce/apex/cm_CompanyRegistrationCtrl.getForm';
import getPredefinedValues from '@salesforce/apex/cm_CompanyRegistrationCtrl.getPredefinedValues';
import saveRecord from '@salesforce/apex/cm_CompanyRegistrationCtrl.saveRecord';
import labels from './labels';

export default class extends LightningModal {
    @api conferenceId;
    @api partnerAccessCode;
    @api hasRecord;
    getFormMethod;

    labels = labels;
    isLoading = true;
    isFormReady = false;

    connectedCallback() {
        this.getFormMethod = () => getForm({ conferenceId: this.conferenceId });
        this.overrides = [
            { name: 'TotalPrice__c', show: this.formMode === 'readonly' },
            { name: 'BoothNumber__c', show: this.formMode === 'readonly' },
            { name: 'PaymentStatus__c', show: this.formMode === 'readonly' },
        ];
    }

    get formMode() {
        return this.hasRecord ? 'readonly' : 'edit';
    }

    get isSubmitBtnDisabled() {
        return this.isLoading || !this.isFormReady;
    }

    formSubmitHandler(event) {
        this.isLoading = true;
        this.isFormReady = false;

        saveRecord({ record: event.detail.record, conferenceId: this.conferenceId, accessCode: this.partnerAccessCode })
            .then(() => {
                this.showSuccessToast();
            })
            .catch((error) => showErrors(this, error));
    }

    formIsReady() {
        getPredefinedValues({ conferenceId: this.conferenceId })
            .then((response) => {
                let overrides = [];

                for (const [key, value] of Object.entries(response)) {
                    overrides.push({
                        name: key,
                        value: value
                    });
                }

                this.refs.form.setOverrides(overrides);
            })
            .finally(() => {
                this.isLoading = false;
                this.isFormReady = true;
            });
    }

    handleSubmit() {
        this.refs.form.submit();
    }

    showSuccessToast() {
        const event = new ShowToastEvent({
            title: 'Success!',
            message: this.labels.cmCompanyRegistrationSuccessMessage,
            variant: 'success'
        });
        this.dispatchEvent(event);
        this.close('canceled');
    }

    handleError(event) {
        showErrors(this, event.detail.detail);
    }
}
