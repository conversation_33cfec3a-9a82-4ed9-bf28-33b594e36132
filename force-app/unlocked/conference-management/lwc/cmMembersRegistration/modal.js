import { api } from 'lwc';
import { showErrors } from 'c/errorHandler';
import LightningModal from 'lightning/modal';
import canRegisterAdvisors from '@salesforce/customPermission/cm_CanRegisterAdvisorsForAConference';
import getContacts from '@salesforce/apex/cm_MembersRegistrationCtrl.getContacts';
import labels from './labels';

export default class extends LightningModal {
    @api conferenceId;
    canRegisterAdvisors = canRegisterAdvisors;
    selectedType = 'Student';
    contacts = [];
    selectedContacts = [];
    isLoading = true;
    labels = labels;
    isReachedLimitForAdvisors;
    limitOfAdvisors;
    types = [
        { label: 'Student', value: 'Student' },
        { label: 'Lead Advisor / Advisor', value: 'Advisor' }
    ];
    searchTerm = '';
    allContacts = []; // Store all contacts

    get disableNextButton() {
        return this.selectedContacts.length === 0 || this.isLoading;
    }

    get hasInfoAlert() {
        return this.isReachedLimitForAdvisors;
    }

    get filteredContacts() {
        if (!this.searchTerm) {
            return this.contacts;
        }
        
        const searchTermLower = this.searchTerm.toLowerCase();
        return this.allContacts.filter(contact => {
            return contact.label.toLowerCase().includes(searchTermLower);
        });
    }
    
    connectedCallback() {
        this.loadData();
    }

    loadData() {
        this.isLoading = true;
        getContacts({ conferenceId: this.conferenceId, role: this.selectedType })
            .then(result => {
                this.isReachedLimitForAdvisors = result.remainingLimitOfAdvisors <= 0;
                this.limitOfAdvisors = result.limitOfAdvisors;
                if (this.isReachedLimitForAdvisors) {
                    this.types = this.types.filter(type => type.label === 'Student');
                }
                this.allContacts = result.contacts.map(item => {
                    const freshmanSuffix = item.IsFreshman__c ? ' [Freshman]' : '';
                    const portalRoleSuffix = item.PortalRole__c.includes('Middle School') ? ' [Middle School Member]' : '';
                    return {
                        label: `${item.LastName}, ${item.FirstName}${freshmanSuffix}${portalRoleSuffix}`,
                        value: item.Id
                    };
                });
                this.contacts = this.allContacts; // Initially set contacts to all contacts
            })
            .catch((error) => {
                showErrors(this, error);
            })
            .finally(() => {
                this.isLoading = false;
            });
    }

    handleNext() {
        this.close({
            openModal: true,
            selectedContacts: this.selectedContacts,
            selectedMemberType: this.selectedType,
            isStudentRegistration: this.selectedType === 'Student'
        });
    }

    handleChangeType(event) {
        this.selectedType = event.detail?.value;
        this.selectedContacts = [];
        this.loadData();
    }

    onchangeHandler(event) {
        this.selectedContacts = event.detail.value;
    }

    handleSearchChange(event) {
        this.searchTerm = event.target.value;
    }
}
