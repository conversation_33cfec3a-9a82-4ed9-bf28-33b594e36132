<?xml version="1.0" encoding="UTF-8" ?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>60.0</apiVersion>
    <isExposed>true</isExposed>
    <description>Conference Members Management</description>
    <masterLabel>[CM] Conference Members Management</masterLabel>
    <targets>
        <target>lightningCommunity__Default</target>
        <target>lightningCommunity__Page</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightningCommunity__Default">
            <property
                name="conferenceId"
                type="String"
                label="Conference Id"
                description="The unique identifier for the conference."
                required="true"
                default="{!recordId}"
            />
            <property
                name="readOnlyMode"
                type="Boolean"
                label="ReadOnlyMode"
                description="Properties indicate that the list of records is for view only."
                default="false"
            />
            <property
                name="showOnlyApproved"
                type="Boolean"
                label="Show Only Approved"
                description="Properties indicate that the list of records must include only Approved records."
                default="false"
            />
            <property
                    name="showVotingDelegateAndStateOfficer"
                    type="Boolean"
                    label="Show Voting Delegate and State Officer Candidate fields"
                    description="Show Voting Delegate and State Officer Candidate fields on participant details."
                    default="true"
            />
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>
