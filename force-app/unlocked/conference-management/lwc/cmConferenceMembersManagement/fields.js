import mailingAddress from '@salesforce/schema/Contact.MailingAddress';
import genderIdentity from '@salesforce/schema/Contact.GenderIdentity';
import division from '@salesforce/schema/Contact.Division__c';
import birthdate from '@salesforce/schema/Contact.Birthdate';
import homePhone from '@salesforce/schema/Contact.HomePhone';
import graduationYear from '@salesforce/schema/Contact.GraduationYear__c';
import mobilePhone from '@salesforce/schema/Contact.MobilePhone';
import advisor from '@salesforce/schema/Contact.Advisor__c';
import parentName from '@salesforce/schema/Contact.cm_ParentName__c';
import parentPhone from '@salesforce/schema/Contact.cm_ParentPhone__c';
import tShirtSize from '@salesforce/schema/Contact.cm_TShirtSize__c';
import addOns from '@salesforce/schema/Contact.cm_AddOns__c';
import votingDelegate from '@salesforce/schema/Contact.cm_VotingDelegate__c';
import stateOfficerCandidate from '@salesforce/schema/Contact.cm_StateOfficerCandidate__c';
import email from '@salesforce/schema/Contact.Email';
import age from '@salesforce/schema/Contact.cm_Age__c';

export default {
    mailingAddress,
    division,
    genderIdentity,
    birthdate,
    homePhone,
    graduationYear,
    mobilePhone,
    advisor,
    parentName,
    parentPhone,
    tShirtSize,
    addOns,
    votingDelegate,
    stateOfficerCandidate,
    email,
    age
};
