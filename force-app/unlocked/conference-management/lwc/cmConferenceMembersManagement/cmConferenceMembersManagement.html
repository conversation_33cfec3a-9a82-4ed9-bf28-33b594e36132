<template>
    <div class="slds-is-relative" if:true={canApproveConferenceMembers}>
        <lightning-spinner if:true={isLoading} alternative-text="Loading"></lightning-spinner>

        <div if:true={isDataLoad}>
            <div class="slds-text-heading_medium slds-p-around_x-small">Conference participants approval process:</div>

            <!-- Search functionality -->
            <div>
                <lightning-input
                    type="search"
                    label="Search by First or Last Name"
                    placeholder="Enter name to search..."
                    value={searchTerm}
                    onchange={handleSearch}
                    variant="label-hidden"
                ></lightning-input>
            </div>

            <div if:true={recordsExist}>
                <div class="overflow-x-auto">
                    <table class="slds-table slds-table_bordered slds-no-row-hover">
                        <thead>
                            <tr class="slds-text-title_caps">
                                <th if:false={readOnlyMode} style="width: 1px">
                                    <lightning-input
                                        type="checkbox"
                                        variant="label-hidden"
                                        onchange={handleSelectAll}
                                    ></lightning-input>
                                </th>
                                <template for:each={headers} for:item="header">
                                    <th
                                        key={header.id}
                                        data-id={header.id}
                                        onclick={handleSorting}
                                        class={header.class}
                                    >
                                        <a if:true={header.isSorAvailable} class="slds-text-link_reset">
                                            <span class="slds-truncate" title={header.label}>{header.label}</span>
                                            <template if:true={header.isSorted}>
                                                <lightning-icon
                                                    if:true={isAsc}
                                                    icon-name="utility:arrowup"
                                                    size="xx-small"
                                                ></lightning-icon>
                                                <lightning-icon
                                                    if:true={isDesc}
                                                    icon-name="utility:arrowdown"
                                                    size="xx-small"
                                                ></lightning-icon>
                                            </template>
                                        </a>
                                        <span
                                            if:false={header.isSorAvailable}
                                            class="slds-truncate"
                                            title={header.label}
                                            >{header.label}</span
                                        >
                                    </th>
                                </template>
                            </tr>
                        </thead>
                        <tbody>
                            <template for:each={filteredRecords} for:item="item">
                                <tr key={item.registrationRequestId}>
                                    <td if:false={readOnlyMode}>
                                        <lightning-input
                                            type="checkbox"
                                            data-id={item.registrationRequestId}
                                            checked={item.selected}
                                            onchange={handleSelect}
                                            disabled={item.readOnly}
                                        ></lightning-input>
                                    </td>
                                    <td>{item.contactName}</td>
                                    <td>{item.role}</td>
                                    <td>{item.status}</td>
                                    <td>
                                        <lightning-formatted-number
                                            value={item.fee}
                                            format-style="currency"
                                            currency-code="USD"
                                        ></lightning-formatted-number>
                                    </td>
                                    <td>
                                        {item.competitionsLabel}
                                        <ul if:true={item.showMore} class="slds-list_dotted">
                                            <template for:each={item.competitions} for:item="competition">
                                                <li key={competition}>{competition}</li>
                                            </template>
                                        </ul>
                                    </td>
                                    <td style="width: 1px" class="slds-text-align_right">
                                        <lightning-button-icon
                                            icon-name="utility:chevrondown"
                                            data-id={item.registrationRequestId}
                                            onclick={handleShowMore}
                                        ></lightning-button-icon>
                                    </td>
                                    <td if:false={readOnlyMode} class="action-column">
                                        <lightning-button
                                            variant="destructive-text"
                                            label={labels.cmConferenceMembersRejectButtonLabel}
                                            class="slds-m-left_x-small"
                                            data-id={item.registrationRequestId}
                                            disabled={item.readOnly}
                                            onclick={rejectRequest}
                                        ></lightning-button>
                                        <lightning-button
                                            variant="brand"
                                            label={labels.cmConferenceMembersApproveButtonLabel}
                                            class="slds-m-left_x-small"
                                            data-id={item.registrationRequestId}
                                            disabled={item.readOnly}
                                            onclick={approveRequest}
                                        ></lightning-button>
                                    </td>
                                </tr>
                                <template if:true={item.showMore}>
                                    <tr key={item.registrationRequestId}>
                                        <td colspan="8">
                                            <lightning-record-view-form
                                                object-api-name={objectApiName}
                                                record-id={item.contactId}
                                                density="comfy"
                                            >
                                                <div class="slds-grid slds-truncate">
                                                    <div class="slds-col slds-size_1-of-3">
                                                        <lightning-output-field
                                                            field-name={fields.school}
                                                        ></lightning-output-field>
                                                        <lightning-output-field
                                                            field-name={fields.genderIdentity}
                                                        ></lightning-output-field>
                                                        <lightning-output-field
                                                            field-name={fields.birthdate}
                                                        ></lightning-output-field>
                                                        <lightning-output-field
                                                            field-name={fields.age}
                                                        ></lightning-output-field>
                                                        <lightning-output-field
                                                            field-name={fields.graduationYear}
                                                        ></lightning-output-field>
                                                        <lightning-output-field
                                                            field-name={fields.tShirtSize}
                                                        ></lightning-output-field>
                                                        <template lwc:if={showVotingDelegateAndStateOfficer}>
                                                            <lightning-output-field
                                                                field-name={fields.votingDelegate}
                                                            ></lightning-output-field>
                                                            <lightning-output-field
                                                                field-name={fields.stateOfficerCandidate}
                                                            ></lightning-output-field>
                                                        </template>
                                                        <lightning-output-field
                                                            field-name={fields.advisor}
                                                        ></lightning-output-field>
                                                        <lightning-output-field
                                                            field-name={fields.email}
                                                        ></lightning-output-field>
                                                    </div>
                                                    <div class="slds-col slds-size_1-of-3">
                                                        <lightning-output-field
                                                            field-name={fields.mailingAddress}
                                                        ></lightning-output-field>
                                                        <lightning-output-field
                                                            field-name={fields.homePhone}
                                                        ></lightning-output-field>
                                                        <lightning-output-field
                                                            field-name={fields.mobilePhone}
                                                        ></lightning-output-field>
                                                        <lightning-output-field
                                                            field-name={fields.parentName}
                                                        ></lightning-output-field>
                                                        <lightning-output-field
                                                            field-name={fields.parentPhone}
                                                        ></lightning-output-field>
                                                        <lightning-output-field
                                                            field-name={fields.addOns}
                                                        ></lightning-output-field>
                                                    </div>
                                                    <div class="slds-col slds-size_1-of-3">
                                                        <lightning-button
                                                            data-contact-id={item.contactId}
                                                            data-is-student={item.isStudent}
                                                            onclick={openEditRegistrationModal}
                                                            label="Update participant"
                                                        ></lightning-button>
                                                    </div>
                                                </div>
                                            </lightning-record-view-form>
                                        </td>
                                    </tr>
                                </template>
                            </template>
                        </tbody>
                    </table>
                </div>
                <div class="slds-p-vertical_small">
                    <div class="slds-grid slds-grid_align-spread">
                        <div class="slds-col">
                            <span if:false={readOnlyMode} class="slds-float_left">
                                {labels.cmConferenceMembersSelectedRecordsLabel} {selectedRecords}
                            </span>
                        </div>
                        <div class="slds-col">
                            <span>{labels.cmConferenceMembersTotalFeeLabel}&nbsp;
                                <lightning-formatted-number
                                    value={totalFee}
                                    format-style="currency"
                                    currency-code="USD"
                                ></lightning-formatted-number>
                            </span>
                        </div>
                    </div>

                    <div class="slds-grid slds-p-vertical_small slds-grid_align-end">
                        <lightning-button
                            if:true={readOnlyMode}
                            variant="brand"
                            label={labels.cmConferenceMembersSendListButtonLabel}
                            class="slds-m-left_x-small"
                            onclick={sendListOfApprovedMembers}
                        ></lightning-button>
                        <lightning-button
                            if:false={readOnlyMode}
                            variant="destructive-text"
                            label={labels.cmConferenceMembersRejectButtonSelectedLabel}
                            title="Reject action"
                            class="slds-m-left_x-small"
                            onclick={rejectRequests}
                            disabled={disableMultipleActions}
                        ></lightning-button>
                        <lightning-button
                            if:false={readOnlyMode}
                            variant="brand"
                            label={labels.cmConferenceMembersApproveButtonSelectedLabel}
                            title="Approve action"
                            class="slds-m-left_x-small"
                            onclick={approveRequests}
                            disabled={disableMultipleActions}
                        ></lightning-button>
                    </div>
                </div>
            </div>

            <div if:false={recordsExist} class="slds-p-around_medium slds-illustration slds-illustration_small">
                <img src="/img/chatter/OpenRoad.svg" class="slds-illustration__svg" />
                <div class="slds-text-heading_small">
                    <h2>{labels.cmConferenceMembersRecordsNotFound}</h2>
                </div>
            </div>
        </div>
    </div>
</template>