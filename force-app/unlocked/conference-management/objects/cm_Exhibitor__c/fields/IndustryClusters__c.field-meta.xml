<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>IndustryClusters__c</fullName>
    <description>The industry clusters</description>
    <externalId>false</externalId>
    <label>Industry Clusters</label>
    <required>true</required>
    <trackTrending>false</trackTrending>
    <type>MultiselectPicklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Architecture and Construction</fullName>
                <default>false</default>
                <label>Architecture and Construction</label>
            </value>
            <value>
                <fullName>Arts, A/V Technology and Communications/Marketing</fullName>
                <default>false</default>
                <label>Arts, A/V Technology and Communications/Marketing</label>
            </value>
            <value>
                <fullName>Education and Training</fullName>
                <default>false</default>
                <label>Education and Training</label>
            </value>
            <value>
                <fullName>Health Sciences</fullName>
                <default>false</default>
                <label>Health Sciences</label>
            </value>
            <value>
                <fullName>Hospitality and Tourism/Human Services</fullName>
                <default>false</default>
                <label>Hospitality and Tourism/Human Services</label>
            </value>
            <value>
                <fullName>IT, Business Management and Administration, Finance</fullName>
                <default>false</default>
                <label>IT, Business Management and Administration, Finance</label>
            </value>
            <value>
                <fullName>Law, Public Safety, Corrections and Security</fullName>
                <default>false</default>
                <label>Law, Public Safety, Corrections and Security</label>
            </value>
            <value>
                <fullName>Leadership</fullName>
                <default>false</default>
                <label>Leadership</label>
            </value>
            <value>
                <fullName>Manufacturing/STEM</fullName>
                <default>false</default>
                <label>Manufacturing/STEM</label>
            </value>
            <value>
                <fullName>Transportation, Distribution and Logistics</fullName>
                <default>false</default>
                <label>Transportation, Distribution and Logistics</label>
            </value>
        </valueSetDefinition>
    </valueSet>
    <visibleLines>4</visibleLines>
</CustomField>
