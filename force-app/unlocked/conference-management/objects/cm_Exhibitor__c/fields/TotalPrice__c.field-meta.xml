<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>TotalPrice__c</fullName>
    <deprecated>false</deprecated>
    <description>Configurable prices for each of the related picklist values. The price will be used in invoices.</description>
    <externalId>false</externalId>
    <formula>/* Price for Organization Type */
IF(TEXT(OrganizationType__c) = &apos;Industry Partner or Exhibitor&apos;, 
  $Setup.cm_Settings__c.ExhibitorPriceOrganizationIndustry__c, 
  IF(TEXT(OrganizationType__c) = &apos;Non-Profit School or University&apos;, 
  $Setup.cm_Settings__c.ExhibitorPriceOrganizationNonProfit__c, 0)
)
* 
IF(TEXT(BoothSize__c) = &apos;Double&apos;, 2, 1) /* x2 if booth size is Double */

/* Price for Number Of Additional Tables */
+ 
IF(TEXT(NumberOfAdditionalTables__c) = &apos;3&apos;, 150, IF(TEXT(NumberOfAdditionalTables__c) = &apos;2&apos;, 100, IF(TEXT(NumberOfAdditionalTables__c) = &apos;1&apos;, 50, 0)))

/* Price for Electrical Drop */
+
IF(TEXT(ElectricalDrop__c) = &apos;Yes&apos;, 50, 0)

/* Price for Need Prize */
+
IF(TEXT(NeedPrize__c) = &apos;Yes&apos;, 50, 0)</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>Total Price</label>
    <precision>18</precision>
    <required>false</required>
    <scale>2</scale>
    <trackTrending>false</trackTrending>
    <type>Currency</type>
</CustomField>
