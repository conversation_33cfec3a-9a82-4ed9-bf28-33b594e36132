export default class CsvParser {
    delimiter;
    headers = [];
    dataRows = [];

    constructor(delimiter) {
        this.delimiter = delimiter ?? ',';
    }

    fromString(csv, hasHeaders = true) {
        ({ headers: this.headers, dataRows: this.dataRows } = this.parse(csv, hasHeaders));
        return this;
    }

    async fromFile(csvFile, hasHeaders = true) {
        const csv = await this.readFile(csvFile);
        ({ headers: this.headers, dataRows: this.dataRows } = this.parse(csv, hasHeaders));
        return this;
    }

    toArray() {
        return this.headers?.length ? [this.headers, ...this.dataRows] : this.dataRows;
    }

    toArrayOfObjects() {
        if (!this.headers?.length && this.dataRows?.length) {
            throw Error('Cannot compose object array as CSV has no headers');
        }

        const records = [];

        for (let [rowIndex, row] of this.dataRows.entries()) {
            const record = {};
            for (let [valueIndex, value] of row.entries()) {
                if (valueIndex >= this.headers.length) {
                    console.warn(`Data row ${rowIndex} has more columns than headers`);
                    break;
                }

                const fieldName = this.headers[valueIndex];
                record[fieldName] = value;
            }
            records.push(record);
        }

        return records;
    }

    parse(csv, hasHeaders) {
        const allCsvRows = csv?.split(/\r?\n/).filter((l) => l !== '');
        const result = { headers: [], dataRows: [] };

        if (!allCsvRows?.length) {
            return result;
        }

        result.headers = hasHeaders ? this.parseRow(allCsvRows.shift()) : [];
        result.dataRows = allCsvRows.map((r) => this.parseRow(r));

        return result;
    }

    parseRow(csvRow) {
        const regexp = new RegExp(`(?:^|${this.delimiter})(?:"([^"]*)"|([^"${this.delimiter}]*))`, 'g');
        const rowValues = [];

        for (let match of csvRow.matchAll(regexp)) {
            rowValues.push(match[2] ?? match[1] ?? match[0]);
        }

        return rowValues;
    }

    readFile(file) {
        let readResolver;
        const fileReadPromise = new Promise((resolve) => {
            readResolver = resolve;
        });

        const reader = new FileReader();
        reader.onload = (e) => {
            readResolver(e.target.result);
        };
        reader.readAsText(file);

        return fileReadPromise;
    }
}
