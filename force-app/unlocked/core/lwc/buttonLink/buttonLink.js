import { api, LightningElement } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';

export default class extends NavigationMixin(LightningElement) {
    @api stretch;
    @api variant;
    @api label;
    @api pageApiName;
    @api queryParamsJson;

    openLink() {
        if (!this.pageApiName) {
            return;
        }
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: this.pageApiName
            },
            state: this.getTargetPageState()
        });
    }

    getTargetPageState() {
        if (!this.queryParamsJson) {
            return {};
        }

        try {
            return JSON.parse(this.queryParamsJson);
        } catch (error) {
            console.error(error);
        }

        return {};
    }
}
