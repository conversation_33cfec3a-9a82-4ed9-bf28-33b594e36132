public inherited sharing class PortalUser {
    final private static String PORTAL_USER_PROFILE_NAME = 'Portal User';
    final private static User CURRENT_USER;

    static {
        CURRENT_USER = [
                SELECT Profile.Name, ContactId, Contact.PortalRole__c, Contact.AccountId
                FROM User
                WHERE Id = :UserInfo.getUserId()
        ];
    }

    public enum Role {
        STUDENT,
        ADVISOR,
        LEAD_ADVISOR,
        PARTNER,
        PARTNER_LEAD,
        TECHNICAL_CHAIR,
        EXHIBITOR,
        JUDGE,
        VOLUNTEER,
        STATE_OFFICER
    }

    public static final Map<Role, String> ROLES = new Map<PortalUser.Role, String> {
           Role.STUDENT => 'Student',
           Role.ADVISOR => 'Advisor',
           Role.LEAD_ADVISOR => 'Lead Advisor',
           Role.PARTNER => 'Partner',
           Role.PARTNER_LEAD => 'Partner Lead',
           Role.TECHNICAL_CHAIR => 'Technical Chair',
           Role.EXHIBITOR => 'Exhibitor',
           Role.JUDGE => 'Judge',
           Role.VOLUNTEER => 'Volunteer',
           Role.STATE_OFFICER => 'State Officer'
    };

    final private static CoreSetting__c SETTING = CoreSetting__c.getOrgDefaults();
    final private static Map<String, String> ROLE_TO_PS = new Map<String, String> ();
    private static Profile portalProfile;

    static {
        ROLE_TO_PS.put(ROLES.get(Role.STUDENT), (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.StudentPermissionSets__c));
        ROLE_TO_PS.put(ROLES.get(Role.ADVISOR), (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.AdvisorPermissionSets__c));
        ROLE_TO_PS.put(ROLES.get(Role.LEAD_ADVISOR), (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.LeadAdvisorPermissionSets__c));
        ROLE_TO_PS.put(ROLES.get(Role.PARTNER), (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.PartnerPermissionSets__c));
        ROLE_TO_PS.put(ROLES.get(Role.PARTNER_LEAD), (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.PartnerLeadPermissionSets__c));
        ROLE_TO_PS.put(ROLES.get(Role.TECHNICAL_CHAIR), (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.TechnicalChairPermissionSets__c));
        ROLE_TO_PS.put(ROLES.get(Role.EXHIBITOR), (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.ExhibitorPermissionSets__c));
        ROLE_TO_PS.put(ROLES.get(Role.JUDGE), (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.JudgePermissionSets__c));
        ROLE_TO_PS.put(ROLES.get(Role.VOLUNTEER), (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.VolunteerPermissionSets__c));
        ROLE_TO_PS.put(ROLES.get(Role.STATE_OFFICER), (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.StateOfficerPermissionSets__c));

    }

    static public Boolean hasRole(Role role) {
        return hasRole(UserInfo.getUserId(), role);
    }

    static public Boolean hasRole(Id recordId, Role role) {
        return getPortalRoles(recordId)?.contains(ROLES.get(role));
    }

    static public Boolean hasRole(Contact record, Role role) {
        return getPortalRoles(record)?.contains(ROLES.get(role));
    }

    static public Id getContactId() {
        return getContactId(UserInfo.getUserId());
    }

    static public Id getContactId(Id userId) {
        return getUser(userId).ContactId;
    }

    static public Id getAccountId() {
        return getAccountId(UserInfo.getUserId());
    }

    static public Id getAccountId(Id userId) {
        return getUser(userId).Contact?.AccountId;
    }

    static public Boolean isPortalUser() {
        return !getPortalRoles().isEmpty() && getUser(UserInfo.getUserId()).Profile.Name == PORTAL_USER_PROFILE_NAME;
    }

    static public Boolean isPortalUser(Id userOrContactId) {
        return !getPortalRoles(userOrContactId).isEmpty() && getUser(userOrContactId).Profile.Name == PORTAL_USER_PROFILE_NAME;
    }

    static public List<String> getPortalRoles() {
        return getPortalRoles(UserInfo.getUserId());
    }

    static public List<String> getPortalRoles(Id recordId) {
        String theRole = '';
        String typeOfId = recordId.getSobjectType() + '';
        if (typeOfId == 'User') {
            if (recordId == UserInfo.getUserId()  && Test.isRunningTest() == false) {
                theRole = CURRENT_USER.Contact.PortalRole__c;
            } else {
                theRole = [SELECT Contact.PortalRole__c FROM User WHERE Id = :recordId]?.Contact?.PortalRole__c;
            }
        }
        else if (typeOfId == 'Contact') {
            theRole = [SELECT PortalRole__c FROM Contact WHERE Id = :recordId]?.PortalRole__c;
        }

        List<String> roles = theRole?.split(';');
        return roles != null ? roles : new List<String>();
    }

    static public List<String> getPortalRoles(Contact record) {
        List<String> roles = record?.PortalRole__c?.split(';');
        return roles != null ? roles : new List<String>();
    }

    public static User create(Contact theContact) {
        return create(new List<Contact> {theContact})[0];
    }

    public static List<User> create(List<Contact> contacts) {
        List<User> users = new List<User>();
        for (Contact theContact: contacts) {
            users.add(makeUser(theContact));
        }
        insert users;

        Map<Id, String> userIdToRole = new Map<Id, String>();
        for (User theUser: [SELECT Id, ContactId, Contact.PortalRole__c FROM User WHERE Id IN :users]) {
            userIdToRole.put(theUser.Id, theUser.Contact.PortalRole__c);
        }

        return users;
    }

    public static void updatePermissionSetsByRole(Id userOrContactId, String role) {
        User theUser = getUser(userOrContactId);
        updatePermissionSetsByRole(new Map<Id, String> { theUser.Id => role });
    }

    public static void updatePermissionSetsByRole(Map<Id, String> userIdToRole) {
        if (System.isFuture() || System.isBatch()) {
            updatePermissionSetsByRoleNow(userIdToRole);
        } else {
            updatePermissionSetsByRoleFuture(userIdToRole);
        }
    }

    private static void updatePermissionSetsByRoleNow(Map<Id, String> userIdToRole) {
        cleanPermissionSets(userIdToRole.keySet());
        assignPermissionSetsByRole(userIdToRole);
    }

    @Future
    private static void updatePermissionSetsByRoleFuture(Map<Id, String> userIdToRole) {
        updatePermissionSetsByRoleNow(userIdToRole);
    }

    private static void cleanPermissionSets(Id userId) {
        cleanPermissionSets(new Set<Id> { userId });
    }

    private static void cleanPermissionSets(Set<Id> userIds) {
        List<String> permissionNames = getAllPermissionSetNamesFromSetting();
        delete [
                SELECT Id
                FROM PermissionSetAssignment
            WHERE
                AssigneeId = :userIds
                AND (PermissionSet.Name IN :permissionNames
                OR PermissionSetGroup.DeveloperName IN :permissionNames) // Only permission sets / set groups specified in Setting
        ];
    }

    private static void assignPermissionSetsByRole(Id userId, String role) {
        assignPermissionSetsByRole(new Map<Id, String> { userId => role });
    }

    private static void assignPermissionSetsByRole(Map<Id, String> userIdToRole) {
        if (System.isFuture() || System.isBatch()) {
            assignPermissionSetsByRoleRegular(userIdToRole);
        } else {
            assignPermissionSetsByRoleFuture(userIdToRole);
        }
    }

    @Future
    private static void assignPermissionSetsByRoleFuture(Map<Id, String> userIdToRole) {
        assignPermissionSetsByRoleRegular(userIdToRole);
    }

    private static void assignPermissionSetsByRoleRegular(Map<Id, String> userIdToRole) {
        // Get all names of permission sets in Settings
        List<String> permissionNamesForAllRoles = getAllPermissionSetNamesFromSetting();

        List<SObject> permissionRecords = new List<SObject>();
        // Query records and create map Permission Set (Group) Name -> Permission Set
        List<SObject> permissionSets = [
            SELECT Id, Name
            FROM PermissionSet
            WHERE Name IN :permissionNamesForAllRoles
        ];
        permissionRecords.addAll(permissionSets);
        List<SObject> permissionSetGroups = (List<SObject>) [
            SELECT Id, DeveloperName
            FROM PermissionSetGroup
            WHERE DeveloperName IN :permissionNamesForAllRoles
        ];
        permissionRecords.addAll(permissionSetGroups);

        Map<String, SObject> PSNameToPS = new Map<String, SObject>();
        for (SObject PS : permissionRecords) {
            Schema.SObjectField nameField = PS.getSObjectType() == PermissionSet.SObjectType
                ? PermissionSet.Name
                : PermissionSetGroup.DeveloperName;
            PSNameToPS.put((String) PS.get(nameField), PS);
        }

        // Create List of PermissionSetAssignment for each user and each of his roles
        List<PermissionSetAssignment> assignments = new List<PermissionSetAssignment>();
        for (Id userId: userIdToRole.keySet()) {
            List<String> userRoles = userIdToRole.get(userId)?.split(';'); // Lead Advisor;Exhibitor
            if (userRoles != null) {
                Set<String> userAllPSNames = new Set<String>();
                for (String role: userRoles) {
                    if (String.isNotEmpty(role)) {
                        List<String> userPSNames = ROLE_TO_PS.get(role)?.split('\r\n');
                        if (userPSNames != null) {
                            for (String PSName: userPSNames) {
                                userAllPSNames.add(PSName.trim());
                            }
                        }
                    }
                }
                for (String PSName : userAllPSNames) {
                    PermissionSetAssignment assignment = new PermissionSetAssignment(AssigneeId = userId);
                    SObject permissionRecord = PSNameToPS.get(PSName);
                    Schema.SObjectField assignmentField = permissionRecord.getSObjectType() == PermissionSet.SObjectType
                        ? PermissionSetAssignment.PermissionSetId
                        : PermissionSetAssignment.PermissionSetGroupId;
                    assignment.put(assignmentField, permissionRecord.Id);
                    assignments.add(assignment);
                }
            }
        }
        insert assignments;
    }

    private static List<String> getAllPermissionSetNamesFromSetting() {
        List<String> permissionNamesForAllRoles = new List<String>();
        for (String key: ROLE_TO_PS.keySet()) {
            List<String> permissionNames = ROLE_TO_PS.get(key)?.split('\r\n'); // LeadAdvisorPermissions\nExhibitorPermissions
            if (permissionNames != null) {
                for (String PSName: permissionNames) {
                    permissionNamesForAllRoles.add(PSName.trim());
                }
            }
        }
        return permissionNamesForAllRoles;
    }

    private static User makeUser(Contact theContact) {
        String alias = theContact.FirstName.left(3);
        alias += theContact.LastName.left((3 - alias.length()) + 5);
        return new User(
                ProfileId = getPortalProfile(theContact.PortalRole__c).Id,
                Alias = alias,
                ContactId = theContact.Id,
                Email = theContact.Email,
                Username = theContact.Email,
                FirstName = theContact.FirstName,
                LastName = theContact.LastName,
                LanguageLocaleKey = (String) SETTING.get(CoreSetting__c.PortalUserDefaultLanguageLocaleKey__c),
                EmailEncodingKey = (String) SETTING.get(CoreSetting__c.PortalUserDefaultEmailEncodingKey__c),
                LocaleSidKey = (String) SETTING.get(CoreSetting__c.PortalUserDefaultLocaleSidKey__c),
                TimeZoneSidKey = (String) SETTING.get(CoreSetting__c.PortalUserDefaultTimeZoneSidKey__c)
        );
    }

    private static User getUser(Id recordId) {
        String typeOfId = recordId.getSobjectType() + '';
        if (typeOfId == 'User') {
            if (recordId == UserInfo.getUserId() && Test.isRunningTest() == false) {
                return CURRENT_USER;
            }
            return [SELECT Profile.Name, ContactId, Contact.PortalRole__c, Contact.AccountId FROM User WHERE Id = :recordId];
        }
        else if (typeOfId == 'Contact') {
            return [SELECT Profile.Name, ContactId, Contact.PortalRole__c, Contact.AccountId FROM User WHERE ContactId = :recordId];
        }
        return null;
    }

    private static Profile getPortalProfile(String portalRole) {
        String profileName;
        String profileNameDefault = (String) SETTING.get(CoreSetting__c.PortalUserDefaultProfileName__c);
        String profileNameForLeadAdvisor = (String) SETTING.get(CoreSetting__c.PortalUserLeadAdvisorProfileName__c);

        if (portalRole.containsIgnoreCase('Lead Advisor') && String.isNotEmpty(profileNameForLeadAdvisor)) { // works for Lead Advisor & Advisor roles
            profileName = profileNameForLeadAdvisor;
        }
        else if (portalProfile == null) {
            profileName = profileNameDefault;
        }

        if (String.isNotEmpty(profileName)) {
            portalProfile = [SELECT Id, Name FROM Profile WHERE Name LIKE :profileName];
        }

        return portalProfile;
    }
}