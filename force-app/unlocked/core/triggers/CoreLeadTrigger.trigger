trigger CoreLeadTrigger on Lead (after update) {
    List<Account> accountsToUpdate = new List<Account>();
    for(Lead convertedLead : Trigger.new) {
        if (convertedLead.IsConverted && Trigger.oldMap.get(convertedLead.Id).IsConverted == false) {
            accountsToUpdate.add(new Account(
                    Id = convertedLead.ConvertedAccountId,
                    MainContact__c = convertedLead.ConvertedContactId
            ));
        }
    }
    update accountsToUpdate;
}