<?xml version="1.0" encoding="UTF-8" ?>
<CustomLabels xmlns="http://soap.sforce.com/2006/04/metadata">
    <labels>
        <fullName>inv_SuccessfulPaymentByCredit</fullName>
        <categories>invInvoiceDetail component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv successful Toast paid by credit</shortDescription>
        <value>Successfully paid by credit</value>
    </labels>
    <labels>
        <fullName>inv_DownloadInvoiceBtn</fullName>
        <categories>invInvoiceDetail component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Download Invoice Button</shortDescription>
        <value>Download Invoice</value>
    </labels>
    <labels>
        <fullName>inv_SendViaEmailBtn</fullName>
        <categories>invInvoiceDetail component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Send via Email Button</shortDescription>
        <value>Send via Email</value>
    </labels>
    <labels>
        <fullName>inv_EmailSentMessage</fullName>
        <categories>invInvoiceDetail component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Toast message when email was sent</shortDescription>
        <value>Please check you mailbox.</value>
    </labels>
    <labels>
        <fullName>inv_PayByCreditBtn</fullName>
        <categories>invInvoiceDetail component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Pay By Credit Button</shortDescription>
        <value>Pay By Credit</value>
    </labels>
    <labels>
        <fullName>inv_LineNameCol</fullName>
        <categories>invInvoiceLineList component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Line Name Column</shortDescription>
        <value>Name</value>
    </labels>
    <labels>
        <fullName>inv_LineUnitPriceCol</fullName>
        <categories>invInvoiceLineList component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Line Unit Price Column</shortDescription>
        <value>Unit Price</value>
    </labels>
    <labels>
        <fullName>inv_LineQuantityCol</fullName>
        <categories>invInvoiceLineList component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Line Quantity Column</shortDescription>
        <value>Quantity</value>
    </labels>
    <labels>
        <fullName>inv_LineTotalCol</fullName>
        <categories>invInvoiceLineList component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Line Total Column</shortDescription>
        <value>Total</value>
    </labels>
    <labels>
        <fullName>inv_InvoicePreviewBtn</fullName>
        <categories>invInvoiceLineList component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Preview Invoice Button</shortDescription>
        <value>Preview Invoice</value>
    </labels>
    <labels>
        <fullName>inv_SaveInvoiceBtn</fullName>
        <categories>invInvoiceLineList component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Save Invoice Button</shortDescription>
        <value>Save &amp; Pay Invoice</value>
    </labels>
    <labels>
        <fullName>inv_PayBtn</fullName>
        <categories>invPaymentForm component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Pay Button</shortDescription>
        <value>Pay</value>
    </labels>
    <labels>
        <fullName>inv_PayBtn</fullName>
        <categories>invPaymentForm component</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Pay Button</shortDescription>
        <value>Pay</value>
    </labels>
    <labels>
        <fullName>inv_InvoiceHeader</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Header</shortDescription>
        <value>INVOICE</value>
    </labels>
    <labels>
        <fullName>inv_DueUponReceiptText</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Due Upon Receipt Text</shortDescription>
        <value>Due Upon Receipt</value>
    </labels>
    <labels>
        <fullName>inv_IssueDate</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Issue Date</shortDescription>
        <value>Issue Date:</value>
    </labels>
    <labels>
        <fullName>inv_InvoiceNumber</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Number</shortDescription>
        <value>Invoice Number:</value>
    </labels>
    <labels>
        <fullName>inv_BilledTo</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Billed to</shortDescription>
        <value>Billed to:</value>
    </labels>
    <labels>
        <fullName>inv_ProductName</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Product Name</shortDescription>
        <value>Product Name</value>
    </labels>
    <labels>
        <fullName>inv_ProductComment</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Product Comment</shortDescription>
        <value>Comment</value>
    </labels>
    <labels>
        <fullName>inv_Quantity</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Line Quantity</shortDescription>
        <value>Quantity</value>
    </labels>
    <labels>
        <fullName>inv_UnitPrice</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Unit Price</shortDescription>
        <value>Unit Price</value>
    </labels>
    <labels>
        <fullName>inv_Amount</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Line Amount</shortDescription>
        <value>Amount</value>
    </labels>
    <labels>
        <fullName>inv_Total</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Invoice Total</shortDescription>
        <value>Total:</value>
    </labels>
    <labels>
        <fullName>inv_BankName</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Bank Name</shortDescription>
        <value>Bank:</value>
    </labels>
    <labels>
        <fullName>inv_AccountName</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Account Name</shortDescription>
        <value>Account Name:</value>
    </labels>
    <labels>
        <fullName>inv_AccountNumber</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv Account Number</shortDescription>
        <value>Account Number:</value>
    </labels>
    <labels>
        <fullName>inv_NoInvoiceLinesMes</fullName>
        <categories>Invoice page</categories>
        <language>en_US</language>
        <protected>false</protected>
        <shortDescription>inv No Invoice Lines Message</shortDescription>
        <value>There is no invoice lines available</value>
    </labels>
</CustomLabels>
