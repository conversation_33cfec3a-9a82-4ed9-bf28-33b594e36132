import { LightningElement, api } from 'lwc';
import { showErrors } from 'c/errorHandler';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getInvoice from '@salesforce/apex/inv_InvoiceDetailCtrl.getInvoice';
import sendInvoiceViaEmail from '@salesforce/apex/inv_InvoiceDetailCtrl.sendInvoiceViaEmail';
import sendViaEmailToLeadAdvisors from '@salesforce/apex/inv_InvoiceDetailCtrl.sendViaEmailToLeadAdvisors';
import payInvoiceByCredit from '@salesforce/apex/inv_InvoiceDetailCtrl.payInvoiceByCredit';
import invDownloadInvoiceBtn from '@salesforce/label/c.inv_DownloadInvoiceBtn';
import invSendViaEmailBtn from '@salesforce/label/c.inv_SendViaEmailBtn';
import invEmailSentMessage from '@salesforce/label/c.inv_EmailSentMessage';
import invPayByCreditBtn from '@salesforce/label/c.inv_PayByCreditBtn';
import invSuccessfulPaymentByCredit from '@salesforce/label/c.inv_SuccessfulPaymentByCredit';
import LightningConfirm from 'lightning/confirm';

const PAID_STATUSES = ['Paid', 'Paid By Credit'];

export default class extends LightningElement {
    @api recordId;
    @api isAdminMode = false;
    isSendInvoiceToLADisabled = true;
    isSpinnerShown;
    isPossibleToPayByDiscount = false;
    invoice;

    labels = {
        invDownloadInvoiceBtn,
        invSendViaEmailBtn,
        invEmailSentMessage,
        invPayByCreditBtn,
        invSuccessfulPaymentByCredit
    };

    connectedCallback() {
        this.fetchInvoice();
    }

    fetchInvoice() {
        this.toggleSpinnerDuring(getInvoice({ invoiceId: this.recordId }))
            .then((invoice) => {
                this.invoice = invoice;
                this.isPossibleToPayByDiscount = !PAID_STATUSES.includes(invoice.Status__c) && this.invoice.TotalPrice__c <= 0;
                this.isSendInvoiceToLADisabled = invoice.Status__c !== 'Pending';
            })
            .catch((error) => showErrors(this, error));
    }

    handleDownloadButtonClick() {
        const downloadLink = this.template.querySelector('a[data-id="pdf-download-link"]');
        downloadLink.href = this.refs.preview.downloadUrl;
        downloadLink.download = this.invoice.Name + '.pdf';
        downloadLink.click();
    }

    async handleSendEmailToLAButtonClick() {
        const result = await LightningConfirm.open({
            message: 'Are you sure you want to send the invoice to the lead advisors?',
            variant: 'headerless',
            label: '',
        });

        if (result) {
            console.log('Here');
            sendViaEmailToLeadAdvisors({ invoiceId: this.recordId })
                .then(() => {
                    this.dispatchEvent(
                        new ShowToastEvent({
                            message: 'Successfully sent',
                            variant: 'success'
                        })
                    )
                })
                .catch(error => {
                    showErrors(this, error)
                });
        }
    }

    handleSendEmailButtonClick() {
        this.toggleSpinnerDuring(sendInvoiceViaEmail({ invoiceId: this.invoice.Id }))
            .then(() => {
                this.dispatchEvent(
                    new ShowToastEvent({
                        message: this.labels.invEmailSentMessage,
                        variant: 'success'
                    })
                );
            })
            .catch((error) =>
                showErrors(this, error)
            );
    }

    handlePaymentProcessed() {
        this.fetchInvoice();
    }

    handlePayByDiscount() {
        this.toggleSpinnerDuring(payInvoiceByCredit({recordId: this.recordId}))
            .then(() => {
                this.dispatchEvent(
                    new ShowToastEvent({
                        message: this.labels.invSuccessfulPaymentByCredit,
                        variant: 'success'
                    })
                );
                this.fetchInvoice();
            })
            .catch((error) => showErrors(this, error));
    }

    toggleSpinnerDuring(promise) {
        this.isSpinnerShown = true;
        return promise.finally(() => (this.isSpinnerShown = false));
    }
}
