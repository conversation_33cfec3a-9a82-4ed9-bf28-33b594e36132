<template>
    <template if:true={isNoLinesAvailable}>
        <article class="slds-card">
            <p class="slds-align_absolute-center slds-text-heading_small slds-p-around_medium">
                {labels.invNoInvoiceLinesMes}
            </p>
        </article>
    </template>
    <template if:false={isNoLinesAvailable}>
        <lightning-spinner if:true={isSpinnerShown}></lightning-spinner>
        <lightning-datatable
            if:true={lines}
            key-field="Id"
            data={lines}
            columns={columns}
            onrowselection={handleRowSelection}
        >
        </lightning-datatable>
        <div class="slds-grid slds-grid_align-center slds-gutters slds-m-top_small">
            <button
                onclick={handlePreviewButtonClick}
                disabled={isPreviewButtonDisabled}
                class="slds-button slds-button_outline-brand slds-col"
            >
                {labels.invInvoicePreviewBtn}
            </button>
            <template if:false={invoice}>
                <button
                    if:true={previewReference}
                    onclick={handleSaveButtonClick}
                    class="slds-button slds-button_brand slds-col"
                >
                    {labels.invSaveInvoiceBtn}
                </button>
            </template>
        </div>
        <c-inv-invoice-preview if:true={previewReference} reference={previewReference}></c-inv-invoice-preview>
    </template>
</template>
