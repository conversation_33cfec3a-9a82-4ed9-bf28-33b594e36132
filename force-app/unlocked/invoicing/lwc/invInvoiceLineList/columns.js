import nameField from '@salesforce/schema/InvoiceLine__c.Name';
import unitPriceField from '@salesforce/schema/InvoiceLine__c.UnitPrice__c';
import totalPriceField from '@salesforce/schema/InvoiceLine__c.TotalPrice__c';
import quantityField from '@salesforce/schema/InvoiceLine__c.Quantity__c';

import invLineNameCol from '@salesforce/label/c.inv_LineNameCol';
import invLineUnitPriceCol from '@salesforce/label/c.inv_LineUnitPriceCol';
import invLineQuantityCol from '@salesforce/label/c.inv_LineQuantityCol';
import invLineTotalCol from '@salesforce/label/c.inv_LineTotalCol';

export const columns = [
    { label: invLineNameCol, fieldName: nameField.fieldApiName, hideDefaultActions: true },
    { label: invLineUnitPriceCol, fieldName: unitPriceField.fieldApiName, type: 'currency', hideDefaultActions: true },
    { label: invLineQuantityCol, fieldName: quantityField.fieldApiName, type: 'number', hideDefaultActions: true },
    { label: invLineTotalCol, fieldName: totalPriceField.fieldApiName, type: 'currency', hideDefaultActions: true }
];
