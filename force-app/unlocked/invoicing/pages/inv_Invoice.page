<apex:page
    controller="inv_InvoicePageCtrl"
    showHeader="false"
    sidebar="false"
    renderAs="{!renderAs}"
    standardStylesheets="false"
    applyHtmlTag="false"
>
    <head>
        <apex:stylesheet value="{!URLFOR($Resource.InvoicingStyles, 'invoicePage.css')}" />
        <title>{!$Label.inv_InvoiceHeader}</title>
    </head>

    <body>
        <apex:outputPanel
            rendered="{!IF(NOT(ISBLANK($Setup.inv_Settings__c.InvoiceLogoUrl__c)), true, false)}"
            layout="none"
        >
            <div class="invoice-logo">
                <img src="{!$Setup.inv_Settings__c.InvoiceLogoUrl__c}" alt="Company Logo" height="100" />
            </div>
        </apex:outputPanel>



        <table>
        <tbody>
        <tr>
            <td>
                SkillsUSA Illinois <br/>
                2816 Court Street <br/>
                Pekin, IL 61554 <br/>
                833.754.5545 <br/>
                <EMAIL>
            </td>
            <td class="right-align">
                <b>{!$Label.inv_BilledTo}</b>
                <div>
                    {!invoiceDto.billedTo.companyName} - {!invoiceDto.billedTo.firstName}
                    {!invoiceDto.billedTo.lastName}
                </div>
                {!invoiceDto.billedTo.street} <br /> {!invoiceDto.billedTo.city} {!invoiceDto.billedTo.state}
                {!invoiceDto.billedTo.postalCode}, {!invoiceDto.billedTo.country}
            </td>
        </tr>
        </tbody>
        </table>


        <div class="invoice-header">
            <h1 class="invoice-title">{!$Label.inv_InvoiceHeader}</h1>
            <p class="invoice-date">
                {!$Label.inv_IssueDate}&nbsp;
                <apex:outputText value="{0,date,MM'/'dd'/'yyyy}">
                    <apex:param value="{!invoiceDto.issueDate}" />
                </apex:outputText>
            </p>
            <p><b>{!$Label.inv_DueUponReceiptText}</b></p>
        </div>

        <div class="invoice-body">
            <table>
                <tbody>
                <tr>
                    <td>
<!--                        <p><b>{!$Label.inv_BilledTo}</b></p>-->
<!--                        <p>-->
<!--                            <div>-->
<!--                                {!invoiceDto.billedTo.companyName} - {!invoiceDto.billedTo.firstName}-->
<!--                                {!invoiceDto.billedTo.lastName}-->
<!--                            </div>-->
<!--                            {!invoiceDto.billedTo.street} <br /> {!invoiceDto.billedTo.city} {!invoiceDto.billedTo.state}-->
<!--                            {!invoiceDto.billedTo.postalCode}, {!invoiceDto.billedTo.country}-->
<!--                        </p>-->
                    </td>
                    <td class="right-align">

                    </td>
                </tr>
                </tbody>
            </table>

            <div>
                <span> {!$Label.inv_InvoiceNumber} </span>
                <apex:outputText value="{!invoiceDto.recordNumber}"> </apex:outputText>
                <br/>
                <br/>
            </div>

            <table class="bordered">
                <tr>
                    <th>{!$Label.inv_ProductName}</th>
                    <th>{!$Label.inv_ProductComment}</th>
                    <th class="right-align">{!$Label.inv_Quantity}</th>
                    <th class="right-align">{!$Label.inv_UnitPrice}</th>
                    <th class="right-align">{!$Label.inv_Amount}</th>
                </tr>
                <apex:repeat value="{!invoiceDto.lines}" var="item">
                    <tr>
                        <td>{!item.name}</td>
                        <td>{!item.comment}</td>
                        <td class="right-align">
                            <apex:outputText value="{!item.quantity}"> </apex:outputText>
                        </td>
                        <td class="right-align">
                            <apex:outputText value="{0,number,#,##0.00}">
                                <apex:param value="{!item.price}" />
                            </apex:outputText>
                        </td>
                        <td class="right-align">
                            <apex:outputText value="{0,number,#,##0.00}">
                                <apex:param value="{!item.total}" />
                            </apex:outputText>
                        </td>
                    </tr>
                </apex:repeat>
            </table>
            <br />
            <div class="total">
                <span class="total-label">{!$Label.inv_Total}</span>
                <apex:outputText value="{0,number,#,##0.00}">
                    <apex:param value="{!invoiceDto.total}" />
                </apex:outputText>
                <br /><br /><br />
            </div>
        </div>

        <div>
            <b>Please send check and a copy of this invoice to:</b> <br/>
            SkillsUSA Illinois <br/>
            Attn: Member Engagement <br/>
            2816 Court Street <br/>
            Pekin, IL 61554 <br/>
            <a target="_blank" href="https://skillsusaillinois.sharepoint.com/sites/SkillsUSAIllinoisStaff/Shared%20Documents/Forms/AllItems.aspx?ga=1&id=%2Fsites%2FSkillsUSAIllinoisStaff%2FShared%20Documents%2FBusiness%20Forms%2FPUBLIC%20%2D%20Form%20W%2D9%2FW%2D9%20%2D%20SkillsUSA%20Illinois%2Epdf&parent=%2Fsites%2FSkillsUSAIllinoisStaff%2FShared%20Documents%2FBusiness%20Forms%2FPUBLIC%20%2D%20Form%20W%2D9">Download W9</a>
        </div>

        <div>
            <apex:outputText escape="false" value="{!invoiceDto.comment}"></apex:outputText>
        </div>

    </body>
</apex:page>
