@IsTest
class inv_DtoTest {
    @IsTest
    static void invoiceFields() {
        inv_DTO.Invoice invoiceDto = new inv_DTO.Invoice();
        invoiceDto.recordNumber = 'Number';
        invoiceDto.issueDate = Date.today();
        invoiceDto.total = Math.random() * 100;
        invoiceDto.billedTo = new inv_DTO.Partner();
        invoiceDto.lines = new List<inv_DTO.InvoiceLine>();

        Assert.isNotNull(invoiceDto.recordNumber, 'recordNumber is populated and cannot be null');
        Assert.isNotNull(invoiceDto.issueDate, 'issueDate is populated and cannot be null');
        Assert.isNotNull(invoiceDto.total, 'total is populated and cannot be null');
        Assert.isNotNull(invoiceDto.billedTo, 'recordNumber is populated and cannot be null');
        Assert.isNotNull(invoiceDto.lines, 'lines is populated and cannot be null');
    }

    @IsTest
    static void invoiceLineFields() {
        inv_DTO.InvoiceLine invoiceLineDto = new inv_DTO.InvoiceLine();
        invoiceLineDto.name = 'Name';
        invoiceLineDto.price = Math.random() * 100;
        invoiceLineDto.quantity = Integer.valueOf(Math.random() * 5);
        invoiceLineDto.total = Math.random() * 100;

        Assert.isNotNull(invoiceLineDto.name, 'name is populated and cannot be null');
        Assert.isNotNull(invoiceLineDto.price, 'price is populated and cannot be null');
        Assert.isNotNull(invoiceLineDto.quantity, 'quantity is populated and cannot be null');
        Assert.isNotNull(invoiceLineDto.total, 'total is populated and cannot be null');
    }

    @IsTest
    static void partnerFields() {
        inv_DTO.Partner partnerDto = new inv_DTO.Partner();
        partnerDto.companyName = 'company';
        partnerDto.firstName = 'first name';
        partnerDto.lastName = 'last name';
        partnerDto.street = 'street';
        partnerDto.city = 'city';
        partnerDto.state = 'state';
        partnerDto.postalCode = '123456';
        partnerDto.country = 'country';

        Assert.isNotNull(partnerDto.companyName, 'companyName is populated and cannot be null');
        Assert.isNotNull(partnerDto.firstName, 'firstName is populated and cannot be null');
        Assert.isNotNull(partnerDto.lastName, 'lastName is populated and cannot be null');
        Assert.isNotNull(partnerDto.street, 'street is populated and cannot be null');
        Assert.isNotNull(partnerDto.city, 'city is populated and cannot be null');
        Assert.isNotNull(partnerDto.state, 'state is populated and cannot be null');
        Assert.isNotNull(partnerDto.postalCode, 'postalCode is populated and cannot be null');
        Assert.isNotNull(partnerDto.country, 'country is populated and cannot be null');
    }
}