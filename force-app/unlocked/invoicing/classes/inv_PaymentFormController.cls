public with sharing class inv_PaymentFormController {
    private static final String TOKEN = 'UUIDSKILLSUSA';
    private static final String PROVIDER_ID = 'UUIDSKILLSUSA';
    private static final String PROVIDER_NAME = 'inv_ProviderCallableAPI';
    private static final String CHECKOUT_API = 'acps.CheckoutCallableApi';

    @AuraEnabled
    public static String createPayment(Id invoiceId) {
        try {
            Callable clb = (Callable) Type.forName(CHECKOUT_API)?.newInstance();

            handshake(clb);

            Map<String, Object> itemToPay = makeItemToPay(invoiceId);
            List<Map<String,Object>> items = new List<Map<String, Object>>{itemToPay};

            Map<String, Object> response = createPaymentForItems(clb, items);

            return getTokenFromPayload(response);
        } catch (Exception e) {
            throw Error.toLWC(e.getMessage());
        }
    }

    private static void handshake(Callable clb) {
        clb.call('handshake', new Map<String, Object>{
                'token' => TOKEN,
                'provider' => PROVIDER_NAME
        });
    }

    private static Map<String, Object> makeItemToPay(Id invoiceId) {
        Invoice__c invoice = [SELECT Id, Name, TotalPrice__c FROM Invoice__c WHERE Id = :invoiceId];
        return new Map<String, Object> {
                'providerId' => PROVIDER_ID,
                'name' => invoice.Name,
                'price' => invoice.TotalPrice__c,
                'quantity' => 1,
                'type' => 'Product',
                'masterProductId' => '',
                'productId' => invoice.Id,
                'isRecurring' => false,
                'collectPaymentInfo' => false
        };
    }

    private static Map<String, Object> createPaymentForItems(Callable clb, List<Map<String,Object>> items) {
        Id CONTACT_ID = PortalUser.getContactId();
        return (Map<String, Object>) clb.call('createPaymentForItems', new Map<String, Object> {
                'params' => new Map<String, Object> {
                        'contactId' => CONTACT_ID,
                        'itemsToPay' => items
                }
        });
    }

    private static String getTokenFromPayload(Map<String, Object> response) {
        Object payload = response?.get('payload');
        Map<String, Object> payloadObject = (Map<String, Object>) payload;
        Object dataObject = payloadObject.get('data');
        Map<String, Object> data = (Map<String, Object>) dataObject;

        return (String) data.get('token');
    }
}