public class inv_DTO {
    public virtual class Invoice {
        public String recordNumber { get; set; }
        public Date issueDate { get; set; }
        public Double total { get; set; }

        public Partner billedTo { get; set; }
        public List<InvoiceLine> lines { get; set; }
        public String comment { get; set; }
    }

    public virtual class InvoiceLine {
        public String name { get; set; }
        public String comment { get; set; }
        public Double price { get; set; }
        public Double quantity { get; set; }
        public Double total { get; set; }
    }

    public virtual class Partner {
        public String companyName { get; set; }
        public String firstName { get; set; }
        public String lastName { get; set; }
        public String street { get; set; }
        public String city { get; set; }
        public String state { get; set; }
        public String postalCode { get; set; }
        public String country { get; set; }
    }
}