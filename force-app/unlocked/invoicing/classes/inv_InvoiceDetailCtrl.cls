public with sharing class inv_InvoiceDetailCtrl {
    @AuraEnabled(Cacheable=false)
    public static Invoice__c getInvoice(Id invoiceId) {
        try {
            return inv_Selector.getInvoice(invoiceId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void sendViaEmailToLeadAdvisors(Id invoiceId) {
        try {
            inv_InvoiceService.sendViaEmailToLeadAdvisors(invoiceId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void sendInvoiceViaEmail(Id invoiceId) {
        try {
            inv_InvoiceService.sendViaEmail(invoiceId);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void payInvoiceByCredit(Id recordId) {
        try {
            Invoice__c invoice = inv_Selector.getInvoice(recordId);
            if (PortalUser.getAccountId() != invoice.Account__c) {
                throw Error.toLWC('Permission denied.');
            }

            // Update status
            invoice.PaidByUserId__c = UserInfo.getUserId();
            invoice.Status__c = 'Paid By Credit';
            update invoice;
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}