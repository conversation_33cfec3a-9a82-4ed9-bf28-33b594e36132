global with sharing class inv_ProviderCallableAPI implements Callable {
    private static final String CHECKOUT_CALLABLE_API = 'acps.CheckoutCallableApi';
    private static String token = 'UUIDSKILLSUSA';
    @TestVisible
    private Boolean isHandshakeSuccess { get; set; }
    {
        isHandshakeSuccess = false;
    }

    private String getToken() {
        if (token == null) {
            token = EncodingUtil.base64Encode(Crypto.generateAesKey(128));
        }
        return token;
    }

    public Object call(String action, Map<String, Object> args) {

        if (action.equals('handshake')) {
            isHandshakeSuccess = handleHandshakeValidation(args);
            return isHandshakeSuccess;
        } else if (action.equals('validateToken')) {
            return handleValidateToken(args);
        } else if (isHandshakeSuccess && action.equals('onOrderItemsCreate')) {
            return onOrderItemsCreate(args);
        } else if (isHandshakeSuccess && action.equals('onOrderPaid')) {
            return onOrderPaid(args);
        } else {
            return null;
        }
    }

    private static Boolean handleHandshakeValidation(Map<String, Object> args) {
        try {
            String tokenToValidate = (String) args.get('token');
            Callable checkoutCallable = (Callable) Type.forName(CHECKOUT_CALLABLE_API).newInstance();
            Map<String, Object> result = (Map<String, Object>) checkoutCallable.call(
                    'validateToken',
                    new Map<String, Object>{
                            'token' => tokenToValidate
                    }
            );

            if (!extractIsSuccess(result)) {
                return false;
            }

            return (Boolean) extractPayloadData(result);
        } catch (Exception e) {
            return false;
        }
    }

    private Boolean handleValidateToken(Map<String, Object> args) {
        String connectorToken = this.getToken();
        String tokenToValidate = (String) args.get('token');

        return connectorToken.equals(tokenToValidate);
    }

    @TestVisible
    private static String onOrderItemsCreate(Map<String, Object> args) {
        for (Map<String, Object> orderDetails : (List<Map<String, Object>>) args.get('ordersDetails')) {
            for (String key : orderDetails.keySet()) {
                if (key == 'orderItems') {
                    List<Object> orderItemsDetails = (List<Object>) orderDetails.get(key);
                    Map<String, Object> itemMap = (Map<String, Object>) orderItemsDetails[0];
                    String orderItemNumber = (String) itemMap.get('orderItemNumber');
                    String productId = (String) itemMap.get('productId');
                    Id orderItemId = new WSHelper().getOrderItemId(orderItemNumber);

                    WS.upsertRecord(new Invoice__c(
                            Id = productId,
                            UCOrderItem__c = orderItemId
                    ));
                }
            }
        }
        return 'ok';
    }

    private static String onOrderPaid(Map<String, Object> args) {
        // do some logic
        List<Map<String, Object>> paidOrdersDetails = (List<Map<String, Object>>) args.get('paidOrdersDetails');
        if (paidOrdersDetails.size() > 0) {
            Map<String, Object> paidOrdersDetail = paidOrdersDetails[0];
            String orderNumber = (String) paidOrdersDetail.get('orderNumber');
            List<Object> paidInvoiceDetails = (List<Object>) paidOrdersDetail.get('orderItems');
            if (paidInvoiceDetails.size() > 0) {
                Map<String, Object> paidInvoiceMap = (Map<String, Object>) paidInvoiceDetails[0];
                String productId =(String) paidInvoiceMap.get('productId');

                WS.upsertRecord(new Invoice__c(
                        Id = productId,
                        Status__c = 'PAID',
                        PaymentOrderNumber__c = orderNumber,
                        PaidByUserId__c = UserInfo.getUserId()
                ));
            }
        }

        return 'ok';
    }

    @TestVisible
    private static Boolean extractIsSuccess(Object response) {
        Map<String, Object> mapResponse = (Map<String, Object>) response;
        return (Boolean) mapResponse.get('isSuccess');
    }

    private static Map<String, Object> extractPayload(Object response) {
        Map<String, Object> mapResponse = (Map<String, Object>) response;
        return (Map<String, Object>) mapResponse.get('payload');
    }

    @TestVisible
    private static Object extractPayloadData(Object response) {
        Map<String, Object> payload = extractPayload(response);
        return payload.get('data');
    }

    without sharing class WSHelper {
        public String getOrderItemId(String orderItemName) {
            return [SELECT Id FROM acps__OrderItem__c WHERE Name = :orderItemName]?.Id;
        }
    }
}