public with sharing class inv_InvoiceTriggerHandler {
    private static Set<System.TriggerOperation> bypassOperations = new Set<System.TriggerOperation>();
    private System.TriggerOperation triggerOperation;
    private List<Invoice__c> invoicesNew;
    private Map<Id, Invoice__c> idToInvoiceOld;

    public inv_InvoiceTriggerHandler(
        System.TriggerOperation triggerOperation,
        List<Invoice__c> invoicesNew,
        Map<Id, Invoice__c> idToInvoiceOld
    ) {
        this.triggerOperation = triggerOperation;
        this.invoicesNew = invoicesNew;
        this.idToInvoiceOld = idToInvoiceOld;
    }

    public void run() {
        if (bypassOperations.contains(this.triggerOperation)) {
            return;
        }
        switch on this.triggerOperation {
            when AFTER_INSERT {
                afterInsert();
            }
        }
    }

    private void afterInsert() {
        Invoice__c firstInYearInvoice = queryFirstInvoiceInYear();
        List<Invoice__c> updatedNameInvoices = new List<Invoice__c>();
        for (Invoice__c invoice : this.invoicesNew) {
            updatedNameInvoices.add(
                new Invoice__c(
                    Id = invoice.Id,
                    Name = inv_InvoiceService.generateName(invoice, Long.valueOf(firstInYearInvoice.SequenceNumber__c))
                )
            );
        }
        Database.update(updatedNameInvoices, AccessLevel.SYSTEM_MODE);
    }

    private Invoice__c queryFirstInvoiceInYear() {
        Integer currentYear = Date.today().year();
        return (Invoice__c) WS.retrieveRecord(
            'SELECT Name, SequenceNumber__c, CreatedDate' +
                ' FROM Invoice__c' +
                ' WHERE CALENDAR_YEAR(CreatedDate) =' +
                currentYear +
                ' ORDER BY CreatedDate ASC' +
                ' LIMIT 1'
        );
    }
}