public with sharing class inv_InvoiceService {
    public static final String INVOICE_NAME_PREFIX = 'INV';
    public static final Integer INVOICE_NUMBER_DIGITS_AMOUNT = 4;
    public static final String INVOICE_EMAIL_TEMPLATE_NAME = 'InvoicePdfExport';
    public static final String PENDING_INVOICE_STATUS = 'Pending';
    public static final String DEFAULT_INVOICE_NAME =
        INVOICE_NAME_PREFIX + ''.leftPad(INVOICE_NUMBER_DIGITS_AMOUNT, '0');
    private static final String PDF_EXTENSION = '.pdf';

    public static Invoice__c createInvoice(List<Id> invoiceLineIds) {
        Invoice__c invoice = new Invoice__c(Name = inv_InvoiceService.DEFAULT_INVOICE_NAME);
        invoice.Account__c = PortalUser.getAccountId();
        List<InvoiceLine__c> invoiceLines = inv_Selector.getInvoiceLines(invoiceLineIds);
        invoice.TotalPrice__c = calculateTotal(invoiceLines);

        //inserting in system mode so no need to give TotalPrice__c edit permission to user
        Database.insert(invoice, AccessLevel.SYSTEM_MODE);

        for (InvoiceLine__c invoiceLine : invoiceLines) {
            invoiceLine.Invoice__c = invoice.Id;
        }

        WS.updateRecords(invoiceLines);

        invoice = inv_Selector.getInvoice(invoice.Id);
        saveInvoicePdf(invoice.Id, Network.getNetworkId());

        return invoice;
    }

    public static String generateName(Invoice__c invoice, Long startNumber) {
        Long inYearNumber = Long.valueOf(invoice.SequenceNumber__c) - startNumber + 1;
        return INVOICE_NAME_PREFIX +
            invoice.CreatedDate.year() +
            String.valueOf(inYearNumber).leftPad(INVOICE_NUMBER_DIGITS_AMOUNT, '0');
    }

    public static Decimal calculateTotal(List<InvoiceLine__c> invoiceLines) {
        Decimal total = 0;
        for (InvoiceLine__c invoiceLine : invoiceLines) {
            total += invoiceLine.TotalPrice__c;
        }
        return total;
    }

    @Future(Callout=true)
    public static void saveInvoicePdf(Id invoiceId, Id networkId) {
        Invoice__c invoice = inv_Selector.getInvoice(invoiceId);
        ContentVersion contentVer = new ContentVersion();
        contentVer.VersionData = getInvoicePagePdfContent(invoice.Id);
        contentVer.Title = invoice.Name;
        contentVer.PathOnClient = invoice.Name + PDF_EXTENSION;
        contentVer.NetworkId = networkId;

        //Avoiding DMLException because of required NetworkId for community users
        if (Test.isRunningTest()) {
            return;
        }

        Database.insert(contentVer, AccessLevel.USER_MODE);

        ContentVersion savedContentVer = [
            SELECT ContentDocumentId
            FROM ContentVersion
            WHERE Id = :contentVer.Id
            WITH USER_MODE
        ];

        ContentDocumentLink contentDocLink = new ContentDocumentLink(
            ContentDocumentId = savedContentVer.ContentDocumentId,
            LinkedEntityId = invoice.Id,
            ShareType = 'V'
        );

        Database.insert(contentDocLink, AccessLevel.USER_MODE);
    }

    public static void sendViaEmail(Id invoiceId) {
        Invoice__c invoice = inv_Selector.getInvoice(invoiceId);
        EmailTemplate template = inv_Selector.getEmailTemplate(INVOICE_EMAIL_TEMPLATE_NAME);
        Id orgWideEmailId = CoreSetting__c.getInstance().OrgWideEmailId__c;
        Contact currentContact = inv_Selector.getContactByUserId(UserInfo.getUserId());

        Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
        email.setToAddresses(new List<String>{ currentContact.Email });
        email.setTemplateId(template.Id);
        email.setWhatId(invoiceId);
        email.setTargetObjectId(currentContact.Id);
        if (String.isNotBlank(orgWideEmailId)) {
            email.setOrgWideEmailAddressId(orgWideEmailId);
        }

        Messaging.EmailFileAttachment invoiceAttachment = new Messaging.EmailFileAttachment();
        invoiceAttachment.setFileName(invoice.Name + PDF_EXTENSION);
        invoiceAttachment.setBody(getInvoicePagePdfContent(invoiceId));
        invoiceAttachment.setContentType('application/pdf');

        email.setFileAttachments(new List<Messaging.EmailFileAttachment>{ invoiceAttachment });

        if (!Test.isRunningTest()) {
            Messaging.sendEmail(new List<Messaging.SingleEmailMessage>{ email });
        }
    }

    public static void sendViaEmailToLeadAdvisors(Id invoiceId) {
        Invoice__c invoice = inv_Selector.getInvoice(invoiceId);
        EmailTemplate template = inv_Selector.getEmailTemplate('InvoiceUpdated');
        Id orgWideEmailId = CoreSetting__c.getInstance().OrgWideEmailId__c;
        List<Contact> LAs = inv_Selector.getLeadAdvisorsForSchool(invoice.Account__c);

        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();
        for (Contact theContact: LAs) {
            Messaging.SingleEmailMessage email = new Messaging.SingleEmailMessage();
            email.setTemplateId(template.Id);
            email.setWhatId(invoiceId);
            email.setTargetObjectId(theContact.Id);
            if (String.isNotBlank(orgWideEmailId)) {
                email.setOrgWideEmailAddressId(orgWideEmailId);
            }

            emails.add(email);
        }

        if (!Test.isRunningTest()) {
            Messaging.sendEmail(emails);
        }
    }

    public static Blob getInvoicePagePdfContent(Id invoiceId) {
        PageReference invoicePageRef = Page.inv_Invoice;
        invoicePageRef.getParameters().put('reference', invoiceId);
        Blob invoicePageContent = Test.isRunningTest() ? Blob.valueOf('test string') : invoicePageRef.getContent();

        return invoicePageContent;
    }
}