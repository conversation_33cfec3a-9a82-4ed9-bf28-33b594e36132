@IsTest
private class inv_ProviderCallableAPITest
{
    @IsTest
    static void testHandshake() {
        new inv_ProviderCallableAPI().call('handshake', new Map<String, Object> {
                'token' => 'UUIDSKILLSUSA'
        });
    }

    @IsTest
    static void testValidateToken() {
        new inv_ProviderCallableAPI().call('validateToken', new Map<String, Object> {
                'token' => 'UUIDSKILLSUSA'
        });
    }

    @IsTest
    static void testOnOrderPaid() {
        inv_ProviderCallableAPI instance = new inv_ProviderCallableAPI();
        instance.isHandshakeSuccess = true;
        instance.call('onOrderPaid', new Map<String, Object> {
                'paidOrdersDetails' => new List<Map<String, Object>>()
        });
    }

    @IsTest
    static void testExtractIsSuccess() {
        inv_ProviderCallableAPI.extractIsSuccess(new Map<String, Object> {
                'isSuccess' => true
        });
    }

    @IsTest
    static void testOnOrderItemsCreate() {
        Invoice__c invoice = inv_TestDataFactory.createInvoice();
        insert invoice;

        Map<String, Object> args = new Map<String, Object> {
                'ordersDetails' => new List<Map<String, Object>> {
                        new Map<String, Object> {
                                'orderItems' => new List<Object> {
                                        new Map<String, String> {
                                                'orderItemNumber' => null,
                                                'productId' => invoice.Id
                                        }
                                }
                        }
                }
        };

        inv_ProviderCallableAPI.onOrderItemsCreate(args);
    }

    @IsTest
    static void testExtractPayloadData() {
        inv_ProviderCallableAPI.extractPayloadData(new Map<String, Object> {
                'payload' => new Map<String, Object> {
                        'data' => null
                }
        });
    }
}