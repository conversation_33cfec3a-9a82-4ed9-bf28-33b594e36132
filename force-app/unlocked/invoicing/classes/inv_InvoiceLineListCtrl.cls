public with sharing class inv_InvoiceLineListCtrl {
    @AuraEnabled
    public static List<InvoiceLine__c> getInvoiceLines() {
        try {
            return inv_Selector.getUnpaidInvoiceLines();
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static Invoice__c createInvoice(List<Id> invoiceLineIds) {
        try {
            return inv_InvoiceService.createInvoice(invoiceLineIds);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}