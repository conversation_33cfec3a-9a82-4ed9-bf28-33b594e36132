public class sm_LeadNotificationBatch implements Database.Batchable<SObject> {
    @TestVisible
    static final String LEAD_CLOSED_STATUS = 'Closed - Not Converted';
    private Set<Date> daysAfterCreation;
    private Set<String> leadStatuses;
    private String leadQuery;
    private EmailTemplate leadTemplate;
    private CustomNotificationType leadNotificationType;
    private Id orgWideEmailId;

    public sm_LeadNotificationBatch() {
        initializeSets();
        initializeNotificationSettings();
        buildQuery();
    }

    private void initializeSets() {
        daysAfterCreation = new Set<Date>{
            System.today().addDays(-2),
            System.today().addDays(-5),
            System.today().addDays(-10),
            System.today().addDays(-20),
            System.today().addDays(-30),
            System.today().addDays(-31)
        };
        leadStatuses = new Set<String>{ 'Open - Not Contacted' };
    }

    private void initializeNotificationSettings() {
        leadTemplate = sm_Selector.getNotContactedLeadNotificationEmailTemplate();
        leadNotificationType = sm_Selector.getNotContactedLeadCustomNotificationType();
        orgWideEmailId = (String) CoreSetting__c.getOrgDefaults().get(CoreSetting__c.OrgWideEmailId__c);
    }

    private void buildQuery() {
        leadQuery = 'SELECT Id, Email, OwnerId, CreatedDate, Status FROM Lead WHERE Status IN :leadStatuses AND Email != null AND DAY_ONLY(CreatedDate) IN :daysAfterCreation';
    }

    public Database.QueryLocator start(Database.BatchableContext BC) {
        return Database.getQueryLocator(leadQuery);
    }

    public void execute(Database.BatchableContext BC, List<SObject> scope) {
        closeLeads(scope);
        sendNotifications(scope);
    }

    private void closeLeads(List<Lead> leads) {
        List<Lead> leadsToClose = new List<Lead>();

        for (Lead theLead : leads) {
            Date leadCreationDate = theLead.CreatedDate.date();

            if (leadCreationDate == System.today().addDays(-31)) {
                leadsToClose.add(new Lead(Id = theLead.Id, Status = LEAD_CLOSED_STATUS));
            }
        }

        update as user leadsToClose;
    }

    private void sendNotifications(List<Lead> leads) {
        List<Messaging.SingleEmailMessage> emails = new List<Messaging.SingleEmailMessage>();

        for (Lead theLead : leads) {
            Date leadCreationDate = theLead.CreatedDate.date();

            if (leadCreationDate != System.today().addDays(-31)) {
                emails.add(generateEmail(theLead));
                sendInternalNotification(theLead);
            }
        }

        Messaging.sendEmail(emails, false);
    }

    private Messaging.SingleEmailMessage generateEmail(Lead theLead) {
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setTemplateId(leadTemplate.Id);
        mail.setTargetObjectId(theLead.Id);
        if (String.isNotBlank(orgWideEmailId)) {
            mail.setOrgWideEmailAddressId(orgWideEmailId);
        }

        return mail;
    }

    private void sendInternalNotification(Lead theLead) {
        Messaging.CustomNotification notification = new Messaging.CustomNotification();
        notification.setTitle(System.Label.sm_CustomLeadNotificationTile);
        notification.setBody(System.Label.sm_CustomLeadNotificationBody);
        notification.setNotificationTypeId(leadNotificationType.Id);
        notification.setTargetId(theLead.Id);
        notification.send(new Set<String>{ theLead.OwnerId });
    }

    public void finish(Database.BatchableContext BC) {
    }
}