public with sharing class sm_CreateMiddleSchoolMembersFormCtrl {
    private static final String FORM_LAYOUT_NAME = 'Contact-[SM] Middle School Member';

    @AuraEnabled
    public static aclab.FormLayout getForm() {
        try {
            return aclab.Form.retrieve(FORM_LAYOUT_NAME);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled(Cacheable=false)
    public static List<sm_SchoolTrainingProgram__c> getProgramTrainings() {
        try {
            Id contactId = PortalUser.getContactId();
            List<sm_ContactTrainingProgram__c> programs = [
                SELECT SchoolTrainingProgram__r.TrainingProgram__c
                FROM sm_ContactTrainingProgram__c
                WHERE Contact__c = :contactId
            ];
            Set<Id> relevantPrograms = new Set<Id>();
            for (sm_ContactTrainingProgram__c ctp : programs) {
                relevantPrograms.add(ctp.SchoolTrainingProgram__r.TrainingProgram__c);
            }

            Id accountId = PortalUser.getAccountId();
            return [
                SELECT Id, TrainingProgram__r.Name
                FROM sm_SchoolTrainingProgram__c
                WHERE School__c = :accountId AND TrainingProgram__c IN :relevantPrograms
                ORDER BY TrainingProgram__r.Name
            ];
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static void saveRecord(Contact record, List<Id> programs) {
        try {
            String approvalStatus = PortalUser.hasRole(PortalUser.Role.ADVISOR)
                ? 'User Created by Advisor'
                : 'User Created by Lead Advisor';
            Contact newContact = (Contact) aclab.Form.cleanInjectedFields(record, FORM_LAYOUT_NAME);
            newContact.AccountId = PortalUser.getAccountId();
            newContact.PortalRole__c = 'Middle School Member';
            newContact.Division__c = 'Middle School';
            newContact.Advisor__c = PortalUser.getContactId();
            newContact.IsMiddleSchoolMember__c = true;
            newContact.ApprovalStatus__c = approvalStatus;

            newContact = (Contact) WS.insertRecord(newContact);
            sm_PortalUserService.addTrainingPrograms(newContact.Id, programs);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }
}