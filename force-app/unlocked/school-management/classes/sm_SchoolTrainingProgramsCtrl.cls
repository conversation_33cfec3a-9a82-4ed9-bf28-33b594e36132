public with sharing class sm_SchoolTrainingProgramsCtrl {
    @AuraEnabled(Cacheable=false)
    public static Options getOptions() {
        try {
            List<sm_TrainingProgram__c> allOptions = [SELECT Id, Name FROM sm_TrainingProgram__c ORDER BY Name];
            List<Option> options = new List<Option>();
            for (sm_TrainingProgram__c tp : allOptions) {
                options.add(new Option(tp.Name, tp.Id));
            }

            List<sm_SchoolTrainingProgram__c> selectedOptions = getSchoolPrograms();
            List<Id> selected = new List<Id>();
            for (sm_SchoolTrainingProgram__c stp : selectedOptions) {
                selected.add(stp.TrainingProgram__c);
            }

            return new Options(options, selected);
        } catch (Exception e) {
            throw Error.toLWC(e);
        }
    }

    @AuraEnabled
    public static Boolean save(List<Id> programIds) {
        Id currentAccountId = [SELECT AccountId FROM User WHERE Id = :UserInfo.getUserId()]?.AccountId;

        List<sm_SchoolTrainingProgram__c> existingPrograms = getSchoolPrograms();
        List<sm_SchoolTrainingProgram__c> newPrograms = new List<sm_SchoolTrainingProgram__c>();
        List<Id> toDeletePrograms = new List<Id>();

        for (Id theId : programIds) {
            if (!contains(existingPrograms, theId)) {
                newPrograms.add(
                    new sm_SchoolTrainingProgram__c(TrainingProgram__c = theId, School__c = currentAccountId)
                );
            }
        }

        for (sm_SchoolTrainingProgram__c program : existingPrograms) {
            if (!contains(programIds, program.TrainingProgram__c)) {
                toDeletePrograms.add(program.TrainingProgram__c);
            }
        }

        WS.insertRecords(newPrograms);
        WS.deleteRecords(
            [
                SELECT Id
                FROM sm_SchoolTrainingProgram__c
                WHERE TrainingProgram__c IN :toDeletePrograms AND School__c = :currentAccountId
            ]
        );

        return true;
    }

    private static Boolean contains(List<sm_SchoolTrainingProgram__c> programs, Id theId) {
        for (sm_SchoolTrainingProgram__c program : programs) {
            if (program.TrainingProgram__c == theId) {
                return true;
            }
        }
        return false;
    }

    private static Boolean contains(List<Id> programIds, Id theId) {
        for (Id programId : programIds) {
            if (programId == theId) {
                return true;
            }
        }
        return false;
    }

    private static List<sm_SchoolTrainingProgram__c> getSchoolPrograms() {
        return [
            SELECT Id, TrainingProgram__c, TrainingProgram__r.Name
            FROM sm_SchoolTrainingProgram__c
            WHERE School__c = :PortalUser.getAccountId()
            ORDER BY TrainingProgram__r.Name
        ];
    }

    public class Options {
        @AuraEnabled
        public List<Option> options;
        @AuraEnabled
        public List<Id> selected;

        public Options(List<Option> options, List<Id> selected) {
            this.options = options;
            this.selected = selected;
        }
    }

    class Option {
        @AuraEnabled
        public String label;
        @AuraEnabled
        public String value;

        public Option(String label, String value) {
            this.label = label;
            this.value = value;
        }
    }
}