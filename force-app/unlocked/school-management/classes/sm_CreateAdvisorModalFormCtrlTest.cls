@IsTest
public class sm_CreateAdvisorModalFormCtrlTest {
    @TestSetup
    static void setup() {
        System.runAs(sm_TestUtils.createAdminUser()) {
            sm_TestUtils.insertSettings();
            Account theAccount = sm_TestUtils.createAccount('Acme');
            Contact theContact = sm_TestUtils.createContact(theAccount.Id, 'Lead Advisor');
            PortalUser.create(theContact);
        }
    }

    @IsTest
    static void testInit() {
        System.runAs(sm_TestUtils.getUserByRole('Lead Advisor')) {
            Test.startTest();
            sm_CreateAdvisorModalFormCtrl.init();
            Test.stopTest();
        }
    }

    @IsTest
    static void geContactFormSectionsSuccess() {
        aclab.FormLayout theForm = null;
        System.runAs(sm_TestUtils.getAdmin()) {
            Test.startTest();
            theForm = sm_CreateAdvisorModalFormCtrl.getForm();
            Test.stopTest();
        }
        Assert.areNotEqual(0, theForm.sections.size(), 'Sections size must not be 0');
    }

    @IsTest
    static void createAdvisorSuccess() {
        Contact newContact = new Contact(FirstName = 'Tom', LastName = 'Smith', Email = '<EMAIL>');

        Test.startTest();
        System.runAs(sm_TestUtils.getUserByRole('Lead Advisor')) {
            sm_CreateAdvisorModalFormCtrl.saveRecord(newContact, new List<Id>(), 'Advisor');
        }
        Test.stopTest();

        User newAdvisor = [SELECT Id FROM User WHERE Email = :newContact.Email];

        Assert.areNotEqual(null, newAdvisor.Id, 'Advisor must be created');
    }

    @IsTest
    static void createAdvisorError() {
        Contact contactRecord = new Contact(FirstName = 'Tom', LastName = 'Smith');

        Test.startTest();
        System.runAs(sm_TestUtils.getAdmin()) {
            try {
                sm_CreateAdvisorModalFormCtrl.saveRecord(contactRecord, new List<Id>(), 'Advisor');
            } catch (Exception e) {
                Assert.isNotNull(e.getMessage(), 'Exception message cannot be null');
            }
        }
        Test.stopTest();
    }

    @IsTest
    static void createLeadAdvisorSuccess() {
        Contact newContact = new Contact(FirstName = 'Anton', LastName = 'Smith', Email = '<EMAIL>');

        Test.startTest();
        System.runAs(sm_TestUtils.getUserByRole('Lead Advisor')) {
            sm_CreateAdvisorModalFormCtrl.saveRecord(newContact, new List<Id>(), 'Lead Advisor');
        }
        Test.stopTest();

        User newAdvisor = [SELECT Id FROM User WHERE Email = :newContact.Email];

        Assert.areNotEqual(null, newAdvisor.Id, 'Advisor must be created');
    }

    @IsTest
    static void createLeadAdvisorError() {
        Contact contactRecord = new Contact(FirstName = 'Anton', LastName = 'Smith');

        Test.startTest();
        System.runAs(sm_TestUtils.getAdmin()) {
            try {
                sm_CreateAdvisorModalFormCtrl.saveRecord(contactRecord, new List<Id>(), 'Lead Advisor');
            } catch (Exception e) {
                Assert.isNotNull(e.getMessage(), 'Exception message cannot be null');
            }
        }
        Test.stopTest();
    }

}