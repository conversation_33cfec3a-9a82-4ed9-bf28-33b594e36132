import { api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { showErrors } from 'c/errorHandler';
import LightningModal from 'lightning/modal';
import getForm from '@salesforce/apex/sm_CreateAdvisorModalFormCtrl.getForm';
import saveRecord from '@salesforce/apex/sm_CreateAdvisorModalFormCtrl.saveRecord';
import init from '@salesforce/apex/sm_CreateAdvisorModalFormCtrl.init';
import labels from './labels';

export default class extends LightningModal {
    @api modalHeader = 'Create Lead Advisor / Advisor';
    @api accountId;
    @api skipRoleSelection;
    @api trainingPrograms = [];
    availableDivisions = [];
    labels = labels;
    roleSelected = false;
    selectedRole = 'Advisor';
    isLoading = false;
    isFormReady = false;
    initResponse;
    _selected = [];

    get options() {
        return [
            { label: 'Advisor', value: 'Advisor' },
            { label: 'Lead Advisor', value: 'Lead Advisor' }
        ];
    }

    connectedCallback() {
        if (this.skipRoleSelection) {
            this.roleSelected = true;
        }

        this.getFormMethod = () => {
            return new Promise((resolve, reject) => {
                getForm()
                    .then(form => {
                        this.injectNameFields(form);
                        resolve(form);
                    })
                    .catch(error => reject(error));
            });
        };

        init()
            .then((response) => {
                this.availableDivisions = response?.availableDivisions;
                /* eslint-disable */
                this.trainingPrograms = response?.trainingPrograms?.map((el) => ({ label: el.TrainingProgram__r.Name, value: el.Id }));
                this.initResponse = response;
            })
            .catch((error) => showErrors(this, error));
    }

    injectNameFields(form) {
        ['Last Name', 'First Name'].forEach(field => {
            form.sections[0]?.columns[0]?.fields.unshift({
                label: field,
                name: field.replace(' ', ''),
                type: 'string',
                isUiRequired: true
            });
        });
    }

    handleChange(event) {
        this.selectedRole = event.detail.value;
    }

    handleNext() {
        this.roleSelected = true;
    }

    handleReady() {
        this.isFormReady = true;
        let availableOptions = this.availableDivisions.map((el) => ({ label: el, value: el }));
        this.refs.form.setOverrides([
            { name: 'Division__c', options: availableOptions },
            {
                name: 'MailingAddress',
                country: 'US',
                street: this.initResponse?.schoolMailingStreet,
                city: this.initResponse?.schoolMailingCity,
                province: this.initResponse?.schoolMailingState,
                postalCode: this.initResponse?.schoolMailingPostalCode,
                countryDisabled: true
            }
        ]);
    }

    handleSubmit(event) {
        const record = event.detail.record;
        const role = this.selectedRole;

        this.isLoading = true;
        saveRecord({ record, trainingPrograms: this.selectedTrainingPrograms, role })
            .then(() => this.handleSuccess())
            .catch((error) => showErrors(this, error))
            .finally(() => {
                this.isLoading = false;
            });
    }

    get selectedTrainingPrograms() {
        return this._selected.length ? this._selected : [];
    }

    trainingProgramsChange(event) {
        this._selected = event.detail.value;
    }

    submitForm() {
        let valid = this.refs.trainingPrograms.reportValidity();
        if (valid) {
            this.refs.form.submit();
        }
    }

    handleSuccess() {
        const event = new ShowToastEvent({
            title: 'Success!',
            message: this.labels.smCreateAdvisorFormSuccessMessage,
            variant: 'success'
        });
        this.dispatchEvent(event);
        this.close('canceled');
    }
}
