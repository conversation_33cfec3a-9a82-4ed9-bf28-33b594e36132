import { LightningElement, api } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { showErrors } from 'c/errorHandler';
import getForm from '@salesforce/apex/sm_StudentTrainingProgramFormCtrl.getForm';
import saveRecord from '@salesforce/apex/sm_StudentTrainingProgramFormCtrl.saveRecord';
import init from '@salesforce/apex/sm_StudentTrainingProgramFormCtrl.init';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import labels from './labels';

export default class extends NavigationMixin(LightningElement) {
    @api postSavePageName;
    advisorId;
    labels = labels;
    isLoading = true;
    overrides = [
        { name: 'Phone', label: this.labels.smCellPhoneFieldLabel },
        { name: 'EmploymentDescription__c', disabled: true },
        { name: 'Email', label: 'Personal Email' },
        {
            name: 'Birthdate',
            max: new Date(new Date().setFullYear(new Date().getFullYear() - 13)).toISOString() // Prevent students under the age of 13 from being registered
        },
        { name: 'Name', fieldsToDisplay: ['firstName', 'salutation', 'lastName', 'middleName'] },
        { name: 'sm_EmploymentStatus__c', label: this.labels.smEmploymentStatusFieldLabel },
        { name: 'sm_EmploymentDescription__c', label: 'Where do you work?', disabled: true },
        { name: 'MailingAddress', required: true, country: 'US', countryDisabled: true, addressLabel: 'Home Address', countryLabel: 'Country', postalCodeLabel: 'Zip/Postal Code', streetLabel: 'Street', cityLabel: 'City', provinceLabel: 'State' },
        { name: 'Division__c', fieldLevelHelp: 'High School student should register as High School even if it is a dual credit class.' }
    ];
    trainingPrograms = [];
    availableDivisions = [];
    schoolEmail;
    selected = [];
    style = 'slds-hide';

    connectedCallback() {
        this.advisorId = this.getAdvisorId();
        this.getFormMethod = () => {
            return new Promise((resolve, reject) => {
                getForm()
                    .then(form => {
                        this.injectNameFields(form);
                        resolve(form);
                    })
                    .catch(error => reject(error));
            });
        };
        init({ contactId: this.advisorId })
            .then((response) => {
                this.availableDivisions = response?.availableDivisions;
                this.trainingPrograms = response?.trainingPrograms?.map((el) => ({ label: el.TrainingProgram__r.Name, value: el.Id }));
                this.schoolEmail = response?.schoolEmail;
            })
            .catch((error) => showErrors(this, error));
    }

    injectNameFields(form) {
        ['Last Name', 'First Name'].forEach(field => {
            form.sections[0]?.columns[0]?.fields.unshift({
                label: field,
                name: field.replace(' ', ''),
                type: 'string',
                isUiRequired: true
            });
        });
    }

    renderedCallback() {
        let availableOptions = this.availableDivisions.map((el) => ({ label: el, value: el }));
        this.refs.form.setOverrides([
            { name: 'Division__c', options: availableOptions },
            { name: 'School_Email__c', value: this.schoolEmail, required: true },
        ]);
    }

    handleSubmit(event) {
        const contactRecord = event.detail.record;
        const captchaToken = event.detail.captchaToken;

        if (this.selectedTrainingPrograms.length === 0) {
            this.refs.trainingPrograms.reportValidity();
            return;
        }

        this.isLoading = true;

        saveRecord({ contactRecord, advisorId: this.advisorId, programs: this.selectedTrainingPrograms, captchaToken })
            .then(() => {
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Success.',
                        message: 'The record is successfully created.',
                        variant: 'success'
                    })
                );
                this.redirectToPostSavePage();
            })
            .catch((error) => showErrors(this, error))
            .finally(() => {
                this.isLoading = false;
            });
    }

    submitForm() {
        let valid = this.template.querySelector('lightning-dual-listbox').reportValidity();
        if (valid) {
            this.refs.form.submit();
        }
    }

    handleFormReady() {
        this.isLoading = false;
        this.style = '';
    }

    handleFieldChange(ev) {
        if (ev.detail?.name === 'sm_EmploymentStatus__c') {
            let isYes = ev.detail.value === 'Yes';
            this.refs.form.setOverrides({
                name: 'sm_EmploymentDescription__c', value: null, disabled: !isYes
            });
        }
    }

    get selectedTrainingPrograms() {
        return this.selected.length ? this.selected : [];
    }

    trainingProgramsChange(event) {
        this.selected = event.detail.value;
    }

    getAdvisorId() {
        return new URLSearchParams(new URL(window.location.href).search).get('a');
    }

    redirectToPostSavePage() {
        if (!this.postSavePageName) {
            return;
        }
        this[NavigationMixin.Navigate]({
            type: 'comm__namedPage',
            attributes: {
                name: this.postSavePageName
            }
        });
    }
}
