import { LightningElement, track, api } from 'lwc';

import requiredFieldMissingValueLabel from '@salesforce/label/c.sm_GlobalRequiredFieldMissingValueErrorLabel';
import placeholderLabel from '@salesforce/label/c.sm_MultipicklistPlaceholderLabel';

const OPEN_CLASS = 'slds-is-open';

export default class extends LightningElement {
    labels = {
        requiredFieldMissingValueLabel,
        placeholderLabel
    };

    @api options = [];
    @api name = '';
    @api placeholder = this.labels.placeholderLabel;
    @api required = false;

    @track selectedValues = [];
    hasError = false;
    _isOpen = false;
    skipDropdownCloseHandler = false;

    dropdownCloseHandler = () => {
        if (!this.skipDropdownCloseHandler) {
            this.isOpen = false;
        } else {
            this.skipDropdownCloseHandler = false;
        }
    };

    set isOpen(isOpen) {
        this._isOpen = isOpen;
        if (isOpen) {
            document.addEventListener('click', this.dropdownCloseHandler);
        } else {
            document.removeEventListener('click', this.dropdownCloseHandler);
            this.reportValidity();
        }
    }
    get isOpen() {
        return this._isOpen;
    }

    @api
    set value(value) {
        this.selectedValues = value?.split(';') || [];
    }
    get value() {
        return this.selectedValues.length > 0
            ? this.selectedValues.join(';')
            : undefined;
    }

    @api
    reportValidity() {
        this.hasError = !((this.required && this.selectedValues.length > 0) || !this.required);
        return !this.hasError;
    }

    get containerClasses() {
        return 'slds-form-element' + (this.hasError ? ' has-error' : '');
    }

    get comboboxClasses() {
        return (
            'slds-combobox slds-dropdown-trigger slds-dropdown-trigger_click' + (this.isOpen ? ` ${OPEN_CLASS}` : '')
        );
    }

    get opts() {
        return this.options.map((option) => {
            return {
                label: option.label,
                value: option.value,
                checked: this.selectedValues.includes(option.value)
            };
        });
    }

    get displayValue() {
        let displayValue = '';
        if (this.selectedValues.length) {
            const selectedOptions = this.options.filter((option) => this.selectedValues.includes(option.value));
            const selectedLabels = selectedOptions.map((selectedOption) => selectedOption.label);
            displayValue = selectedLabels.join(';');
        } else {
            displayValue = this.placeholder;
        }
        return displayValue;
    }

    handleInputClick() {
        this.isOpen = !this.isOpen;
    }

    handleContainerClick() {
        this.skipDropdownCloseHandler = true;
    }

    handleOptionChange(event) {
        event.stopPropagation();
        this.selectedValues = event.detail.value;
        this.dispatchEvent(
            new CustomEvent('change', {
                detail: {
                    name: this.name,
                    value: this.value
                }
            })
        );
    }
}
