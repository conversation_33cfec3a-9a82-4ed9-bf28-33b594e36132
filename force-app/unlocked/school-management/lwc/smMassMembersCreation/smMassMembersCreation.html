<template>
    <div class="slds-is-relative">
        <lightning-spinner lwc:if={isLoading}></lightning-spinner>
        <div class="slds-grid slds-m-bottom_small">
            <div class="slds-col slds-size_8-of-12">
                <h2 class="slds-text-heading_large slds-m-bottom_xx-small">{title}</h2>
            </div>
            <div class="slds-col slds-size_4-of-12">
                <div class="slds-grid slds-wrap">
                    <div class="slds-col slds-size_1-of-1 slds-text-align_center slds-m-bottom_small">
                        <div class="slds-grid">
                            <div class="slds-col slds-size_1-of-2">
                                <lightning-button
                                    variant="neutral"
                                    label={labels.uploadFileLabel}
                                    icon-name="utility:upload"
                                    icon-position="left"
                                    onclick={handleUploadFileButtonClick}
                                ></lightning-button>
                            </div>
                            <div class="slds-col slds-size_1-of-2">
                                <lightning-button
                                    variant="brand"
                                    label={labels.formatFileLabel}
                                    icon-name="utility:new_window"
                                    icon-position="right"
                                    onclick={handlePredefinedFileButtonClick}
                                ></lightning-button>
                            </div>
                        </div>
                    </div>
                    <div class="slds-col slds-size_1-of-1">
                        <lightning-formatted-rich-text
                            value={labels.uploadingDescriptionLabel}
                        ></lightning-formatted-rich-text>
                    </div>
                </div>
            </div>
        </div>

        <table
            aria-multiselectable="true"
            class="slds-m-bottom_small slds-table slds-table_bordered slds-table_fixed-layout slds-table_resizable-cols"
            role="grid"
        >
            <thead>
                <tr class="slds-line-height_reset">
                    <!--checkbox-->
                    <th class="slds-text-align_right slds-cell_action-mode" scope="col" style="width: 3.25rem">
                        <lightning-input
                            type="checkbox"
                            label=""
                            variant="label-hidden"
                            onchange={handleSelectAllChange}
                        ></lightning-input>
                    </th>
                    <!--columns-->
                    <th for:each={columns} for:item="column" for:index="index" key={column.field} scope="col">
                        <div class="slds-truncate slds-p-around_x-small" title={column.label}>
                            <abbr lwc:if={column.required} class="slds-required" title="required">* </abbr>
                            {column.label}
                        </div>
                    </th>
                    <!--action-->
                    <th class="slds-cell_action-mode" scope="col" style="width: 3.25rem">
                        <div class="slds-truncate slds-assistive-text" title="Actions">Actions</div>
                    </th>
                </tr>
            </thead>
            <tbody>
                <template for:each={members} for:item="member">
                    <c-sm-mass-members-creation-item
                        key={member.index}
                        available-roles={availableRoles}
                        available-school-training-programs={schoolTrainingPrograms}
                        columns={columns}
                        member={member}
                        onmemberdelete={handleMemberDelete}
                        onmemberselectchange={handleMemberSelectChange}
                        onmemberchange={handleMemberChange}
                    ></c-sm-mass-members-creation-item>
                </template>
            </tbody>
        </table>
        <div class="slds-grid">
            <div class="slds-m-right_x-small">
                <button
                    lwc:if={hasSelected}
                    class="slds-button slds-button_brand button-position-initial"
                    onclick={handleBulkDelete}
                >
                    {labels.deleteSelectedLabel}
                </button>
                <button lwc:else class="slds-button slds-button_brand button-position-initial" disabled>
                    {labels.deleteSelectedLabel}
                </button>
            </div>
            <div>
                <button class="slds-button slds-button_brand button-position-initial" onclick={handleAddRow}>
                    {labels.addRowLabel}
                </button>
            </div>
            <div class="slds-container_right">
                <button
                    class="slds-button slds-button_brand button-position-initial"
                    onclick={handleCompleteRegistrationButtonClick}
                >
                    {labels.completeRegistrationLabel}
                </button>
            </div>
        </div>
    </div>
</template>
