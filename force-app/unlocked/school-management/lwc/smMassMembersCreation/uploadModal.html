<template>
    <lightning-modal-header label={title}></lightning-modal-header>
    <lightning-modal-body>
        <div class="slds-is-relative slds-text-align_center">
            <lightning-spinner if:true={isLoading} alternative-text="Loading" size="medium"></lightning-spinner>
            <lightning-input
                type="file"
                label={filename}
                accept=".csv"
                onchange={handleFileUpload}
            ></lightning-input>
        </div>
    </lightning-modal-body>
    <lightning-modal-footer>
        <lightning-button
            variant="brand"
            label="Save"
            class="slds-m-left_x-small"
            onclick={handleSaveButtonClick}
            disabled={isLoading}
        ></lightning-button>
    </lightning-modal-footer>
</template>