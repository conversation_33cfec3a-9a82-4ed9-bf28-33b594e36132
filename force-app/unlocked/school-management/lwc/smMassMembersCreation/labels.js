import firstName<PERSON>abel from '@salesforce/label/c.sm_MassMemberCreationFirstNameLabel';
import lastNameLabel from '@salesforce/label/c.sm_MassMemberCreationLastNameLabel';
import emailLabel from '@salesforce/label/c.sm_MassMemberCreationEmailLabel';
import roleLabel from '@salesforce/label/c.sm_MassMemberCreationRoleLabel';
import trainingProgramsLabel from '@salesforce/label/c.sm_MassMemberCreationTrainingProgramsLabel';
import advisorLabel from '@salesforce/label/c.sm_MassMemberCreationAdvisorLabel';
import graduationYearLabel from '@salesforce/label/c.sm_MassMemberCreationGraduationYearLabel';
import phoneLabel from '@salesforce/label/c.sm_MassMemberCreationPhoneLabel';
import dateOfBirthLabel from '@salesforce/label/c.sm_MassMemberCreationDateOfBirthLabel';
import parentContactNameLabel from '@salesforce/label/c.sm_MassMemberCreationParentContactNameLabel';
import parentContactEmailLabel from '@salesforce/label/c.sm_MassMemberCreationParentContactEmailLabel';
import ethnicityLabel from '@salesforce/label/c.sm_MassMemberCreationEthnicityLabel';
import genderLabel from '@salesforce/label/c.sm_MassMemberCreationGenderLabel';
import addressLabel from '@salesforce/label/c.sm_MassMemberCreationAddressLabel';
import deleteSelectedLabel from '@salesforce/label/c.sm_MassMemberCreationDeleteSelectedLabel';
import addRowLabel from '@salesforce/label/c.sm_MassMemberCreationAddRowLabel';
import completeRegistrationLabel from '@salesforce/label/c.sm_MassMemberCreationCompleteRegistrationLabel';
import divisionLabel from '@salesforce/label/c.sm_MassMemberCreationDivisionLabel';
import successLabel from '@salesforce/label/c.sm_GlobalSuccessLabel';
import errorLabel from '@salesforce/label/c.sm_GlobalErrorLabel';
import successfulRegistrationLabel from '@salesforce/label/c.sm_MassMemberCreationSuccessfulRegistrationLabel';
import uploadingSchoolMembersLabel from '@salesforce/label/c.sm_UploadingSchoolMembersLabel';
import formatFileLabel from '@salesforce/label/c.sm_FormatFileButtonLabel';
import uploadFileLabel from '@salesforce/label/c.sm_UploadFileButtonLabel';
import uploadingDescriptionLabel from '@salesforce/label/c.sm_UploadingDescriptionLabel';
import duplicateErrorMessageLabel from '@salesforce/label/c.sm_MassMemberCreationDuplicateErrorMessageLabel';
import duplicateRowErrorMessageLabel from '@salesforce/label/c.sm_MassMemberCreationDuplicateRowErrorMessageLabel';
import csvFileReadError from '@salesforce/label/c.sm_MassMemberCreationFailedCsvFileRead';

export default {
    firstNameLabel,
    lastNameLabel,
    emailLabel,
    roleLabel,
    trainingProgramsLabel,
    advisorLabel,
    graduationYearLabel,
    phoneLabel,
    dateOfBirthLabel,
    parentContactNameLabel,
    parentContactEmailLabel,
    ethnicityLabel,
    genderLabel,
    addressLabel,
    deleteSelectedLabel,
    addRowLabel,
    completeRegistrationLabel,
    divisionLabel,
    successLabel,
    errorLabel,
    successfulRegistrationLabel,
    uploadingSchoolMembersLabel,
    formatFileLabel,
    uploadFileLabel,
    uploadingDescriptionLabel,
    duplicateErrorMessageLabel,
    duplicateRowErrorMessageLabel,
    csvFileReadError
};
