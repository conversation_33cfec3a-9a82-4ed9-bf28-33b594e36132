import { LightningElement, api } from 'lwc';
import labels from './labels';

const OPEN_CLASS = 'slds-is-open';

export default class extends LightningElement {
    @api name = '';
    @api required = false;
    @api params = {};

    labels = labels;

    hasError = false;
    _isOpen = false;
    _data = {};

    dropdownCloseHandler = () => {
        this.isOpen = false;
    };

    set isOpen(isOpen) {
        this._isOpen = isOpen;
        if (isOpen) {
            document.addEventListener('click', this.dropdownCloseHandler);
        } else {
            document.removeEventListener('click', this.dropdownCloseHandler);
            this.reportValidity();
        }
    }

    @api
    set fieldData(data) {
        this._data = data ? JSON.parse(JSON.stringify(data)) : {};
    }
    get fieldData() {
        return this._data;
    }

    get isAddress() {
        return this.name === 'address';
    }

    get isOpen() {
        return this._isOpen;
    }

    get input() {
        return this.template.querySelector('.data-input');
    }

    @api
    reportValidity() {
        return this.input.reportValidity();
    }

    get containerClasses() {
        return 'slds-form-element' + (this.hasError ? ' has-error' : '');
    }

    get comboboxClasses() {
        return (
            'slds-combobox slds-dropdown-trigger slds-dropdown-trigger_click' + (this.isOpen ? ` ${OPEN_CLASS}` : '')
        );
    }

    get displayValue() {
        return Object.values(this.fieldData)
            .filter((dataItem) => !!dataItem)
            .join(';');
    }

    handleInputClick() {
        this.isOpen = !this.isOpen;
    }

    handleContainerClick(event) {
        event.stopPropagation();
    }

    handleChange(event) {
        event.stopPropagation();
        const data = event.detail;
        delete data.validity;
        this._data = data;
        this.dispatchEvent(
            new CustomEvent('change', {
                detail: {
                    name: this.name,
                    value: this.fieldData
                }
            })
        );
    }
}
