<template>
    <lightning-modal-header label={labels.smCreateMiddleSchoolMemberModalHeader}></lightning-modal-header>
    <lightning-modal-body>
        <div class="slds-is-relative">
            <lightning-spinner if:true={isLoading} alternative-text="Loading" size="medium"></lightning-spinner>
            <aclab-form
                get-form-method={getFormMethod}
                onformsubmit={handleSubmit}
                hide-submit-button="true"
                onformready={handleReady}
            ></aclab-form>

            <lightning-dual-listbox
                lwc:if={isFormReady}
                name="trainingPrograms"
                label="Training programs for school member"
                source-label="Available"
                selected-label="Selected"
                field-level-help="Select training programs for school member"
                required="true"
                options={trainingPrograms}
                onchange={trainingProgramsChange}
            ></lightning-dual-listbox>

            <div lwc:if={isFormReady} class="slds-p-top_medium slds-float_right">
                <lightning-button onclick={submitForm} label="Submit" variant="brand"></lightning-button>
            </div>
        </div>
    </lightning-modal-body>
</template>
