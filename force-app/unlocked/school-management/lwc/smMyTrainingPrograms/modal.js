import { api } from "lwc";
import LightningModal from 'lightning/modal';
import { showErrors } from 'c/errorHandler';
import getOptions from "@salesforce/apex/sm_MyTrainingProgramsCtrl.getOptions"
import save from "@salesforce/apex/sm_MyTrainingProgramsCtrl.save"
import { ShowToastEvent } from "lightning/platformShowToastEvent";

export default class extends LightningModal {
    @api recordId;
    @api description;
    options = [];
    selectedOptions = [];
    isLoading = false;

    connectedCallback() {
        getOptions({contactId: this.recordId})
            .then(response => {
                console.log(JSON.stringify(response))

                this.options = response.options;
                this.selectedOptions = response.selected;
            })
            .catch(error => { showErrors(this, error) });
    }

    save() {
        this.isLoading = true;
        save({
            programIds: this.selectedOptions,
            contactId: this.recordId
        })
            .then(() => {
                this.dispatchEvent(new ShowToastEvent({
                    title: 'Success.',
                    message: 'Updated.',
                    variant: 'success'
                }));
                this.close();
            })
            .catch(error => { showErrors(this, error); })
            .finally(() => { this.isLoading = false; })
    }

    onChange(ev) {
        this.selectedOptions = ev.detail.value;
    }
}
