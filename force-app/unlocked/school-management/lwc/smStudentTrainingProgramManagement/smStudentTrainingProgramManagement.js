import { LightningElement, track } from 'lwc';
import { showErrors } from 'c/errorHandler';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import LightningConfirm from 'lightning/confirm';
import getStudents from '@salesforce/apex/sm_StudentTrainingProgramManagementCtrl.getStudents';
import updateStudent from '@salesforce/apex/sm_StudentTrainingProgramManagementCtrl.updateStudent';
import getContactInfo from '@salesforce/apex/sm_StudentTrainingProgramManagementCtrl.getContactInfo';
import labels from './labels';

export default class extends LightningElement {
    @track records;
    @track headers = [
        { Id: 'contactName', label: this.labels.smStudentTrainingNameColumn },
        { Id: 'status', label: this.labels.smStudentTrainingStatusColumn },
        { Id: 'details', label: this.labels.smStudentTrainingDetailsColumn },
        {
            Id: 'actions',
            label: this.labels.smStudentTrainingActionColumn,
            class: 'slds-align-middle slds-text-align_right'
        }
    ];

    isLoading = true;
    isDataLoad = false;

    isQRReady = false;
    qrCodeImage;
    qrCodeLink;

    get labels() {
        return labels;
    }

    get recordsExist() {
        return this.records.length > 0;
    }

    connectedCallback() {
        this.loadData();
    }

    loadData() {
        getContactInfo({})
            .then((result) => {
                this.qrCodeImage = result.sm_AdvisorsRegistrationFormQRCode__c;
                this.qrCodeLink = result.sm_AdvisorsRegistrationFormLink__c;
                this.isQRReady = true;
            })
            .catch((error) => {
                showErrors(this, error);
            });

        getStudents({})
            .then((response) => {
                console.log('res0', response, JSON.stringify(response));
                this.records = response.map((item) => ({
                    record: item,
                    showMore: false
                }));
                this.isDataLoad = true;
            })
            .catch((error) => {
                showErrors(this, error);
            })
            .finally(() => {
                this.isLoading = false;
            });
    }

    handleShowMore(event) {
        const el = event.currentTarget;
        const recordId = el.dataset.id;
        el.iconName = el.iconName === 'utility:chevronup' ? 'utility:chevrondown' : 'utility:chevronup';

        this.records = this.records.map((item) =>
            item.record.Id === recordId ? { ...item, showMore: !item.showMore } : item
        );
    }

    async rejectRequest(event) {
        const recordId = event.currentTarget.dataset.id;
        const isConfirmed = await LightningConfirm.open({
            label: this.labels.smStudentTrainingRejectConfirmationMessage,
            theme: 'error'
        });
        if (isConfirmed) {
            this.updateStudent(recordId, false);
        }
    }

    async approveRequest(event) {
        const recordId = event.currentTarget.dataset.id;
        const isConfirmed = await LightningConfirm.open({
            label: this.labels.smStudentTrainingApproveConfirmationMessage,
            theme: 'success'
        });
        if (isConfirmed) {
            this.updateStudent(recordId, true);
        }
    }

    updateStudent(studentId, isApproved) {
        this.isLoading = true;
        updateStudent({ studentId: studentId, isApproved: isApproved })
            .then(() => {
                this.showToast(isApproved);
            })
            .then(() => {
                this.loadData();
            })
            .catch((error) => {
                showErrors(this, error);
                this.isLoading = false;
            });
    }

    showToast(isApproved) {
        const message = isApproved
            ? this.labels.smStudentTrainingApprovedMessage
            : this.labels.smStudentTrainingRejectedMessage;
        const evt = new ShowToastEvent({
            title: 'Success',
            message: message,
            variant: 'success'
        });
        this.dispatchEvent(evt);
    }

    get qrCodeUrl() {
        return (this.qrCodeImage?.match(/<img[^>]+src="([^">]+)"/) || [])[1]?.replace(/&amp;/g, '&') || undefined;
    }
}
