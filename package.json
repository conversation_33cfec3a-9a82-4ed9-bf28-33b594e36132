{"name": "salesforce-app", "private": true, "version": "1.0.0", "description": "Salesforce App", "scripts": {"lint": "eslint **/{aura,lwc}/**", "test": "npm run test:unit", "test:unit": "sfdx-lwc-jest", "test:unit:watch": "sfdx-lwc-jest --watch", "test:unit:debug": "sfdx-lwc-jest --debug", "test:unit:coverage": "sfdx-lwc-jest --coverage", "prettier": "prettier --write \"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}\"", "prettier:verify": "prettier --list-different \"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}\"", "postinstall": "husky install", "precommit": "lint-staged"}, "devDependencies": {"@lwc/eslint-plugin-lwc": "^1.1.2", "@prettier/plugin-xml": "^3.x", "@salesforce/eslint-config-lwc": "^3.2.3", "@salesforce/eslint-plugin-aura": "^2.0.0", "@salesforce/eslint-plugin-lightning": "^1.0.0", "@salesforce/sfdx-lwc-jest": "^1.1.0", "eslint": "^8.11.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jest": "^26.1.2", "husky": "^7.0.4", "lint-staged": "^12.3.7", "prettier": "^3.x", "prettier-plugin-apex": "^2.x"}, "lint-staged": {"**/*.{cls,cmp,component,css,html,js,json,md,page,trigger,xml,yaml,yml}": ["prettier --write"], "**/{aura,lwc}/**": ["eslint"]}}